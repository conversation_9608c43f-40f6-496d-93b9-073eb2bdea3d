/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "TerrainModelUtils.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
void TerrainModelUtils::Initialize(Napi::Env env)
{
    // Initialize the terrain model library
    // Set up any global state, logging, etc.
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::CreateDTM(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    try {
        DTMPtr dtmPtr;
        
        if (info.Length() == 0) {
            // Default constructor
            dtmPtr = DTM::Create();
        } else if (info.Length() == 2 && info[0].IsNumber() && info[1].IsNumber()) {
            // Constructor with initial and increment point counts
            size_t iniPoint = static_cast<size_t>(info[0].As<Napi::Number>().Int64Value());
            size_t incPoint = static_cast<size_t>(info[1].As<Napi::Number>().Int64Value());
            dtmPtr = DTM::Create(iniPoint, incPoint);
        } else {
            Napi::TypeError::New(env, "Invalid arguments for createDTM").ThrowAsJavaScriptException();
            return env.Null();
        }

        if (!dtmPtr) {
            Napi::Error::New(env, "Failed to create DTM").ThrowAsJavaScriptException();
            return env.Null();
        }

        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->SetHandle(dtmPtr);
        
        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error creating DTM: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::LoadDTMFromFile(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string filename").ThrowAsJavaScriptException();
        return env.Null();
    }

    std::string fileName = info[0].As<Napi::String>().Utf8Value();
    
    try {
        DTMPtr dtmPtr = DTM::CreateFromFile(fileName);
        
        if (!dtmPtr) {
            Napi::Error::New(env, "Failed to load DTM from file: " + fileName).ThrowAsJavaScriptException();
            return env.Null();
        }

        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->SetHandle(dtmPtr);
        
        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error loading DTM from file: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::SetLogLevel(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string log level").ThrowAsJavaScriptException();
        return env.Null();
    }

    std::string logLevel = info[0].As<Napi::String>().Utf8Value();
    
    try {
        // Set log level in the terrain model library
        // TODO: Implement actual log level setting
        
        return env.Undefined();
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error setting log level: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Point3D TerrainModelUtils::ConvertJSObjectToPoint3D(Napi::Object jsObject)
{
    Point3D point;
    
    if (jsObject.Has("x") && jsObject.Get("x").IsNumber()) {
        point.x = jsObject.Get("x").As<Napi::Number>().DoubleValue();
    }
    
    if (jsObject.Has("y") && jsObject.Get("y").IsNumber()) {
        point.y = jsObject.Get("y").As<Napi::Number>().DoubleValue();
    }
    
    if (jsObject.Has("z") && jsObject.Get("z").IsNumber()) {
        point.z = jsObject.Get("z").As<Napi::Number>().DoubleValue();
    }
    
    return point;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Object TerrainModelUtils::ConvertPoint3DToJSObject(Napi::Env env, const Point3D& point)
{
    Napi::Object jsObject = Napi::Object::New(env);
    jsObject.Set("x", Napi::Number::New(env, point.x));
    jsObject.Set("y", Napi::Number::New(env, point.y));
    jsObject.Set("z", Napi::Number::New(env, point.z));
    return jsObject;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
std::vector<Point3D> TerrainModelUtils::ConvertJSArrayToPoint3DVector(Napi::Array jsArray)
{
    std::vector<Point3D> points;
    points.reserve(jsArray.Length());
    
    for (uint32_t i = 0; i < jsArray.Length(); ++i) {
        Napi::Value element = jsArray.Get(i);
        if (element.IsObject()) {
            points.push_back(ConvertJSObjectToPoint3D(element.As<Napi::Object>()));
        }
    }
    
    return points;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Array TerrainModelUtils::ConvertPoint3DVectorToJSArray(Napi::Env env, const std::vector<Point3D>& points)
{
    Napi::Array jsArray = Napi::Array::New(env, points.size());
    
    for (size_t i = 0; i < points.size(); ++i) {
        jsArray.Set(i, ConvertPoint3DToJSObject(env, points[i]));
    }
    
    return jsArray;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Range3D TerrainModelUtils::ConvertJSObjectToRange3D(Napi::Object jsObject)
{
    Range3D range;
    
    if (jsObject.Has("low") && jsObject.Get("low").IsObject()) {
        range.low = ConvertJSObjectToPoint3D(jsObject.Get("low").As<Napi::Object>());
    }
    
    if (jsObject.Has("high") && jsObject.Get("high").IsObject()) {
        range.high = ConvertJSObjectToPoint3D(jsObject.Get("high").As<Napi::Object>());
    }
    
    return range;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Object TerrainModelUtils::ConvertRange3DToJSObject(Napi::Env env, const Range3D& range)
{
    Napi::Object jsObject = Napi::Object::New(env);
    jsObject.Set("low", ConvertPoint3DToJSObject(env, range.low));
    jsObject.Set("high", ConvertPoint3DToJSObject(env, range.high));
    return jsObject;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::CreateDTMFromPoints(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsArray()) {
        Napi::TypeError::New(env, "Expected array of points").ThrowAsJavaScriptException();
        return env.Null();
    }

    try {
        std::vector<Point3D> points = ConvertJSArrayToPoint3DVector(info[0].As<Napi::Array>());
        
        if (points.empty()) {
            Napi::Error::New(env, "Cannot create DTM from empty point array").ThrowAsJavaScriptException();
            return env.Null();
        }

        DTMPtr dtmPtr = DTM::CreateFromPoints(points);
        
        if (!dtmPtr) {
            Napi::Error::New(env, "Failed to create DTM from points").ThrowAsJavaScriptException();
            return env.Null();
        }

        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->SetHandle(dtmPtr);
        
        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error creating DTM from points: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::GetVersion(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    return Napi::String::New(env, "1.0.0");
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value TerrainModelUtils::InitializeLogging(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    std::string logFile;
    if (info.Length() > 0 && info[0].IsString()) {
        logFile = info[0].As<Napi::String>().Utf8Value();
    }
    
    try {
        // Initialize logging system
        // TODO: Implement actual logging initialization
        
        return Napi::Boolean::New(env, true);
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error initializing logging: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

} // namespace TerrainModelNodeAddon
