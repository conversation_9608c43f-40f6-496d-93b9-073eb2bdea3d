/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const path = require('path');

// Load the native addon
let nativeAddon;
try {
  // Try to load from build directory
  nativeAddon = require('./build/Release/TerrainModelNodeAddon.node');
} catch (err) {
  try {
    // Fallback to debug build
    nativeAddon = require('./build/Debug/TerrainModelNodeAddon.node');
  } catch (debugErr) {
    throw new Error(`Failed to load TerrainModelNodeAddon: ${err.message}\nDebug build error: ${debugErr.message}`);
  }
}

// Export all classes and functions from the native addon
module.exports = {
  // Core DTM Classes
  DTM: nativeAddon.DTM,
  DTMFeature: nativeAddon.DTMFeature,
  DTMFeatureInfo: nativeAddon.DTMFeatureInfo,
  DTMDrapedPoint: nativeAddon.DTMDrapedPoint,
  DTMFeatureEnumerator: nativeAddon.DTMFeatureEnumerator,
  DTMMesh: nativeAddon.DTMMesh,
  DTMMeshEnumerator: nativeAddon.DTMMeshEnumerator,

  // Advanced DTM Classes
  DTMTinEditor: nativeAddon.DTMTinEditor,
  DTMDrapedLinearElement: nativeAddon.DTMDrapedLinearElement,
  DTMDrapedLinearElementPoint: nativeAddon.DTMDrapedLinearElementPoint,

  // Analysis Classes
  WaterAnalysis: nativeAddon.WaterAnalysis,
  WaterAnalysisResult: nativeAddon.WaterAnalysisResult,
  WaterAnalysisResultItem: nativeAddon.WaterAnalysisResultItem,
  WaterAnalysisResultPoint: nativeAddon.WaterAnalysisResultPoint,
  WaterAnalysisResultStream: nativeAddon.WaterAnalysisResultStream,
  WaterAnalysisResultPond: nativeAddon.WaterAnalysisResultPond,

  // Design Classes
  DTMPond: nativeAddon.DTMPond,
  DTMSideSlopeInput: nativeAddon.DTMSideSlopeInput,
  DTMSideSlopeInputPoint: nativeAddon.DTMSideSlopeInputPoint,
  DTMSlopeTable: nativeAddon.DTMSlopeTable,

  // Caching and Performance Classes
  DTMCaching: nativeAddon.DTMCaching,

  // Enums
  DTMFeatureType: {
    Point: 0,
    BreakLine: 1,
    VoidLine: 2,
    HoleLine: 3,
    IslandLine: 4,
    ContourLine: 5
  },

  DTMDrapedPointCode: {
    External: 0,
    Triangle: 1,
    Void: 2,
    PointOrSide: 3
  },

  DTMEdgeOption: {
    NoRemove: 1,
    RemoveSliver: 2,
    RemoveMaxSide: 3
  },

  DTMSideSlopeDirection: {
    Left: 0,
    Right: 1,
    Both: 2
  },

  DTMSideSlopeOption: {
    ToSurface: 0,
    ToElevation: 1,
    ToHorizontalDistance: 2,
    ToDeltaElevation: 3
  },

  DTMSideSlopeCutFillOption: {
    Cut: 0,
    Fill: 1,
    Both: 2,
    Auto: 3
  },

  DTMSideSlopeCornerOption: {
    None: 0,
    Round: 1,
    Square: 2,
    Chamfer: 3
  },

  DTMSideSlopeStrokeCornerOption: {
    None: 0,
    Extend: 1,
    Trim: 2
  },

  DTMSideSlopeRadialOption: {
    ToSurface: 0,
    ToElevation: 1,
    ToDistance: 2,
    ToDelta: 3
  },

  WaterAnalysisResultType: {
    Point: 0,
    Stream: 1,
    Pond: 2
  },

  ZeroSlopeTraceOption: {
    None: 0,
    TraceLastAngle: 1,
    Pond: 2
  },

  VisibilityType: {
    Invisible: 0,
    Visible: 1,
    PartiallyVisible: 2
  },

  DTMFenceType: {
    Shape: 0,
    Block: 1,
    Circle: 2
  },

  DTMFenceOption: {
    Internal: 0,
    External: 1,
    Both: 2
  },

  // Module-level functions
  createDTM: nativeAddon.createDTM,
  loadDTMFromFile: nativeAddon.loadDTMFromFile,
  createDTMFromPoints: nativeAddon.createDTMFromPoints,
  createDTMFromMesh: nativeAddon.createDTMFromMesh,
  mergeDTMs: nativeAddon.mergeDTMs,
  compareDTMs: nativeAddon.compareDTMs,
  setLogLevel: nativeAddon.setLogLevel,
  getVersion: nativeAddon.getVersion,
  initializeLogging: nativeAddon.initializeLogging,

  // Module properties
  version: nativeAddon.version,

  // Utility functions
  createPoint3D: (x, y, z) => ({ x, y, z }),
  createRange3D: (lowX, lowY, lowZ, highX, highY, highZ) => ({
    low: { x: lowX, y: lowY, z: lowZ },
    high: { x: highX, y: highY, z: highZ }
  }),

  // Validation helpers
  isValidPoint3D: (point) => {
    return point && 
           typeof point.x === 'number' && 
           typeof point.y === 'number' && 
           typeof point.z === 'number' &&
           !isNaN(point.x) && !isNaN(point.y) && !isNaN(point.z);
  },

  isValidRange3D: (range) => {
    return range && 
           module.exports.isValidPoint3D(range.low) && 
           module.exports.isValidPoint3D(range.high);
  },

  // Array conversion helpers
  convertPointsArray: (points) => {
    if (!Array.isArray(points)) {
      throw new TypeError('Expected array of points');
    }
    return points.map((point, index) => {
      if (!module.exports.isValidPoint3D(point)) {
        throw new TypeError(`Invalid point at index ${index}`);
      }
      return { x: point.x, y: point.y, z: point.z };
    });
  },

  // Error handling helpers
  TerrainModelError: class TerrainModelError extends Error {
    constructor(message, code) {
      super(message);
      this.name = 'TerrainModelError';
      this.code = code;
    }
  },

  // Constants
  Constants: {
    DEFAULT_POINT_TOLERANCE: 1e-6,
    DEFAULT_LINE_TOLERANCE: 1e-6,
    DEFAULT_MAX_SIDE: 1000.0,
    MIN_TRIANGULATION_POINTS: 3,
    MAX_USER_TAG: 2147483647,
    MIN_USER_TAG: -2147483648
  }
};

// Add convenience methods to DTM prototype if available
if (nativeAddon.DTM) {
  // Add async versions of some methods
  nativeAddon.DTM.prototype.triangulateAsync = function(callback) {
    if (typeof callback !== 'function') {
      return new Promise((resolve, reject) => {
        setImmediate(() => {
          try {
            const result = this.triangulate();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      });
    } else {
      setImmediate(() => {
        try {
          const result = this.triangulate();
          callback(null, result);
        } catch (error) {
          callback(error);
        }
      });
    }
  };

  nativeAddon.DTM.prototype.saveAsync = function(fileName, callback) {
    if (typeof callback !== 'function') {
      return new Promise((resolve, reject) => {
        setImmediate(() => {
          try {
            this.save(fileName);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });
    } else {
      setImmediate(() => {
        try {
          this.save(fileName);
          callback(null);
        } catch (error) {
          callback(error);
        }
      });
    }
  };
}
