/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "FileIO.h"
#include "GeometryUtils.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <filesystem>

namespace TerrainModel {

//=======================================================================================
// FileIOResult implementation
//=======================================================================================
FileIOResult FileIOResult::Success(const std::string& message) {
    FileIOResult result;
    result.success = true;
    result.message = message;
    return result;
}

FileIOResult FileIOResult::Failure(const std::string& message) {
    FileIOResult result;
    result.success = false;
    result.message = message;
    return result;
}

//=======================================================================================
// FileIOManager implementation
//=======================================================================================
FileIOManager& FileIOManager::Instance() {
    static FileIOManager instance;
    return instance;
}

FileIOManager::FileIOManager() {
    RegisterDefaultReaders();
    RegisterDefaultWriters();
}

FileIOManager::~FileIOManager() = default;

void FileIOManager::RegisterReader(const std::string& extension, std::unique_ptr<DTMReader> reader) {
    std::string lowerExt = ToLowerCase(extension);
    m_readers[lowerExt] = std::move(reader);
}

void FileIOManager::RegisterWriter(const std::string& extension, std::unique_ptr<DTMWriter> writer) {
    std::string lowerExt = ToLowerCase(extension);
    m_writers[lowerExt] = std::move(writer);
}

FileIOResult FileIOManager::LoadDTM(const std::string& fileName, DTM& dtm) {
    try {
        // Check if file exists
        if (!std::filesystem::exists(fileName)) {
            return FileIOResult::Failure("File does not exist: " + fileName);
        }
        
        // Get file extension
        std::string extension = GetFileExtension(fileName);
        std::string lowerExt = ToLowerCase(extension);
        
        // Find appropriate reader
        auto readerIt = m_readers.find(lowerExt);
        if (readerIt == m_readers.end()) {
            return FileIOResult::Failure("No reader available for file type: " + extension);
        }
        
        // Load the DTM
        FileIOResult result = readerIt->second->Read(fileName, dtm);
        
        if (result.success) {
            result.message = "DTM loaded successfully from " + fileName;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error loading DTM: " + std::string(e.what()));
    }
}

FileIOResult FileIOManager::SaveDTM(const std::string& fileName, const DTM& dtm) {
    try {
        // Get file extension
        std::string extension = GetFileExtension(fileName);
        std::string lowerExt = ToLowerCase(extension);
        
        // Find appropriate writer
        auto writerIt = m_writers.find(lowerExt);
        if (writerIt == m_writers.end()) {
            return FileIOResult::Failure("No writer available for file type: " + extension);
        }
        
        // Create directory if it doesn't exist
        std::filesystem::path filePath(fileName);
        if (filePath.has_parent_path()) {
            std::filesystem::create_directories(filePath.parent_path());
        }
        
        // Save the DTM
        FileIOResult result = writerIt->second->Write(fileName, dtm);
        
        if (result.success) {
            result.message = "DTM saved successfully to " + fileName;
        }
        
        return result;
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error saving DTM: " + std::string(e.what()));
    }
}

std::vector<std::string> FileIOManager::GetSupportedReadFormats() const {
    std::vector<std::string> formats;
    for (const auto& pair : m_readers) {
        formats.push_back(pair.first);
    }
    return formats;
}

std::vector<std::string> FileIOManager::GetSupportedWriteFormats() const {
    std::vector<std::string> formats;
    for (const auto& pair : m_writers) {
        formats.push_back(pair.first);
    }
    return formats;
}

void FileIOManager::RegisterDefaultReaders() {
    // Register built-in readers
    RegisterReader(".tin", std::make_unique<TINReader>());
    RegisterReader(".xyz", std::make_unique<XYZReader>());
    RegisterReader(".csv", std::make_unique<CSVReader>());
    RegisterReader(".json", std::make_unique<JSONReader>());
}

void FileIOManager::RegisterDefaultWriters() {
    // Register built-in writers
    RegisterWriter(".tin", std::make_unique<TINWriter>());
    RegisterWriter(".xyz", std::make_unique<XYZWriter>());
    RegisterWriter(".csv", std::make_unique<CSVWriter>());
    RegisterWriter(".json", std::make_unique<JSONWriter>());
}

std::string FileIOManager::GetFileExtension(const std::string& fileName) const {
    std::filesystem::path filePath(fileName);
    return filePath.extension().string();
}

std::string FileIOManager::ToLowerCase(const std::string& str) const {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

//=======================================================================================
// TINReader implementation
//=======================================================================================
FileIOResult TINReader::Read(const std::string& fileName, DTM& dtm) {
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot open file: " + fileName);
        }
        
        std::string line;
        int lineNumber = 0;
        
        // Read header
        if (!std::getline(file, line)) {
            return FileIOResult::Failure("Empty file or cannot read header");
        }
        lineNumber++;
        
        // Parse header to get counts
        std::istringstream headerStream(line);
        int vertexCount, triangleCount;
        if (!(headerStream >> vertexCount >> triangleCount)) {
            return FileIOResult::Failure("Invalid header format at line " + std::to_string(lineNumber));
        }
        
        // Read vertices
        std::vector<Point3D> vertices;
        vertices.reserve(vertexCount);
        
        for (int i = 0; i < vertexCount; ++i) {
            if (!std::getline(file, line)) {
                return FileIOResult::Failure("Unexpected end of file while reading vertices");
            }
            lineNumber++;
            
            std::istringstream vertexStream(line);
            double x, y, z;
            if (!(vertexStream >> x >> y >> z)) {
                return FileIOResult::Failure("Invalid vertex format at line " + std::to_string(lineNumber));
            }
            
            vertices.emplace_back(x, y, z);
        }
        
        // Add vertices to DTM
        for (const auto& vertex : vertices) {
            dtm.AddPointFeature(vertex);
        }
        
        // Read triangles (if present)
        std::vector<std::array<int, 3>> triangles;
        triangles.reserve(triangleCount);
        
        for (int i = 0; i < triangleCount; ++i) {
            if (!std::getline(file, line)) {
                return FileIOResult::Failure("Unexpected end of file while reading triangles");
            }
            lineNumber++;
            
            std::istringstream triangleStream(line);
            int v1, v2, v3;
            if (!(triangleStream >> v1 >> v2 >> v3)) {
                return FileIOResult::Failure("Invalid triangle format at line " + std::to_string(lineNumber));
            }
            
            triangles.push_back({v1, v2, v3});
        }
        
        // Triangulate if we have triangle data
        if (!triangles.empty()) {
            // TODO: Use triangle connectivity information
            // For now, just triangulate the points
            auto result = dtm.Triangulate();
            if (!result.isSuccess()) {
                return FileIOResult::Failure("Triangulation failed: " + result.message);
            }
        }
        
        file.close();
        
        return FileIOResult::Success("TIN file loaded successfully");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error reading TIN file: " + std::string(e.what()));
    }
}

//=======================================================================================
// TINWriter implementation
//=======================================================================================
FileIOResult TINWriter::Write(const std::string& fileName, const DTM& dtm) {
    try {
        std::ofstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot create file: " + fileName);
        }
        
        const auto& vertices = dtm.GetVertices();
        const auto& triangles = dtm.GetTriangles();
        
        // Write header
        file << vertices.size() << " " << triangles.size() << std::endl;
        
        // Write vertices
        file << std::fixed << std::setprecision(6);
        for (const auto& pair : vertices) {
            const auto& vertex = pair.second;
            if (vertex->IsActive()) {
                Point3D pos = vertex->GetPosition();
                file << pos.x << " " << pos.y << " " << pos.z << std::endl;
            }
        }
        
        // Write triangles
        for (const auto& pair : triangles) {
            const auto& triangle = pair.second;
            if (triangle->IsActive()) {
                // Note: This assumes vertex IDs are sequential starting from 1
                // A more robust implementation would maintain a vertex ID mapping
                file << triangle->GetVertex1Id() << " " 
                     << triangle->GetVertex2Id() << " " 
                     << triangle->GetVertex3Id() << std::endl;
            }
        }
        
        file.close();
        
        return FileIOResult::Success("TIN file saved successfully");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error writing TIN file: " + std::string(e.what()));
    }
}

//=======================================================================================
// XYZReader implementation
//=======================================================================================
FileIOResult XYZReader::Read(const std::string& fileName, DTM& dtm) {
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot open file: " + fileName);
        }
        
        std::string line;
        int lineNumber = 0;
        int pointsRead = 0;
        
        while (std::getline(file, line)) {
            lineNumber++;
            
            // Skip empty lines and comments
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            std::istringstream lineStream(line);
            double x, y, z;
            
            if (lineStream >> x >> y >> z) {
                dtm.AddPointFeature(Point3D(x, y, z));
                pointsRead++;
            } else {
                return FileIOResult::Failure("Invalid point format at line " + std::to_string(lineNumber));
            }
        }
        
        file.close();
        
        if (pointsRead == 0) {
            return FileIOResult::Failure("No valid points found in file");
        }
        
        return FileIOResult::Success("XYZ file loaded successfully, " + std::to_string(pointsRead) + " points read");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error reading XYZ file: " + std::string(e.what()));
    }
}

//=======================================================================================
// XYZWriter implementation
//=======================================================================================
FileIOResult XYZWriter::Write(const std::string& fileName, const DTM& dtm) {
    try {
        std::ofstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot create file: " + fileName);
        }
        
        const auto& vertices = dtm.GetVertices();
        
        // Write header comment
        file << "# X Y Z" << std::endl;
        file << "# Generated by TerrainModel" << std::endl;
        
        // Write vertices
        file << std::fixed << std::setprecision(6);
        int pointsWritten = 0;
        
        for (const auto& pair : vertices) {
            const auto& vertex = pair.second;
            if (vertex->IsActive()) {
                Point3D pos = vertex->GetPosition();
                file << pos.x << " " << pos.y << " " << pos.z << std::endl;
                pointsWritten++;
            }
        }
        
        file.close();
        
        return FileIOResult::Success("XYZ file saved successfully, " + std::to_string(pointsWritten) + " points written");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error writing XYZ file: " + std::string(e.what()));
    }
}

//=======================================================================================
// CSVReader implementation
//=======================================================================================
FileIOResult CSVReader::Read(const std::string& fileName, DTM& dtm) {
    try {
        std::ifstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot open file: " + fileName);
        }
        
        std::string line;
        int lineNumber = 0;
        int pointsRead = 0;
        bool hasHeader = false;
        
        // Check if first line is a header
        if (std::getline(file, line)) {
            lineNumber++;
            
            // Simple heuristic: if first line contains non-numeric data, treat as header
            std::istringstream testStream(line);
            double testValue;
            if (!(testStream >> testValue)) {
                hasHeader = true;
            } else {
                // Reset file position to beginning
                file.clear();
                file.seekg(0);
                lineNumber = 0;
            }
        }
        
        while (std::getline(file, line)) {
            lineNumber++;
            
            if (line.empty()) {
                continue;
            }
            
            // Parse CSV line
            std::vector<std::string> fields = ParseCSVLine(line);
            
            if (fields.size() >= 3) {
                try {
                    double x = std::stod(fields[0]);
                    double y = std::stod(fields[1]);
                    double z = std::stod(fields[2]);
                    
                    dtm.AddPointFeature(Point3D(x, y, z));
                    pointsRead++;
                } catch (const std::exception&) {
                    return FileIOResult::Failure("Invalid numeric data at line " + std::to_string(lineNumber));
                }
            } else {
                return FileIOResult::Failure("Insufficient fields at line " + std::to_string(lineNumber));
            }
        }
        
        file.close();
        
        if (pointsRead == 0) {
            return FileIOResult::Failure("No valid points found in file");
        }
        
        return FileIOResult::Success("CSV file loaded successfully, " + std::to_string(pointsRead) + " points read");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error reading CSV file: " + std::string(e.what()));
    }
}

//=======================================================================================
// CSVWriter implementation
//=======================================================================================
FileIOResult CSVWriter::Write(const std::string& fileName, const DTM& dtm) {
    try {
        std::ofstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot create file: " + fileName);
        }
        
        const auto& vertices = dtm.GetVertices();
        
        // Write header
        file << "X,Y,Z" << std::endl;
        
        // Write vertices
        file << std::fixed << std::setprecision(6);
        int pointsWritten = 0;
        
        for (const auto& pair : vertices) {
            const auto& vertex = pair.second;
            if (vertex->IsActive()) {
                Point3D pos = vertex->GetPosition();
                file << pos.x << "," << pos.y << "," << pos.z << std::endl;
                pointsWritten++;
            }
        }
        
        file.close();
        
        return FileIOResult::Success("CSV file saved successfully, " + std::to_string(pointsWritten) + " points written");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error writing CSV file: " + std::string(e.what()));
    }
}

std::vector<std::string> CSVReader::ParseCSVLine(const std::string& line) {
    std::vector<std::string> fields;
    std::string field;
    bool inQuotes = false;
    
    for (char c : line) {
        if (c == '"') {
            inQuotes = !inQuotes;
        } else if (c == ',' && !inQuotes) {
            fields.push_back(field);
            field.clear();
        } else {
            field += c;
        }
    }
    
    fields.push_back(field); // Add last field
    
    return fields;
}

//=======================================================================================
// JSONReader implementation (simplified)
//=======================================================================================
FileIOResult JSONReader::Read(const std::string& fileName, DTM& dtm) {
    // TODO: Implement JSON reading
    // This would require a JSON parsing library or custom parser
    return FileIOResult::Failure("JSON reading not yet implemented");
}

//=======================================================================================
// JSONWriter implementation (simplified)
//=======================================================================================
FileIOResult JSONWriter::Write(const std::string& fileName, const DTM& dtm) {
    try {
        std::ofstream file(fileName);
        if (!file.is_open()) {
            return FileIOResult::Failure("Cannot create file: " + fileName);
        }
        
        const auto& vertices = dtm.GetVertices();
        const auto& triangles = dtm.GetTriangles();
        
        file << std::fixed << std::setprecision(6);
        
        // Write JSON structure
        file << "{" << std::endl;
        file << "  \"type\": \"DTM\"," << std::endl;
        file << "  \"vertices\": [" << std::endl;
        
        // Write vertices
        bool firstVertex = true;
        for (const auto& pair : vertices) {
            const auto& vertex = pair.second;
            if (vertex->IsActive()) {
                if (!firstVertex) {
                    file << "," << std::endl;
                }
                Point3D pos = vertex->GetPosition();
                file << "    {\"id\": " << pair.first 
                     << ", \"x\": " << pos.x 
                     << ", \"y\": " << pos.y 
                     << ", \"z\": " << pos.z << "}";
                firstVertex = false;
            }
        }
        
        file << std::endl << "  ]," << std::endl;
        file << "  \"triangles\": [" << std::endl;
        
        // Write triangles
        bool firstTriangle = true;
        for (const auto& pair : triangles) {
            const auto& triangle = pair.second;
            if (triangle->IsActive()) {
                if (!firstTriangle) {
                    file << "," << std::endl;
                }
                file << "    {\"id\": " << pair.first 
                     << ", \"vertices\": [" 
                     << triangle->GetVertex1Id() << ", "
                     << triangle->GetVertex2Id() << ", "
                     << triangle->GetVertex3Id() << "]}";
                firstTriangle = false;
            }
        }
        
        file << std::endl << "  ]" << std::endl;
        file << "}" << std::endl;
        
        file.close();
        
        return FileIOResult::Success("JSON file saved successfully");
        
    } catch (const std::exception& e) {
        return FileIOResult::Failure("Error writing JSON file: " + std::string(e.what()));
    }
}

} // namespace TerrainModel
