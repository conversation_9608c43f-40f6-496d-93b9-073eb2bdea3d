/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"
#include "NativeDTMFeature.h"
#include "NativeDTMFeatureEnumerator.h"
#include "NativeDTMMesh.h"
#include "NativeDTMMeshEnumerator.h"
#include "TerrainModelUtils.h"
#include <napi.h>

namespace TerrainModelNodeAddon {

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
static Napi::Object InitializeModule(Napi::Env env, Napi::Object exports)
{
    Napi::HandleScope scope(env);

    // Initialize the terrain model library
    TerrainModelUtils::Initialize(env);

    // Register all native classes
    NativeDTM::Init(env, exports);
    NativeDTMFeature::Init(env, exports);
    NativeDTMFeatureEnumerator::Init(env, exports);
    NativeDTMMesh::Init(env, exports);
    NativeDTMMeshEnumerator::Init(env, exports);

    // Add module-level properties and functions
    exports.DefineProperties({
        Napi::PropertyDescriptor::Value("version", Napi::String::New(env, "1.0.0"), napi_default),
        Napi::PropertyDescriptor::Function(env, exports, "createDTM", &TerrainModelUtils::CreateDTM),
        Napi::PropertyDescriptor::Function(env, exports, "loadDTMFromFile", &TerrainModelUtils::LoadDTMFromFile),
        Napi::PropertyDescriptor::Function(env, exports, "setLogLevel", &TerrainModelUtils::SetLogLevel),
    });

    return exports;
}

} // namespace TerrainModelNodeAddon

// Node.js module registration
NODE_API_MODULE(TerrainModelNodeAddon, TerrainModelNodeAddon::InitializeModule)
