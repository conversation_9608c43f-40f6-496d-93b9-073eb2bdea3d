/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"
#include "NativeDTMFeature.h"
#include "NativeDTMFeatureEnumerator.h"
#include "NativeDTMMesh.h"
#include "NativeDTMMeshEnumerator.h"
#include "NativeDTMTinEditor.h"
#include "NativeDTMDrapedLinearElement.h"
#include "NativeWaterAnalysis.h"
#include "NativeDTMPond.h"
#include "NativeDTMSideSlopeInput.h"
#include "NativeDTMCaching.h"
#include "TerrainModelUtils.h"
#include <napi.h>

namespace TerrainModelNodeAddon {

//=======================================================================================
// @bsimethod
//=======================================================================================
static Napi::Object InitializeModule(Napi::Env env, Napi::Object exports)
{
    Napi::HandleScope scope(env);

    // Initialize the terrain model library
    TerrainModelUtils::Initialize(env);

    // Register core DTM classes
    NativeDTM::Init(env, exports);
    NativeDTMFeature::Init(env, exports);
    NativeDTMFeatureEnumerator::Init(env, exports);
    NativeDTMMesh::Init(env, exports);
    NativeDTMMeshEnumerator::Init(env, exports);

    // Register advanced DTM classes
    NativeDTMTinEditor::Init(env, exports);
    NativeDTMDrapedLinearElement::Init(env, exports);

    // Register analysis and utility classes
    NativeWaterAnalysis::Init(env, exports);
    NativeDTMPond::Init(env, exports);
    NativeDTMSideSlopeInput::Init(env, exports);
    NativeDTMCaching::Init(env, exports);

    // Add module-level properties and functions
    exports.DefineProperties({
        Napi::PropertyDescriptor::Value("version", Napi::String::New(env, "1.0.0"), PROPERTY_ATTRIBUTES),
        Napi::PropertyDescriptor::Function(env, exports, "createDTM", &TerrainModelUtils::CreateDTM),
        Napi::PropertyDescriptor::Function(env, exports, "loadDTMFromFile", &TerrainModelUtils::LoadDTMFromFile),
        Napi::PropertyDescriptor::Function(env, exports, "setLogLevel", &TerrainModelUtils::SetLogLevel),
        Napi::PropertyDescriptor::Function(env, exports, "getVersion", &TerrainModelUtils::GetVersion),
        Napi::PropertyDescriptor::Function(env, exports, "initializeLogging", &TerrainModelUtils::InitializeLogging),
    });

    return exports;
}

} // namespace TerrainModelNodeAddon

// Node.js module registration
NODE_API_MODULE(TerrainModelNodeAddon, TerrainModelNodeAddon::InitializeModule)
