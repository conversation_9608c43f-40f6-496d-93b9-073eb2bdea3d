/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "DTMTriangle.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// DTMVertex implementation
//=======================================================================================
DTMVertex::DTMVertex(DTMVertexId id, const Point3D& position)
    : m_id(id)
    , m_position(position)
    , m_isActive(true)
    , m_isBoundary(false)
    , m_isConstrained(false)
    , m_userTag(0)
{
}

void DTMVertex::AddAdjacentTriangle(DTMTriangleId triangleId) {
    auto it = std::find(m_adjacentTriangles.begin(), m_adjacentTriangles.end(), triangleId);
    if (it == m_adjacentTriangles.end()) {
        m_adjacentTriangles.push_back(triangleId);
    }
}

void DTMVertex::RemoveAdjacentTriangle(DTMTriangleId triangleId) {
    auto it = std::find(m_adjacentTriangles.begin(), m_adjacentTriangles.end(), triangleId);
    if (it != m_adjacentTriangles.end()) {
        m_adjacentTriangles.erase(it);
    }
}

void DTMVertex::AddAdjacentEdge(DTMEdgeId edgeId) {
    auto it = std::find(m_adjacentEdges.begin(), m_adjacentEdges.end(), edgeId);
    if (it == m_adjacentEdges.end()) {
        m_adjacentEdges.push_back(edgeId);
    }
}

void DTMVertex::RemoveAdjacentEdge(DTMEdgeId edgeId) {
    auto it = std::find(m_adjacentEdges.begin(), m_adjacentEdges.end(), edgeId);
    if (it != m_adjacentEdges.end()) {
        m_adjacentEdges.erase(it);
    }
}

bool DTMVertex::IsAdjacentTo(DTMVertexId vertexId) const {
    // Check if this vertex shares an edge with the given vertex
    // This would require access to edge data, so for now we'll implement a simple check
    return false; // TODO: Implement proper adjacency check
}

double DTMVertex::DistanceTo(const DTMVertex& other) const {
    return GeometryUtils::Distance3D(m_position, other.m_position);
}

double DTMVertex::DistanceTo(const Point3D& point) const {
    return GeometryUtils::Distance3D(m_position, point);
}

//=======================================================================================
// DTMEdge implementation
//=======================================================================================
DTMEdge::DTMEdge(DTMEdgeId id, DTMVertexId vertex1Id, DTMVertexId vertex2Id)
    : m_id(id)
    , m_vertex1Id(vertex1Id)
    , m_vertex2Id(vertex2Id)
    , m_isActive(true)
    , m_isBoundary(false)
    , m_isConstrained(false)
    , m_leftTriangleId(Constants::INVALID_ID)
    , m_rightTriangleId(Constants::INVALID_ID)
    , m_userTag(0)
{
}

bool DTMEdge::IsIncidentTo(DTMVertexId vertexId) const {
    return m_vertex1Id == vertexId || m_vertex2Id == vertexId;
}

DTMVertexId DTMEdge::GetOtherVertex(DTMVertexId vertexId) const {
    if (m_vertex1Id == vertexId) {
        return m_vertex2Id;
    } else if (m_vertex2Id == vertexId) {
        return m_vertex1Id;
    }
    return Constants::INVALID_ID;
}

bool DTMEdge::IsIncidentTo(DTMTriangleId triangleId) const {
    return m_leftTriangleId == triangleId || m_rightTriangleId == triangleId;
}

DTMTriangleId DTMEdge::GetOtherTriangle(DTMTriangleId triangleId) const {
    if (m_leftTriangleId == triangleId) {
        return m_rightTriangleId;
    } else if (m_rightTriangleId == triangleId) {
        return m_leftTriangleId;
    }
    return Constants::INVALID_ID;
}

void DTMEdge::SetTriangles(DTMTriangleId leftTriangleId, DTMTriangleId rightTriangleId) {
    m_leftTriangleId = leftTriangleId;
    m_rightTriangleId = rightTriangleId;
    
    // Update boundary status
    m_isBoundary = (leftTriangleId == Constants::INVALID_ID || rightTriangleId == Constants::INVALID_ID);
}

void DTMEdge::ReplaceTriangle(DTMTriangleId oldTriangleId, DTMTriangleId newTriangleId) {
    if (m_leftTriangleId == oldTriangleId) {
        m_leftTriangleId = newTriangleId;
    } else if (m_rightTriangleId == oldTriangleId) {
        m_rightTriangleId = newTriangleId;
    }
    
    // Update boundary status
    m_isBoundary = (m_leftTriangleId == Constants::INVALID_ID || m_rightTriangleId == Constants::INVALID_ID);
}

double DTMEdge::GetLength(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    
    if (v1It == vertices.end() || v2It == vertices.end()) {
        return 0.0;
    }
    
    return GeometryUtils::Distance3D(v1It->second->GetPosition(), v2It->second->GetPosition());
}

Point3D DTMEdge::GetMidpoint(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    
    if (v1It == vertices.end() || v2It == vertices.end()) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    return GeometryUtils::Midpoint(v1It->second->GetPosition(), v2It->second->GetPosition());
}

//=======================================================================================
// DTMTriangle implementation
//=======================================================================================
DTMTriangle::DTMTriangle(DTMTriangleId id, DTMVertexId vertex1Id, DTMVertexId vertex2Id, DTMVertexId vertex3Id)
    : m_id(id)
    , m_vertex1Id(vertex1Id)
    , m_vertex2Id(vertex2Id)
    , m_vertex3Id(vertex3Id)
    , m_isActive(true)
    , m_userTag(0)
{
    m_edgeIds[0] = Constants::INVALID_ID;
    m_edgeIds[1] = Constants::INVALID_ID;
    m_edgeIds[2] = Constants::INVALID_ID;
    
    m_adjacentTriangleIds[0] = Constants::INVALID_ID;
    m_adjacentTriangleIds[1] = Constants::INVALID_ID;
    m_adjacentTriangleIds[2] = Constants::INVALID_ID;
}

bool DTMTriangle::IsIncidentTo(DTMVertexId vertexId) const {
    return m_vertex1Id == vertexId || m_vertex2Id == vertexId || m_vertex3Id == vertexId;
}

bool DTMTriangle::IsIncidentTo(DTMEdgeId edgeId) const {
    return m_edgeIds[0] == edgeId || m_edgeIds[1] == edgeId || m_edgeIds[2] == edgeId;
}

int DTMTriangle::GetVertexIndex(DTMVertexId vertexId) const {
    if (m_vertex1Id == vertexId) return 0;
    if (m_vertex2Id == vertexId) return 1;
    if (m_vertex3Id == vertexId) return 2;
    return -1;
}

int DTMTriangle::GetEdgeIndex(DTMEdgeId edgeId) const {
    if (m_edgeIds[0] == edgeId) return 0;
    if (m_edgeIds[1] == edgeId) return 1;
    if (m_edgeIds[2] == edgeId) return 2;
    return -1;
}

DTMVertexId DTMTriangle::GetVertexId(int index) const {
    switch (index) {
        case 0: return m_vertex1Id;
        case 1: return m_vertex2Id;
        case 2: return m_vertex3Id;
        default: return Constants::INVALID_ID;
    }
}

DTMEdgeId DTMTriangle::GetEdgeId(int index) const {
    if (index >= 0 && index < 3) {
        return m_edgeIds[index];
    }
    return Constants::INVALID_ID;
}

DTMTriangleId DTMTriangle::GetAdjacentTriangleId(int index) const {
    if (index >= 0 && index < 3) {
        return m_adjacentTriangleIds[index];
    }
    return Constants::INVALID_ID;
}

void DTMTriangle::SetEdgeId(int index, DTMEdgeId edgeId) {
    if (index >= 0 && index < 3) {
        m_edgeIds[index] = edgeId;
    }
}

void DTMTriangle::SetAdjacentTriangleId(int index, DTMTriangleId triangleId) {
    if (index >= 0 && index < 3) {
        m_adjacentTriangleIds[index] = triangleId;
    }
}

DTMVertexId DTMTriangle::GetOppositeVertex(DTMEdgeId edgeId) const {
    int edgeIndex = GetEdgeIndex(edgeId);
    if (edgeIndex == -1) {
        return Constants::INVALID_ID;
    }
    
    // The opposite vertex is the one not on the edge
    return GetVertexId((edgeIndex + 2) % 3);
}

DTMTriangleId DTMTriangle::GetOppositeTriangle(DTMVertexId vertexId) const {
    int vertexIndex = GetVertexIndex(vertexId);
    if (vertexIndex == -1) {
        return Constants::INVALID_ID;
    }
    
    // The opposite triangle is across from the vertex
    return m_adjacentTriangleIds[vertexIndex];
}

double DTMTriangle::GetArea(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return 0.0;
    }
    
    return GeometryUtils::TriangleArea3D(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

Point3D DTMTriangle::GetCentroid(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    return GeometryUtils::TriangleCentroid(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

Point3D DTMTriangle::GetNormal(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return Point3D(0.0, 0.0, 1.0);
    }
    
    return GeometryUtils::TriangleNormal(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

Point3D DTMTriangle::GetCircumcenter(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    return GeometryUtils::TriangleCircumcenter(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

double DTMTriangle::GetCircumradius(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return 0.0;
    }
    
    return GeometryUtils::TriangleCircumradius(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

bool DTMTriangle::ContainsPoint(const Point3D& point, const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return false;
    }
    
    return GeometryUtils::IsPointInTriangle2D(
        point,
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

Point3D DTMTriangle::InterpolateElevation(const Point3D& point, const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return point;
    }
    
    return GeometryUtils::InterpolateInTriangle(
        point,
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
}

bool DTMTriangle::IsDegenerate(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices, double tolerance) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return true;
    }
    
    return GeometryUtils::IsTriangleDegenerate(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition(),
        tolerance
    );
}

double DTMTriangle::GetQuality(const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices) const {
    auto v1It = vertices.find(m_vertex1Id);
    auto v2It = vertices.find(m_vertex2Id);
    auto v3It = vertices.find(m_vertex3Id);
    
    if (v1It == vertices.end() || v2It == vertices.end() || v3It == vertices.end()) {
        return 0.0;
    }
    
    double aspectRatio = GeometryUtils::TriangleAspectRatio(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    );
    
    // Quality is inverse of aspect ratio, normalized to [0,1]
    return 1.0 / (1.0 + aspectRatio);
}

} // namespace TerrainModel
