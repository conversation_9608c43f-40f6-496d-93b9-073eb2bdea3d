#---------------------------------------------------------------------------------------------
#  Copyright (c) Bentley Systems, Incorporated. All rights reserved.
#  See COPYRIGHT.md in the repository root for full copyright notice.
#---------------------------------------------------------------------------------------------
%include mdl.mki

# Use `bb pull` to do the `npm install`.
# Since this builds into $SrcRoot, always clean and rebuild.

srcDir = $(_MakeFilePath)
outDir = $(_MakeFilePath)lib/

always:
    ~chdir $(srcDir)
    -python $(SrcRoot)thirdparty/nodejs_10_16_1/bentley/npm.py run clean
    -python $(SrcRoot)thirdparty/nodejs_10_16_1/bentley/npm.py run build

$(BuildContext)Delivery/lib : $(outDir)
    $(LinkFirstDepToFirstTargetAsDirectory)
