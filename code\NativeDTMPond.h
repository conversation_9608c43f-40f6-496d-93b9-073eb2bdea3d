/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Pond Calculation Result structure
//=======================================================================================
struct PondCalculationResult {
    bool calculated;
    double elevation;
    double depth;
    double area;
    double volume;
    std::vector<Point3D> pondFeatures;
    
    PondCalculationResult() 
        : calculated(false), elevation(0.0), depth(0.0), area(0.0), volume(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static PondCalculationResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Stock Pile Result structure
//=======================================================================================
struct StockPileResult {
    double stockPileVolume;
    NativeDTM* stockPileDTM;
    NativeDTM* mergedDTM;
    
    StockPileResult() : stockPileVolume(0.0), stockPileDTM(nullptr), mergedDTM(nullptr) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static StockPileResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Native DTM Pond wrapper class for pond design and analysis
//=======================================================================================
class NativeDTMPond : public TerrainModelObjectWrap<NativeDTMPond>
{
private:
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;
    std::vector<Point3D> m_pondBoundary;
    double m_targetVolume;
    double m_targetElevation;
    double m_targetDepth;
    PondCalculationResult m_lastCalculation;

public:
    // Constructor
    NativeDTMPond(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMPond();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetPondBoundary(const Napi::CallbackInfo& info);
    void SetPondBoundary(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetTargetVolume(const Napi::CallbackInfo& info);
    void SetTargetVolume(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetTargetElevation(const Napi::CallbackInfo& info);
    void SetTargetElevation(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetTargetDepth(const Napi::CallbackInfo& info);
    void SetTargetDepth(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods - Pond design
    Napi::Value CalculatePondByVolume(const Napi::CallbackInfo& info);
    Napi::Value CalculatePondByElevation(const Napi::CallbackInfo& info);
    Napi::Value CalculatePondByDepth(const Napi::CallbackInfo& info);
    Napi::Value CalculatePondByBoundary(const Napi::CallbackInfo& info);

    // Instance methods - Pond analysis
    Napi::Value GetPondStatistics(const Napi::CallbackInfo& info);
    Napi::Value GetPondContours(const Napi::CallbackInfo& info);
    Napi::Value GetPondCrossSection(const Napi::CallbackInfo& info);
    Napi::Value GetPondVolumeTable(const Napi::CallbackInfo& info);

    // Instance methods - Stock pile operations
    Napi::Value CalculateStockPile(const Napi::CallbackInfo& info);
    Napi::Value CreateStockPileDTM(const Napi::CallbackInfo& info);
    Napi::Value MergeWithStockPile(const Napi::CallbackInfo& info);

    // Instance methods - Pond modification
    Napi::Value AddPondBoundaryPoint(const Napi::CallbackInfo& info);
    Napi::Value RemovePondBoundaryPoint(const Napi::CallbackInfo& info);
    Napi::Value InsertPondBoundaryPoint(const Napi::CallbackInfo& info);
    Napi::Value ClearPondBoundary(const Napi::CallbackInfo& info);

    // Instance methods - Validation and optimization
    Napi::Value ValidatePondDesign(const Napi::CallbackInfo& info);
    Napi::Value OptimizePondShape(const Napi::CallbackInfo& info);
    Napi::Value CheckPondFeasibility(const Napi::CallbackInfo& info);

    // Instance methods - Export and visualization
    Napi::Value ExportPondData(const Napi::CallbackInfo& info);
    Napi::Value GeneratePondMesh(const Napi::CallbackInfo& info);
    Napi::Value GetPondVisualizationData(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value GetLastCalculation(const Napi::CallbackInfo& info);
    Napi::Value Reset(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    bool IsValidPond() const { return m_dtm != nullptr && !m_disposed; }

    // Factory method for creating from DTM
    static Napi::Object CreateFromDTM(Napi::Env env, NativeDTM* dtm);

private:
    // Helper methods
    void InitializeFromDTM(NativeDTM* dtm);
    bool ValidateBoundary(Napi::Env env);
    PondCalculationResult PerformPondCalculation(const std::string& method, double parameter);
    std::vector<Point3D> GeneratePondContour(double elevation);
    double CalculateVolumeAtElevation(double elevation);
    double CalculateAreaAtElevation(double elevation);
    std::vector<Point3D> GetCrossSectionPoints(const Point3D& start, const Point3D& end);
};

//=======================================================================================
// Pond Statistics structure
//=======================================================================================
struct PondStatistics {
    double surfaceArea;
    double volume;
    double maxDepth;
    double averageDepth;
    double perimeter;
    Point3D centroid;
    double waterLevel;
    std::vector<double> depthDistribution;
    
    PondStatistics() 
        : surfaceArea(0.0), volume(0.0), maxDepth(0.0), averageDepth(0.0), 
          perimeter(0.0), waterLevel(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static PondStatistics FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Pond Volume Table Entry structure
//=======================================================================================
struct PondVolumeTableEntry {
    double elevation;
    double area;
    double volume;
    double incrementalVolume;
    
    PondVolumeTableEntry() 
        : elevation(0.0), area(0.0), volume(0.0), incrementalVolume(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static PondVolumeTableEntry FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Pond Cross Section structure
//=======================================================================================
struct PondCrossSection {
    Point3D startPoint;
    Point3D endPoint;
    std::vector<Point3D> profilePoints;
    std::vector<double> distances;
    std::vector<double> elevations;
    double totalLength;
    double waterLevel;
    
    PondCrossSection() : totalLength(0.0), waterLevel(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static PondCrossSection FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Pond Visualization Data structure
//=======================================================================================
struct PondVisualizationData {
    std::vector<Point3D> boundaryPoints;
    std::vector<Point3D> waterSurfacePoints;
    std::vector<std::vector<Point3D>> contourLines;
    std::vector<std::array<int, 3>> triangles;
    std::vector<double> depthValues;
    Range3D bounds;
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static PondVisualizationData FromJavaScript(Napi::Object obj);
};

} // namespace TerrainModelNodeAddon
