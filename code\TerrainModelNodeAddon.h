/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include <napi.h>
#include <memory>
#include <string>
#include <vector>

// Include pure C++ TerrainModel headers
#include "TerrainModelCore/TerrainModelTypes.h"
#include "TerrainModelCore/DTM.h"
#include "TerrainModelCore/DTMFeature.h"
#include "TerrainModelCore/DTMTriangle.h"
#include "TerrainModelCore/DTMTinEditor.h"
#include "TerrainModelCore/WaterAnalysis.h"
#include "TerrainModelCore/DTMPond.h"

// Use TerrainModel namespace
using namespace TerrainModel;

namespace TerrainModelNodeAddon {

// Forward declarations - Core classes
class NativeDTM;
class NativeDTMFeature;
class NativeDTMFeatureEnumerator;
class NativeDTMMesh;
class NativeDTMMeshEnumerator;

// Forward declarations - Advanced classes
class NativeDTMTinEditor;
class NativeDTMDrapedLinearElement;
class NativeDTMDrapedLinearElementPoint;
class NativeWaterAnalysis;
class NativeWaterAnalysisResult;
class NativeDTMPond;
class NativeDTMSideSlopeInput;
class NativeDTMSideSlopeInputPoint;
class NativeDTMCaching;

//=======================================================================================
// Common type definitions and constants
//=======================================================================================
constexpr auto PROPERTY_ATTRIBUTES = static_cast<napi_property_attributes>(napi_writable | napi_configurable);

//=======================================================================================
// Error handling macros
//=======================================================================================
#define TERRAIN_MODEL_NAPI_CALL(env, call)                                    \
    do {                                                                       \
        napi_status status = (call);                                          \
        if (status != napi_ok) {                                              \
            const napi_extended_error_info* error_info = nullptr;             \
            napi_get_last_error_info((env), &error_info);                     \
            bool is_pending;                                                   \
            napi_is_exception_pending((env), &is_pending);                    \
            if (!is_pending) {                                                 \
                const char* message = (error_info->error_message == nullptr)  \
                    ? "empty error message"                                    \
                    : error_info->error_message;                               \
                napi_throw_error((env), nullptr, message);                    \
            }                                                                  \
            return nullptr;                                                    \
        }                                                                      \
    } while(0)

//=======================================================================================
// Base wrapper class for all terrain model objects
//=======================================================================================
template<typename T>
class TerrainModelObjectWrap : public Napi::ObjectWrap<T>
{
public:
    TerrainModelObjectWrap(const Napi::CallbackInfo& info) : Napi::ObjectWrap<T>(info) {}
    virtual ~TerrainModelObjectWrap() = default;

protected:
    // Helper method to throw terrain model specific errors
    void ThrowTerrainModelError(Napi::Env env, const std::string& message) {
        Napi::Error::New(env, "TerrainModelError: " + message).ThrowAsJavaScriptException();
    }

    // Helper method to check if object is disposed
    void CheckDisposed(Napi::Env env, const std::string& methodName) {
        if (m_disposed) {
            ThrowTerrainModelError(env, "Object has been disposed and cannot be used in " + methodName);
        }
    }

    bool m_disposed = false;
};

//=======================================================================================
// Point3D structure for JavaScript interop
//=======================================================================================
struct Point3D {
    double x, y, z;
    
    Point3D() : x(0), y(0), z(0) {}
    Point3D(double x, double y, double z) : x(x), y(y), z(z) {}
    
    // Convert from Bentley DPoint3d
    static Point3D FromDPoint3d(const DPoint3d& point) {
        return Point3D(point.x, point.y, point.z);
    }
    
    // Convert to Bentley DPoint3d
    DPoint3d ToDPoint3d() const {
        return DPoint3d::From(x, y, z);
    }
    
    // Convert to/from JavaScript object
    static Point3D FromJavaScript(Napi::Object obj);
    Napi::Object ToJavaScript(Napi::Env env) const;
};

//=======================================================================================
// Range3D structure for JavaScript interop
//=======================================================================================
struct Range3D {
    Point3D low, high;
    
    Range3D() = default;
    Range3D(const Point3D& low, const Point3D& high) : low(low), high(high) {}
    
    // Convert from Bentley DRange3d
    static Range3D FromDRange3d(const DRange3d& range) {
        return Range3D(Point3D::FromDPoint3d(range.low), Point3D::FromDPoint3d(range.high));
    }
    
    // Convert to Bentley DRange3d
    DRange3d ToDRange3d() const {
        return DRange3d::From(low.ToDPoint3d(), high.ToDPoint3d());
    }
    
    // Convert to/from JavaScript object
    static Range3D FromJavaScript(Napi::Object obj);
    Napi::Object ToJavaScript(Napi::Env env) const;
};

} // namespace TerrainModelNodeAddon
