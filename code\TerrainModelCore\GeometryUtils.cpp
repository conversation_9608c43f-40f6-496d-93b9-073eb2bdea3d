/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "GeometryUtils.h"
#include <algorithm>
#include <cmath>
#include <stack>

namespace TerrainModel {

//=======================================================================================
// Point operations
//=======================================================================================
Point3D GeometryUtils::ProjectToLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) {
    Point3D lineVector = Point3D(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
    Point3D pointVector = Point3D(point.x - lineStart.x, point.y - lineStart.y, point.z - lineStart.z);
    
    double lineLength = LengthSquared(lineVector);
    if (IsNearlyZero(lineLength)) {
        return lineStart;
    }
    
    double t = DotProduct(pointVector, lineVector) / lineLength;
    t = Clamp(t, 0.0, 1.0);
    
    return Interpolate(lineStart, lineEnd, t);
}

Point3D GeometryUtils::ProjectToPlane(const Point3D& point, const Point3D& planePoint, const Point3D& planeNormal) {
    Point3D normalizedNormal = Normalize(planeNormal);
    Point3D pointToPlane = Point3D(point.x - planePoint.x, point.y - planePoint.y, point.z - planePoint.z);
    
    double distance = DotProduct(pointToPlane, normalizedNormal);
    
    return Point3D(
        point.x - distance * normalizedNormal.x,
        point.y - distance * normalizedNormal.y,
        point.z - distance * normalizedNormal.z
    );
}

Point3D GeometryUtils::Normalize(const Point3D& vector) {
    double length = Length(vector);
    if (IsNearlyZero(length)) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    return Point3D(vector.x / length, vector.y / length, vector.z / length);
}

double GeometryUtils::Angle(const Point3D& v1, const Point3D& v2) {
    double dot = DotProduct(Normalize(v1), Normalize(v2));
    dot = Clamp(dot, -1.0, 1.0);
    return std::acos(dot);
}

//=======================================================================================
// Triangle operations
//=======================================================================================
double GeometryUtils::TriangleArea2D(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    return std::abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y)) * 0.5;
}

double GeometryUtils::TriangleArea3D(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    Point3D v1 = Point3D(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Point3D v2 = Point3D(p3.x - p1.x, p3.y - p1.y, p3.z - p1.z);
    Point3D cross = CrossProduct(v1, v2);
    return Length(cross) * 0.5;
}

Point3D GeometryUtils::TriangleCentroid(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    return Point3D(
        (p1.x + p2.x + p3.x) / 3.0,
        (p1.y + p2.y + p3.y) / 3.0,
        (p1.z + p2.z + p3.z) / 3.0
    );
}

Point3D GeometryUtils::TriangleNormal(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    Point3D v1 = Point3D(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Point3D v2 = Point3D(p3.x - p1.x, p3.y - p1.y, p3.z - p1.z);
    return Normalize(CrossProduct(v1, v2));
}

Point3D GeometryUtils::TriangleCircumcenter(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    double ax = p1.x, ay = p1.y;
    double bx = p2.x, by = p2.y;
    double cx = p3.x, cy = p3.y;
    
    double d = 2.0 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by));
    
    if (IsNearlyZero(d)) {
        // Degenerate triangle, return centroid
        return TriangleCentroid(p1, p2, p3);
    }
    
    double a2 = ax * ax + ay * ay;
    double b2 = bx * bx + by * by;
    double c2 = cx * cx + cy * cy;
    
    double ux = (a2 * (by - cy) + b2 * (cy - ay) + c2 * (ay - by)) / d;
    double uy = (a2 * (cx - bx) + b2 * (ax - cx) + c2 * (bx - ax)) / d;
    
    // Interpolate Z coordinate
    double uz = (p1.z + p2.z + p3.z) / 3.0;
    
    return Point3D(ux, uy, uz);
}

double GeometryUtils::TriangleCircumradius(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    double a = Distance2D(p2, p3);
    double b = Distance2D(p1, p3);
    double c = Distance2D(p1, p2);
    
    double area = TriangleArea2D(p1, p2, p3);
    if (IsNearlyZero(area)) {
        return std::numeric_limits<double>::infinity();
    }
    
    return (a * b * c) / (4.0 * area);
}

Point3D GeometryUtils::TriangleIncenter(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    double a = Distance2D(p2, p3);
    double b = Distance2D(p1, p3);
    double c = Distance2D(p1, p2);
    
    double perimeter = a + b + c;
    if (IsNearlyZero(perimeter)) {
        return TriangleCentroid(p1, p2, p3);
    }
    
    return Point3D(
        (a * p1.x + b * p2.x + c * p3.x) / perimeter,
        (a * p1.y + b * p2.y + c * p3.y) / perimeter,
        (a * p1.z + b * p2.z + c * p3.z) / perimeter
    );
}

double GeometryUtils::TriangleInradius(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    double a = Distance2D(p2, p3);
    double b = Distance2D(p1, p3);
    double c = Distance2D(p1, p2);
    
    double s = (a + b + c) * 0.5; // semi-perimeter
    double area = TriangleArea2D(p1, p2, p3);
    
    if (IsNearlyZero(s)) {
        return 0.0;
    }
    
    return area / s;
}

//=======================================================================================
// Triangle quality metrics
//=======================================================================================
double GeometryUtils::TriangleAspectRatio(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    double circumradius = TriangleCircumradius(p1, p2, p3);
    double inradius = TriangleInradius(p1, p2, p3);
    
    if (IsNearlyZero(inradius)) {
        return std::numeric_limits<double>::infinity();
    }
    
    return circumradius / (2.0 * inradius);
}

double GeometryUtils::TriangleMinAngle(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    Point3D v1 = Point3D(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Point3D v2 = Point3D(p3.x - p1.x, p3.y - p1.y, p3.z - p1.z);
    Point3D v3 = Point3D(p1.x - p2.x, p1.y - p2.y, p1.z - p2.z);
    Point3D v4 = Point3D(p3.x - p2.x, p3.y - p2.y, p3.z - p2.z);
    Point3D v5 = Point3D(p1.x - p3.x, p1.y - p3.y, p1.z - p3.z);
    Point3D v6 = Point3D(p2.x - p3.x, p2.y - p3.y, p2.z - p3.z);
    
    double angle1 = Angle(v1, v2);
    double angle2 = Angle(v3, v4);
    double angle3 = Angle(v5, v6);
    
    return std::min({angle1, angle2, angle3}) * Constants::RADIANS_TO_DEGREES;
}

double GeometryUtils::TriangleMaxAngle(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    Point3D v1 = Point3D(p2.x - p1.x, p2.y - p1.y, p2.z - p1.z);
    Point3D v2 = Point3D(p3.x - p1.x, p3.y - p1.y, p3.z - p1.z);
    Point3D v3 = Point3D(p1.x - p2.x, p1.y - p2.y, p1.z - p2.z);
    Point3D v4 = Point3D(p3.x - p2.x, p3.y - p2.y, p3.z - p2.z);
    Point3D v5 = Point3D(p1.x - p3.x, p1.y - p3.y, p1.z - p3.z);
    Point3D v6 = Point3D(p2.x - p3.x, p2.y - p3.y, p2.z - p3.z);
    
    double angle1 = Angle(v1, v2);
    double angle2 = Angle(v3, v4);
    double angle3 = Angle(v5, v6);
    
    return std::max({angle1, angle2, angle3}) * Constants::RADIANS_TO_DEGREES;
}

bool GeometryUtils::IsTriangleSliver(const Point3D& p1, const Point3D& p2, const Point3D& p3, double threshold) {
    double aspectRatio = TriangleAspectRatio(p1, p2, p3);
    return aspectRatio > (1.0 / threshold);
}

bool GeometryUtils::IsTriangleDegenerate(const Point3D& p1, const Point3D& p2, const Point3D& p3, double tolerance) {
    double area = TriangleArea2D(p1, p2, p3);
    return area < tolerance;
}

//=======================================================================================
// Point-in-triangle tests
//=======================================================================================
bool GeometryUtils::IsPointInTriangle2D(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3) {
    // Use barycentric coordinates
    Point3D bary = BarycentricCoordinates(point, v1, v2, v3);
    return (bary.x >= 0.0) && (bary.y >= 0.0) && (bary.z >= 0.0);
}

Point3D GeometryUtils::BarycentricCoordinates(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3) {
    Point3D v0 = Point3D(v3.x - v1.x, v3.y - v1.y, v3.z - v1.z);
    Point3D v1v = Point3D(v2.x - v1.x, v2.y - v1.y, v2.z - v1.z);
    Point3D v2v = Point3D(point.x - v1.x, point.y - v1.y, point.z - v1.z);
    
    double dot00 = DotProduct(v0, v0);
    double dot01 = DotProduct(v0, v1v);
    double dot02 = DotProduct(v0, v2v);
    double dot11 = DotProduct(v1v, v1v);
    double dot12 = DotProduct(v1v, v2v);
    
    double invDenom = 1.0 / (dot00 * dot11 - dot01 * dot01);
    double u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    double v = (dot00 * dot12 - dot01 * dot02) * invDenom;
    
    return Point3D(1.0 - u - v, v, u);
}

Point3D GeometryUtils::InterpolateInTriangle(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3) {
    Point3D bary = BarycentricCoordinates(point, v1, v2, v3);
    
    return Point3D(
        bary.x * v1.x + bary.y * v2.x + bary.z * v3.x,
        bary.x * v1.y + bary.y * v2.y + bary.z * v3.y,
        bary.x * v1.z + bary.y * v2.z + bary.z * v3.z
    );
}

//=======================================================================================
// Line operations
//=======================================================================================
double GeometryUtils::PointToLineDistance2D(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) {
    Point3D projected = ProjectToLine(point, lineStart, lineEnd);
    return Distance2D(point, projected);
}

double GeometryUtils::PointToLineDistance3D(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) {
    Point3D projected = ProjectToLine(point, lineStart, lineEnd);
    return Distance3D(point, projected);
}

Point3D GeometryUtils::ClosestPointOnLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) {
    return ProjectToLine(point, lineStart, lineEnd);
}

double GeometryUtils::ParameterOnLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) {
    Point3D lineVector = Point3D(lineEnd.x - lineStart.x, lineEnd.y - lineStart.y, lineEnd.z - lineStart.z);
    Point3D pointVector = Point3D(point.x - lineStart.x, point.y - lineStart.y, point.z - lineStart.z);
    
    double lineLength = LengthSquared(lineVector);
    if (IsNearlyZero(lineLength)) {
        return 0.0;
    }
    
    return DotProduct(pointVector, lineVector) / lineLength;
}

//=======================================================================================
// Line intersection
//=======================================================================================
bool GeometryUtils::LineIntersection2D(const Point3D& p1, const Point3D& p2, const Point3D& p3, const Point3D& p4, Point3D& intersection) {
    double denom = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);

    if (IsNearlyZero(denom)) {
        return false; // Lines are parallel
    }

    double t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / denom;

    intersection.x = p1.x + t * (p2.x - p1.x);
    intersection.y = p1.y + t * (p2.y - p1.y);
    intersection.z = p1.z + t * (p2.z - p1.z);

    return true;
}

bool GeometryUtils::SegmentIntersection2D(const Point3D& p1, const Point3D& p2, const Point3D& p3, const Point3D& p4, Point3D& intersection) {
    double denom = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);

    if (IsNearlyZero(denom)) {
        return false; // Lines are parallel
    }

    double t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / denom;
    double u = -((p1.x - p2.x) * (p1.y - p3.y) - (p1.y - p2.y) * (p1.x - p3.x)) / denom;

    if (t >= 0.0 && t <= 1.0 && u >= 0.0 && u <= 1.0) {
        intersection.x = p1.x + t * (p2.x - p1.x);
        intersection.y = p1.y + t * (p2.y - p1.y);
        intersection.z = p1.z + t * (p2.z - p1.z);
        return true;
    }

    return false;
}

bool GeometryUtils::LineSegmentIntersection2D(const Point3D& lineStart, const Point3D& lineEnd,
                                             const Point3D& segStart, const Point3D& segEnd, Point3D& intersection) {
    return SegmentIntersection2D(lineStart, lineEnd, segStart, segEnd, intersection);
}

//=======================================================================================
// Polygon operations
//=======================================================================================
double GeometryUtils::PolygonArea2D(const std::vector<Point3D>& polygon) {
    if (polygon.size() < 3) {
        return 0.0;
    }

    double area = 0.0;
    size_t n = polygon.size();

    for (size_t i = 0; i < n; ++i) {
        size_t j = (i + 1) % n;
        area += polygon[i].x * polygon[j].y;
        area -= polygon[j].x * polygon[i].y;
    }

    return std::abs(area) * 0.5;
}

Point3D GeometryUtils::PolygonCentroid2D(const std::vector<Point3D>& polygon) {
    if (polygon.empty()) {
        return Point3D(0.0, 0.0, 0.0);
    }

    if (polygon.size() == 1) {
        return polygon[0];
    }

    double area = PolygonArea2D(polygon);
    if (IsNearlyZero(area)) {
        // Degenerate polygon, return average of vertices
        Point3D centroid(0.0, 0.0, 0.0);
        for (const auto& point : polygon) {
            centroid.x += point.x;
            centroid.y += point.y;
            centroid.z += point.z;
        }
        double n = static_cast<double>(polygon.size());
        return Point3D(centroid.x / n, centroid.y / n, centroid.z / n);
    }

    double cx = 0.0, cy = 0.0, cz = 0.0;
    size_t n = polygon.size();

    for (size_t i = 0; i < n; ++i) {
        size_t j = (i + 1) % n;
        double cross = polygon[i].x * polygon[j].y - polygon[j].x * polygon[i].y;
        cx += (polygon[i].x + polygon[j].x) * cross;
        cy += (polygon[i].y + polygon[j].y) * cross;
        cz += (polygon[i].z + polygon[j].z) * cross;
    }

    double factor = 1.0 / (6.0 * area);
    return Point3D(cx * factor, cy * factor, cz * factor);
}

bool GeometryUtils::IsPointInPolygon2D(const Point3D& point, const std::vector<Point3D>& polygon) {
    if (polygon.size() < 3) {
        return false;
    }

    bool inside = false;
    size_t n = polygon.size();

    for (size_t i = 0, j = n - 1; i < n; j = i++) {
        if (((polygon[i].y > point.y) != (polygon[j].y > point.y)) &&
            (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
            inside = !inside;
        }
    }

    return inside;
}

bool GeometryUtils::IsPolygonClockwise2D(const std::vector<Point3D>& polygon) {
    if (polygon.size() < 3) {
        return false;
    }

    double sum = 0.0;
    size_t n = polygon.size();

    for (size_t i = 0; i < n; ++i) {
        size_t j = (i + 1) % n;
        sum += (polygon[j].x - polygon[i].x) * (polygon[j].y + polygon[i].y);
    }

    return sum > 0.0;
}

std::vector<Point3D> GeometryUtils::ConvexHull2D(const std::vector<Point3D>& points) {
    if (points.size() <= 1) {
        return points;
    }

    // Sort points lexicographically
    std::vector<Point3D> sortedPoints = points;
    std::sort(sortedPoints.begin(), sortedPoints.end(), [](const Point3D& a, const Point3D& b) {
        return a.x < b.x || (a.x == b.x && a.y < b.y);
    });

    // Build lower hull
    std::vector<Point3D> hull;
    for (const auto& point : sortedPoints) {
        while (hull.size() >= 2 && Orient2D(hull[hull.size()-2], hull[hull.size()-1], point) <= 0) {
            hull.pop_back();
        }
        hull.push_back(point);
    }

    // Build upper hull
    size_t lowerSize = hull.size();
    for (int i = static_cast<int>(sortedPoints.size()) - 2; i >= 0; --i) {
        while (hull.size() > lowerSize && Orient2D(hull[hull.size()-2], hull[hull.size()-1], sortedPoints[i]) <= 0) {
            hull.pop_back();
        }
        hull.push_back(sortedPoints[i]);
    }

    // Remove last point as it's the same as the first
    if (hull.size() > 1) {
        hull.pop_back();
    }

    return hull;
}

//=======================================================================================
// Circle operations
//=======================================================================================
bool GeometryUtils::IsPointInCircle(const Point3D& point, const Point3D& center, double radius) {
    return DistanceSquared2D(point, center) <= radius * radius;
}

bool GeometryUtils::IsPointInCircumcircle(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3) {
    Point3D center = TriangleCircumcenter(v1, v2, v3);
    double radius = TriangleCircumradius(v1, v2, v3);
    return IsPointInCircle(point, center, radius);
}

Point3D GeometryUtils::CircleCenter(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    return TriangleCircumcenter(p1, p2, p3);
}

//=======================================================================================
// Geometric predicates (robust)
//=======================================================================================
double GeometryUtils::Orient2D(const Point3D& pa, const Point3D& pb, const Point3D& pc) {
    return (pb.x - pa.x) * (pc.y - pa.y) - (pc.x - pa.x) * (pb.y - pa.y);
}

double GeometryUtils::InCircle(const Point3D& pa, const Point3D& pb, const Point3D& pc, const Point3D& pd) {
    double adx = pa.x - pd.x;
    double ady = pa.y - pd.y;
    double bdx = pb.x - pd.x;
    double bdy = pb.y - pd.y;
    double cdx = pc.x - pd.x;
    double cdy = pc.y - pd.y;

    double abdet = adx * bdy - bdx * ady;
    double bcdet = bdx * cdy - cdx * bdy;
    double cadet = cdx * ady - adx * cdy;

    double alift = adx * adx + ady * ady;
    double blift = bdx * bdx + bdy * bdy;
    double clift = cdx * cdx + cdy * cdy;

    return alift * bcdet + blift * cadet + clift * abdet;
}

bool GeometryUtils::IsCounterClockwise(const Point3D& p1, const Point3D& p2, const Point3D& p3) {
    return Orient2D(p1, p2, p3) > 0.0;
}

//=======================================================================================
// Internal helper functions
//=======================================================================================
double GeometryUtils::Determinant2x2(double a, double b, double c, double d) {
    return a * d - b * c;
}

double GeometryUtils::Determinant3x3(double a, double b, double c, double d, double e, double f, double g, double h, double i) {
    return a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g);
}

} // namespace TerrainModel
