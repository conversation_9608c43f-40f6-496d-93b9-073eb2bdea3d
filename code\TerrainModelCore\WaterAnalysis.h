/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include "DTM.h"

namespace TerrainModel {

//=======================================================================================
// Water Analysis Result Item base class
//=======================================================================================
class WaterAnalysisResultItem {
protected:
    WaterAnalysisResultType m_type;
    double m_waterVolume;
    std::vector<Point3D> m_points;

public:
    WaterAnalysisResultItem(WaterAnalysisResultType type) : m_type(type), m_waterVolume(0.0) {}
    virtual ~WaterAnalysisResultItem() = default;
    
    WaterAnalysisResultType GetType() const { return m_type; }
    double GetWaterVolume() const { return m_waterVolume; }
    const std::vector<Point3D>& GetPoints() const { return m_points; }
    
    virtual std::string ToString() const = 0;
};

//=======================================================================================
// Water Analysis Result Point
//=======================================================================================
class WaterAnalysisResultPoint : public WaterAnalysisResultItem {
private:
    Point3D m_location;
    double m_elevation;

public:
    WaterAnalysisResultPoint(const Point3D& location, double elevation)
        : WaterAnalysisResultItem(WaterAnalysisResultType::Point)
        , m_location(location)
        , m_elevation(elevation) {
        m_points.push_back(location);
    }
    
    const Point3D& GetLocation() const { return m_location; }
    double GetElevation() const { return m_elevation; }
    
    std::string ToString() const override {
        return "Point at (" + std::to_string(m_location.x) + ", " + 
               std::to_string(m_location.y) + ", " + std::to_string(m_elevation) + ")";
    }
};

//=======================================================================================
// Water Analysis Result Stream
//=======================================================================================
class WaterAnalysisResultStream : public WaterAnalysisResultItem {
private:
    std::vector<Point3D> m_streamPath;
    double m_length;
    double m_averageSlope;
    Point3D m_startPoint;
    Point3D m_endPoint;

public:
    WaterAnalysisResultStream(const std::vector<Point3D>& streamPath)
        : WaterAnalysisResultItem(WaterAnalysisResultType::Stream)
        , m_streamPath(streamPath)
        , m_length(0.0)
        , m_averageSlope(0.0) {
        m_points = streamPath;
        if (!streamPath.empty()) {
            m_startPoint = streamPath.front();
            m_endPoint = streamPath.back();
            CalculateProperties();
        }
    }
    
    const std::vector<Point3D>& GetStreamPath() const { return m_streamPath; }
    double GetLength() const { return m_length; }
    double GetAverageSlope() const { return m_averageSlope; }
    const Point3D& GetStartPoint() const { return m_startPoint; }
    const Point3D& GetEndPoint() const { return m_endPoint; }
    
    std::string ToString() const override {
        return "Stream with " + std::to_string(m_streamPath.size()) + 
               " points, length: " + std::to_string(m_length);
    }

private:
    void CalculateProperties() {
        m_length = 0.0;
        double totalElevationChange = 0.0;
        
        for (size_t i = 1; i < m_streamPath.size(); ++i) {
            double segmentLength = m_streamPath[i-1].distance(m_streamPath[i]);
            m_length += segmentLength;
            totalElevationChange += std::abs(m_streamPath[i].z - m_streamPath[i-1].z);
        }
        
        if (m_length > 0.0) {
            m_averageSlope = totalElevationChange / m_length;
        }
    }
};

//=======================================================================================
// Water Analysis Result Pond
//=======================================================================================
class WaterAnalysisResultPond : public WaterAnalysisResultItem {
private:
    std::vector<Point3D> m_boundary;
    double m_area;
    double m_maxDepth;
    double m_averageDepth;
    Point3D m_centroid;
    double m_waterLevel;

public:
    WaterAnalysisResultPond(const std::vector<Point3D>& boundary, double waterLevel)
        : WaterAnalysisResultItem(WaterAnalysisResultType::Pond)
        , m_boundary(boundary)
        , m_area(0.0)
        , m_maxDepth(0.0)
        , m_averageDepth(0.0)
        , m_waterLevel(waterLevel) {
        m_points = boundary;
        CalculateProperties();
    }
    
    const std::vector<Point3D>& GetBoundary() const { return m_boundary; }
    double GetArea() const { return m_area; }
    double GetMaxDepth() const { return m_maxDepth; }
    double GetAverageDepth() const { return m_averageDepth; }
    const Point3D& GetCentroid() const { return m_centroid; }
    double GetWaterLevel() const { return m_waterLevel; }
    
    std::string ToString() const override {
        return "Pond with area: " + std::to_string(m_area) + 
               ", max depth: " + std::to_string(m_maxDepth);
    }

private:
    void CalculateProperties() {
        if (m_boundary.size() < 3) return;
        
        // Calculate area using shoelace formula
        m_area = 0.0;
        for (size_t i = 0; i < m_boundary.size(); ++i) {
            size_t j = (i + 1) % m_boundary.size();
            m_area += m_boundary[i].x * m_boundary[j].y;
            m_area -= m_boundary[j].x * m_boundary[i].y;
        }
        m_area = std::abs(m_area) * 0.5;
        
        // Calculate centroid
        double cx = 0.0, cy = 0.0;
        for (const auto& point : m_boundary) {
            cx += point.x;
            cy += point.y;
        }
        m_centroid = Point3D(cx / m_boundary.size(), cy / m_boundary.size(), m_waterLevel);
        
        // Calculate depths
        double totalDepth = 0.0;
        m_maxDepth = 0.0;
        for (const auto& point : m_boundary) {
            double depth = m_waterLevel - point.z;
            if (depth > 0.0) {
                totalDepth += depth;
                m_maxDepth = std::max(m_maxDepth, depth);
            }
        }
        m_averageDepth = totalDepth / m_boundary.size();
        
        // Calculate volume
        m_waterVolume = m_area * m_averageDepth;
    }
};

//=======================================================================================
// Water Analysis Result collection
//=======================================================================================
class WaterAnalysisResult {
private:
    std::vector<std::shared_ptr<WaterAnalysisResultItem>> m_items;
    double m_totalWaterVolume;

public:
    WaterAnalysisResult() : m_totalWaterVolume(0.0) {}
    
    void AddItem(std::shared_ptr<WaterAnalysisResultItem> item) {
        m_items.push_back(item);
        m_totalWaterVolume += item->GetWaterVolume();
    }
    
    const std::vector<std::shared_ptr<WaterAnalysisResultItem>>& GetItems() const { return m_items; }
    size_t GetCount() const { return m_items.size(); }
    double GetTotalWaterVolume() const { return m_totalWaterVolume; }
    
    std::vector<std::shared_ptr<WaterAnalysisResultPoint>> GetPoints() const {
        std::vector<std::shared_ptr<WaterAnalysisResultPoint>> points;
        for (const auto& item : m_items) {
            if (item->GetType() == WaterAnalysisResultType::Point) {
                points.push_back(std::static_pointer_cast<WaterAnalysisResultPoint>(item));
            }
        }
        return points;
    }
    
    std::vector<std::shared_ptr<WaterAnalysisResultStream>> GetStreams() const {
        std::vector<std::shared_ptr<WaterAnalysisResultStream>> streams;
        for (const auto& item : m_items) {
            if (item->GetType() == WaterAnalysisResultType::Stream) {
                streams.push_back(std::static_pointer_cast<WaterAnalysisResultStream>(item));
            }
        }
        return streams;
    }
    
    std::vector<std::shared_ptr<WaterAnalysisResultPond>> GetPonds() const {
        std::vector<std::shared_ptr<WaterAnalysisResultPond>> ponds;
        for (const auto& item : m_items) {
            if (item->GetType() == WaterAnalysisResultType::Pond) {
                ponds.push_back(std::static_pointer_cast<WaterAnalysisResultPond>(item));
            }
        }
        return ponds;
    }
    
    void Clear() {
        m_items.clear();
        m_totalWaterVolume = 0.0;
    }
};

//=======================================================================================
// Main Water Analysis class
//=======================================================================================
class WaterAnalysis {
private:
    DTMPtr m_dtm;
    ZeroSlopeTraceOption m_zeroSlopeOption;
    WaterAnalysisResult m_result;
    
    // Analysis parameters
    double m_minimumFlowLength;
    double m_minimumPondArea;
    double m_flowAccumulation;
    bool m_useFlowAccumulation;

public:
    //=======================================================================================
    // Constructors and Destructor
    //=======================================================================================
    explicit WaterAnalysis(DTMPtr dtm);
    ~WaterAnalysis() = default;
    
    // Non-copyable
    WaterAnalysis(const WaterAnalysis&) = delete;
    WaterAnalysis& operator=(const WaterAnalysis&) = delete;

    //=======================================================================================
    // Configuration
    //=======================================================================================
    ZeroSlopeTraceOption GetZeroSlopeTraceOption() const { return m_zeroSlopeOption; }
    void SetZeroSlopeTraceOption(ZeroSlopeTraceOption option) { m_zeroSlopeOption = option; }
    
    double GetMinimumFlowLength() const { return m_minimumFlowLength; }
    void SetMinimumFlowLength(double length) { m_minimumFlowLength = length; }
    
    double GetMinimumPondArea() const { return m_minimumPondArea; }
    void SetMinimumPondArea(double area) { m_minimumPondArea = area; }
    
    //=======================================================================================
    // Analysis operations
    //=======================================================================================
    WaterAnalysisResult DoTrace(const Point3D& startPoint);
    void DoTraceAsync(const Point3D& startPoint, std::function<void(const WaterAnalysisResult&)> callback);
    void AddWaterVolume(double volume);
    const WaterAnalysisResult& GetResult() const { return m_result; }
    
    //=======================================================================================
    // Advanced analysis
    //=======================================================================================
    std::vector<Point3D> TraceFlowPath(const Point3D& startPoint);
    std::vector<Point3D> FindWatersheds();
    std::vector<Point3D> FindDrainageNetwork();
    std::vector<Point3D> FindLowPoints();
    
    //=======================================================================================
    // Flow accumulation analysis
    //=======================================================================================
    void CalculateFlowAccumulation();
    double GetFlowAccumulationAt(const Point3D& point) const;
    std::vector<Point3D> GetFlowDirections() const;
    
    //=======================================================================================
    // Utility operations
    //=======================================================================================
    void Reset();
    std::shared_ptr<WaterAnalysis> Clone() const;

private:
    //=======================================================================================
    // Internal analysis methods
    //=======================================================================================
    std::vector<Point3D> TraceDownhill(const Point3D& startPoint);
    Point3D FindSteepestDescentDirection(const Point3D& point);
    bool IsLocalMinimum(const Point3D& point);
    std::vector<Point3D> FindPondBoundary(const Point3D& lowPoint);
    
    //=======================================================================================
    // Flow calculation helpers
    //=======================================================================================
    double CalculateSlope(const Point3D& from, const Point3D& to);
    Point3D GetFlowDirection(const Point3D& point);
    std::vector<Point3D> GetNeighborPoints(const Point3D& point, double radius = 1.0);
    
    //=======================================================================================
    // Validation helpers
    //=======================================================================================
    bool IsValidStartPoint(const Point3D& point) const;
    bool IsWithinDTMBounds(const Point3D& point) const;
};

} // namespace TerrainModel
