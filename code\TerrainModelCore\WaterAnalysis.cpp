/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "WaterAnalysis.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <queue>
#include <stack>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// WaterFlowResult implementation
//=======================================================================================
WaterFlowResult WaterFlowResult::Success(const std::string& message) {
    WaterFlowResult result;
    result.success = true;
    result.message = message;
    return result;
}

WaterFlowResult WaterFlowResult::Failure(const std::string& message) {
    WaterFlowResult result;
    result.success = false;
    result.message = message;
    return result;
}

//=======================================================================================
// WaterAnalysis implementation
//=======================================================================================
WaterAnalysis::WaterAnalysis(DTMPtr dtm)
    : m_dtm(dtm)
    , m_flowThreshold(0.001)
    , m_maxIterations(10000)
    , m_convergenceTolerance(1e-6)
{
    if (!m_dtm) {
        throw std::invalid_argument("DTM cannot be null");
    }
    
    if (!m_dtm->IsTriangulated()) {
        throw std::runtime_error("DTM must be triangulated for water analysis");
    }
    
    InitializeFlowNetwork();
}

WaterAnalysis::~WaterAnalysis() = default;

//=======================================================================================
// Flow tracing operations
//=======================================================================================
WaterFlowResult WaterAnalysis::TraceFlow(const Point3D& startPoint) {
    WaterFlowResult result;
    
    try {
        // Find starting triangle
        DrapedPoint drapedStart = m_dtm->DrapePoint(startPoint);
        if (!drapedStart.isValid) {
            return WaterFlowResult::Failure("Start point is outside DTM bounds");
        }
        
        // Initialize flow path
        FlowPath flowPath;
        flowPath.startPoint = drapedStart.drapedPoint;
        flowPath.points.push_back(drapedStart.drapedPoint);
        
        Point3D currentPoint = drapedStart.drapedPoint;
        DTMTriangleId currentTriangle = drapedStart.triangleId;
        
        // Trace flow until we reach a sink or boundary
        for (int iteration = 0; iteration < m_maxIterations; ++iteration) {
            Point3D nextPoint;
            DTMTriangleId nextTriangle;
            
            if (!FindNextFlowPoint(currentPoint, currentTriangle, nextPoint, nextTriangle)) {
                // Reached a sink or boundary
                break;
            }
            
            // Check for convergence (very small movement)
            double distance = GeometryUtils::Distance3D(currentPoint, nextPoint);
            if (distance < m_convergenceTolerance) {
                break;
            }
            
            flowPath.points.push_back(nextPoint);
            flowPath.totalLength += distance;
            flowPath.elevationDrop += std::max(0.0, currentPoint.z - nextPoint.z);
            
            currentPoint = nextPoint;
            currentTriangle = nextTriangle;
        }
        
        flowPath.endPoint = currentPoint;
        
        // Calculate flow characteristics
        CalculateFlowCharacteristics(flowPath);
        
        result.flowPaths.push_back(flowPath);
        result.totalFlowLength = flowPath.totalLength;
        result.totalElevationDrop = flowPath.elevationDrop;
        result.success = true;
        result.message = "Flow tracing completed successfully";
        
    } catch (const std::exception& e) {
        result = WaterFlowResult::Failure("Flow tracing failed: " + std::string(e.what()));
    }
    
    return result;
}

WaterFlowResult WaterAnalysis::TraceMultipleFlows(const std::vector<Point3D>& startPoints) {
    WaterFlowResult result;
    result.success = true;
    result.message = "Multiple flow tracing completed";
    
    for (const auto& startPoint : startPoints) {
        WaterFlowResult singleResult = TraceFlow(startPoint);
        
        if (singleResult.success) {
            result.flowPaths.insert(result.flowPaths.end(), 
                                   singleResult.flowPaths.begin(), 
                                   singleResult.flowPaths.end());
            result.totalFlowLength += singleResult.totalFlowLength;
            result.totalElevationDrop += singleResult.totalElevationDrop;
        } else {
            result.warnings.push_back("Failed to trace flow from point: " + singleResult.message);
        }
    }
    
    return result;
}

//=======================================================================================
// Watershed analysis
//=======================================================================================
WaterFlowResult WaterAnalysis::AnalyzeWatershed(const Point3D& outletPoint) {
    WaterFlowResult result;
    
    try {
        // Find outlet triangle
        DrapedPoint drapedOutlet = m_dtm->DrapePoint(outletPoint);
        if (!drapedOutlet.isValid) {
            return WaterFlowResult::Failure("Outlet point is outside DTM bounds");
        }
        
        // Initialize watershed analysis
        Watershed watershed;
        watershed.outletPoint = drapedOutlet.drapedPoint;
        watershed.outletTriangle = drapedOutlet.triangleId;
        
        // Use flood-fill algorithm to find contributing area
        std::set<DTMTriangleId> visitedTriangles;
        std::queue<DTMTriangleId> trianglesToProcess;
        
        trianglesToProcess.push(drapedOutlet.triangleId);
        visitedTriangles.insert(drapedOutlet.triangleId);
        
        while (!trianglesToProcess.empty()) {
            DTMTriangleId currentTriangle = trianglesToProcess.front();
            trianglesToProcess.pop();
            
            // Check if this triangle contributes flow to the watershed
            if (TriangleContributesToWatershed(currentTriangle, watershed.outletPoint)) {
                watershed.contributingTriangles.push_back(currentTriangle);
                
                // Add adjacent triangles to processing queue
                std::vector<DTMTriangleId> adjacentTriangles = GetAdjacentTriangles(currentTriangle);
                for (DTMTriangleId adjTriangle : adjacentTriangles) {
                    if (visitedTriangles.find(adjTriangle) == visitedTriangles.end()) {
                        trianglesToProcess.push(adjTriangle);
                        visitedTriangles.insert(adjTriangle);
                    }
                }
            }
        }
        
        // Calculate watershed properties
        CalculateWatershedProperties(watershed);
        
        result.watersheds.push_back(watershed);
        result.success = true;
        result.message = "Watershed analysis completed successfully";
        
    } catch (const std::exception& e) {
        result = WaterFlowResult::Failure("Watershed analysis failed: " + std::string(e.what()));
    }
    
    return result;
}

//=======================================================================================
// Drainage network analysis
//=======================================================================================
WaterFlowResult WaterAnalysis::AnalyzeDrainageNetwork() {
    WaterFlowResult result;
    
    try {
        // Find all local minima (potential sinks)
        std::vector<Point3D> sinks = FindLocalMinima();
        
        // Create drainage network
        DrainageNetwork network;
        
        // For each sink, trace upstream to find contributing channels
        for (const auto& sink : sinks) {
            DrainageChannel channel;
            channel.outletPoint = sink;
            
            // Find upstream channels flowing to this sink
            std::vector<Point3D> upstreamPoints = FindUpstreamPoints(sink);
            
            for (const auto& upstreamPoint : upstreamPoints) {
                WaterFlowResult flowResult = TraceFlow(upstreamPoint);
                if (flowResult.success && !flowResult.flowPaths.empty()) {
                    const FlowPath& flowPath = flowResult.flowPaths[0];
                    
                    // Check if this flow path reaches the sink
                    double distanceToSink = GeometryUtils::Distance3D(flowPath.endPoint, sink);
                    if (distanceToSink < m_flowThreshold * 10) {
                        channel.flowPaths.push_back(flowPath);
                        channel.totalLength += flowPath.totalLength;
                        channel.contributingArea += CalculateContributingArea(flowPath);
                    }
                }
            }
            
            if (!channel.flowPaths.empty()) {
                network.channels.push_back(channel);
            }
        }
        
        // Calculate network properties
        CalculateNetworkProperties(network);
        
        result.drainageNetworks.push_back(network);
        result.success = true;
        result.message = "Drainage network analysis completed successfully";
        
    } catch (const std::exception& e) {
        result = WaterFlowResult::Failure("Drainage network analysis failed: " + std::string(e.what()));
    }
    
    return result;
}

//=======================================================================================
// Flow accumulation
//=======================================================================================
WaterFlowResult WaterAnalysis::CalculateFlowAccumulation() {
    WaterFlowResult result;
    
    try {
        // Initialize flow accumulation for all triangles
        const auto& triangles = m_dtm->GetTriangles();
        
        for (const auto& pair : triangles) {
            DTMTriangleId triangleId = pair.first;
            const auto& triangle = pair.second;
            
            if (!triangle->IsActive()) {
                continue;
            }
            
            // Calculate area of triangle
            double area = triangle->GetArea(m_dtm->GetVertices());
            
            // Initialize with unit area (self-contribution)
            m_flowAccumulation[triangleId] = area;
        }
        
        // Sort triangles by elevation (highest first)
        std::vector<std::pair<double, DTMTriangleId>> elevationTriangles;
        
        for (const auto& pair : triangles) {
            DTMTriangleId triangleId = pair.first;
            const auto& triangle = pair.second;
            
            if (!triangle->IsActive()) {
                continue;
            }
            
            // Get average elevation of triangle
            Point3D centroid = triangle->GetCentroid(m_dtm->GetVertices());
            elevationTriangles.emplace_back(centroid.z, triangleId);
        }
        
        // Sort by elevation (descending)
        std::sort(elevationTriangles.begin(), elevationTriangles.end(), 
                 [](const auto& a, const auto& b) { return a.first > b.first; });
        
        // Process triangles from highest to lowest elevation
        for (const auto& pair : elevationTriangles) {
            DTMTriangleId triangleId = pair.second;
            
            // Find downstream triangle
            DTMTriangleId downstreamTriangle = FindDownstreamTriangle(triangleId);
            
            if (downstreamTriangle != Constants::INVALID_ID) {
                // Add this triangle's accumulation to downstream triangle
                m_flowAccumulation[downstreamTriangle] += m_flowAccumulation[triangleId];
            }
        }
        
        result.success = true;
        result.message = "Flow accumulation calculated successfully";
        
    } catch (const std::exception& e) {
        result = WaterFlowResult::Failure("Flow accumulation calculation failed: " + std::string(e.what()));
    }
    
    return result;
}

//=======================================================================================
// Internal helper methods
//=======================================================================================
void WaterAnalysis::InitializeFlowNetwork() {
    // Initialize flow direction and accumulation maps
    m_flowDirections.clear();
    m_flowAccumulation.clear();
    
    const auto& triangles = m_dtm->GetTriangles();
    
    for (const auto& pair : triangles) {
        DTMTriangleId triangleId = pair.first;
        const auto& triangle = pair.second;
        
        if (!triangle->IsActive()) {
            continue;
        }
        
        // Calculate flow direction for this triangle
        Point3D flowDirection = CalculateFlowDirection(triangleId);
        m_flowDirections[triangleId] = flowDirection;
        
        // Initialize flow accumulation
        m_flowAccumulation[triangleId] = 0.0;
    }
}

bool WaterAnalysis::FindNextFlowPoint(const Point3D& currentPoint, DTMTriangleId currentTriangle, 
                                     Point3D& nextPoint, DTMTriangleId& nextTriangle) {
    
    // Get flow direction for current triangle
    auto flowIt = m_flowDirections.find(currentTriangle);
    if (flowIt == m_flowDirections.end()) {
        return false;
    }
    
    Point3D flowDirection = flowIt->second;
    
    // Calculate next point along flow direction
    double stepSize = 1.0; // Adjust based on triangle size
    nextPoint = Point3D(
        currentPoint.x + flowDirection.x * stepSize,
        currentPoint.y + flowDirection.y * stepSize,
        currentPoint.z // Will be updated by draping
    );
    
    // Drape the next point onto the surface
    DrapedPoint drapedNext = m_dtm->DrapePoint(nextPoint);
    if (!drapedNext.isValid) {
        return false; // Reached boundary
    }
    
    nextPoint = drapedNext.drapedPoint;
    nextTriangle = drapedNext.triangleId;
    
    return true;
}

Point3D WaterAnalysis::CalculateFlowDirection(DTMTriangleId triangleId) {
    const auto& triangles = m_dtm->GetTriangles();
    auto triangleIt = triangles.find(triangleId);
    
    if (triangleIt == triangles.end()) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    const auto& triangle = triangleIt->second;
    const auto& vertices = m_dtm->GetVertices();
    
    // Get triangle normal
    Point3D normal = triangle->GetNormal(vertices);
    
    // Flow direction is the steepest descent direction
    // This is the negative gradient in the XY plane
    Point3D flowDirection(-normal.x, -normal.y, 0.0);
    
    // Normalize the flow direction
    return GeometryUtils::Normalize(flowDirection);
}

void WaterAnalysis::CalculateFlowCharacteristics(FlowPath& flowPath) {
    if (flowPath.points.size() < 2) {
        return;
    }
    
    // Calculate average slope
    double totalElevationChange = flowPath.startPoint.z - flowPath.endPoint.z;
    if (flowPath.totalLength > 0) {
        flowPath.averageSlope = totalElevationChange / flowPath.totalLength;
    }
    
    // Calculate sinuosity (path length / straight line distance)
    double straightDistance = GeometryUtils::Distance3D(flowPath.startPoint, flowPath.endPoint);
    if (straightDistance > 0) {
        flowPath.sinuosity = flowPath.totalLength / straightDistance;
    }
    
    // Find steepest segment
    double maxSlope = 0.0;
    for (size_t i = 1; i < flowPath.points.size(); ++i) {
        double segmentLength = GeometryUtils::Distance3D(flowPath.points[i-1], flowPath.points[i]);
        double elevationDiff = flowPath.points[i-1].z - flowPath.points[i].z;
        
        if (segmentLength > 0) {
            double slope = elevationDiff / segmentLength;
            maxSlope = std::max(maxSlope, slope);
        }
    }
    flowPath.maxSlope = maxSlope;
}

std::vector<Point3D> WaterAnalysis::FindLocalMinima() {
    std::vector<Point3D> minima;
    
    const auto& vertices = m_dtm->GetVertices();
    
    for (const auto& pair : vertices) {
        const auto& vertex = pair.second;
        
        if (!vertex->IsActive()) {
            continue;
        }
        
        Point3D vertexPos = vertex->GetPosition();
        bool isLocalMinimum = true;
        
        // Check all adjacent vertices
        const auto& adjacentTriangles = vertex->GetAdjacentTriangles();
        
        for (DTMTriangleId triangleId : adjacentTriangles) {
            // Get other vertices of this triangle
            const auto& triangles = m_dtm->GetTriangles();
            auto triangleIt = triangles.find(triangleId);
            
            if (triangleIt != triangles.end()) {
                const auto& triangle = triangleIt->second;
                
                for (int i = 0; i < 3; ++i) {
                    DTMVertexId otherVertexId = triangle->GetVertexId(i);
                    if (otherVertexId != pair.first) {
                        auto otherVertexIt = vertices.find(otherVertexId);
                        if (otherVertexIt != vertices.end()) {
                            Point3D otherPos = otherVertexIt->second->GetPosition();
                            if (otherPos.z <= vertexPos.z) {
                                isLocalMinimum = false;
                                break;
                            }
                        }
                    }
                }
                
                if (!isLocalMinimum) {
                    break;
                }
            }
        }
        
        if (isLocalMinimum) {
            minima.push_back(vertexPos);
        }
    }
    
    return minima;
}

std::vector<Point3D> WaterAnalysis::FindUpstreamPoints(const Point3D& sinkPoint) {
    std::vector<Point3D> upstreamPoints;
    
    // Simple implementation: find points at higher elevation within a reasonable distance
    const auto& vertices = m_dtm->GetVertices();
    double searchRadius = 100.0; // Adjust based on DTM size
    
    for (const auto& pair : vertices) {
        const auto& vertex = pair.second;
        
        if (!vertex->IsActive()) {
            continue;
        }
        
        Point3D vertexPos = vertex->GetPosition();
        
        // Check if point is upstream (higher elevation and within search radius)
        if (vertexPos.z > sinkPoint.z) {
            double distance = GeometryUtils::Distance2D(vertexPos, sinkPoint);
            if (distance <= searchRadius) {
                upstreamPoints.push_back(vertexPos);
            }
        }
    }
    
    return upstreamPoints;
}

double WaterAnalysis::CalculateContributingArea(const FlowPath& flowPath) {
    // Simple estimation based on flow path length and average triangle size
    // A more sophisticated implementation would calculate the actual contributing area
    
    if (flowPath.points.empty()) {
        return 0.0;
    }
    
    // Estimate average triangle size
    double avgTriangleArea = 0.0;
    size_t triangleCount = 0;
    
    const auto& triangles = m_dtm->GetTriangles();
    for (const auto& pair : triangles) {
        if (pair.second->IsActive()) {
            avgTriangleArea += pair.second->GetArea(m_dtm->GetVertices());
            triangleCount++;
        }
    }
    
    if (triangleCount > 0) {
        avgTriangleArea /= triangleCount;
    }
    
    // Estimate contributing area as flow length times average width
    double estimatedWidth = std::sqrt(avgTriangleArea);
    return flowPath.totalLength * estimatedWidth;
}

bool WaterAnalysis::TriangleContributesToWatershed(DTMTriangleId triangleId, const Point3D& outletPoint) {
    // Simple check: if triangle centroid is higher than outlet, it potentially contributes
    const auto& triangles = m_dtm->GetTriangles();
    auto triangleIt = triangles.find(triangleId);
    
    if (triangleIt == triangles.end()) {
        return false;
    }
    
    Point3D centroid = triangleIt->second->GetCentroid(m_dtm->GetVertices());
    return centroid.z > outletPoint.z;
}

void WaterAnalysis::CalculateWatershedProperties(Watershed& watershed) {
    watershed.area = 0.0;
    watershed.perimeter = 0.0;
    
    const auto& vertices = m_dtm->GetVertices();
    
    // Calculate total area
    for (DTMTriangleId triangleId : watershed.contributingTriangles) {
        const auto& triangles = m_dtm->GetTriangles();
        auto triangleIt = triangles.find(triangleId);
        
        if (triangleIt != triangles.end()) {
            watershed.area += triangleIt->second->GetArea(vertices);
        }
    }
    
    // TODO: Calculate perimeter by finding boundary edges
    // TODO: Calculate other watershed characteristics (shape factor, etc.)
}

void WaterAnalysis::CalculateNetworkProperties(DrainageNetwork& network) {
    network.totalLength = 0.0;
    network.totalArea = 0.0;
    network.drainageDensity = 0.0;
    
    for (const auto& channel : network.channels) {
        network.totalLength += channel.totalLength;
        network.totalArea += channel.contributingArea;
    }
    
    if (network.totalArea > 0) {
        network.drainageDensity = network.totalLength / network.totalArea;
    }
}

std::vector<DTMTriangleId> WaterAnalysis::GetAdjacentTriangles(DTMTriangleId triangleId) {
    std::vector<DTMTriangleId> adjacentTriangles;
    
    const auto& triangles = m_dtm->GetTriangles();
    auto triangleIt = triangles.find(triangleId);
    
    if (triangleIt != triangles.end()) {
        const auto& triangle = triangleIt->second;
        
        for (int i = 0; i < 3; ++i) {
            DTMTriangleId adjTriangle = triangle->GetAdjacentTriangleId(i);
            if (adjTriangle != Constants::INVALID_ID) {
                adjacentTriangles.push_back(adjTriangle);
            }
        }
    }
    
    return adjacentTriangles;
}

DTMTriangleId WaterAnalysis::FindDownstreamTriangle(DTMTriangleId triangleId) {
    // Find the adjacent triangle with the lowest elevation centroid
    std::vector<DTMTriangleId> adjacentTriangles = GetAdjacentTriangles(triangleId);
    
    if (adjacentTriangles.empty()) {
        return Constants::INVALID_ID;
    }
    
    DTMTriangleId downstreamTriangle = Constants::INVALID_ID;
    double lowestElevation = std::numeric_limits<double>::max();
    
    const auto& triangles = m_dtm->GetTriangles();
    const auto& vertices = m_dtm->GetVertices();
    
    for (DTMTriangleId adjTriangle : adjacentTriangles) {
        auto triangleIt = triangles.find(adjTriangle);
        if (triangleIt != triangles.end()) {
            Point3D centroid = triangleIt->second->GetCentroid(vertices);
            if (centroid.z < lowestElevation) {
                lowestElevation = centroid.z;
                downstreamTriangle = adjTriangle;
            }
        }
    }
    
    return downstreamTriangle;
}

} // namespace TerrainModel
