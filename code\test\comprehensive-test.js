/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const TerrainModel = require('../index');
const assert = require('assert');
const path = require('path');

// Comprehensive test suite for TerrainModel Node.js addon
describe('TerrainModel Node.js Addon', function() {
  this.timeout(30000); // 30 second timeout for complex operations

  let dtm;
  
  beforeEach(function() {
    dtm = TerrainModel.createDTM(1000, 500);
  });

  afterEach(function() {
    if (dtm && !dtm.disposed) {
      dtm.dispose();
    }
  });

  describe('Core DTM Operations', function() {
    it('should create a DTM successfully', function() {
      assert(dtm, 'DTM should be created');
      assert.strictEqual(dtm.getVerticesCount(), 0, 'Initial vertex count should be 0');
      assert.strictEqual(dtm.isTriangulated, false, 'DTM should not be triangulated initially');
    });

    it('should add point features', function() {
      const point = TerrainModel.createPoint3D(100, 100, 150);
      const featureId = dtm.addPointFeature(point, 1);
      assert(typeof featureId === 'number', 'Feature ID should be a number');
      assert(featureId > 0, 'Feature ID should be positive');
    });

    it('should add multiple points', function() {
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(50, 100, 110),
        TerrainModel.createPoint3D(100, 100, 115)
      ];
      
      dtm.addPoints(points);
      assert(dtm.getVerticesCount() >= points.length, 'Vertex count should include added points');
    });

    it('should triangulate successfully', function() {
      // Add minimum points for triangulation
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(50, 100, 110)
      ];
      
      dtm.addPoints(points);
      const result = dtm.triangulate();
      
      assert(result.success, 'Triangulation should succeed');
      assert(result.trianglesCount > 0, 'Should have triangles after triangulation');
      assert(dtm.isTriangulated, 'DTM should be marked as triangulated');
    });

    it('should drape points correctly', function() {
      // Setup triangulated DTM
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(50, 100, 110),
        TerrainModel.createPoint3D(100, 100, 115)
      ];
      
      dtm.addPoints(points);
      dtm.triangulate();
      
      const testPoint = TerrainModel.createPoint3D(50, 50, 0);
      const drapedPoint = dtm.drapePoint(testPoint);
      
      assert(drapedPoint, 'Draped point should be returned');
      assert(drapedPoint.isValid, 'Draped point should be valid');
      assert(drapedPoint.drapedPoint.z > 0, 'Draped elevation should be positive');
    });
  });

  describe('Advanced DTM Features', function() {
    beforeEach(function() {
      // Setup a triangulated DTM for advanced tests
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(200, 0, 110),
        TerrainModel.createPoint3D(0, 100, 108),
        TerrainModel.createPoint3D(100, 100, 112),
        TerrainModel.createPoint3D(200, 100, 118),
        TerrainModel.createPoint3D(50, 50, 107),
        TerrainModel.createPoint3D(150, 50, 114)
      ];
      
      dtm.addPoints(points);
      dtm.triangulate();
    });

    it('should create and use DTM TIN Editor', function() {
      const tinEditor = new TerrainModel.DTMTinEditor(dtm);
      assert(tinEditor, 'TIN Editor should be created');
      
      const featurePoints = [
        TerrainModel.createPoint3D(25, 25, 106),
        TerrainModel.createPoint3D(75, 75, 111)
      ];
      
      tinEditor.setFeaturePoints(featurePoints);
      const retrievedPoints = tinEditor.getFeaturePoints();
      assert.strictEqual(retrievedPoints.length, featurePoints.length, 'Feature points should match');
      
      tinEditor.dispose();
    });

    it('should perform water analysis', function() {
      const waterAnalysis = new TerrainModel.WaterAnalysis(dtm);
      assert(waterAnalysis, 'Water analysis should be created');
      
      waterAnalysis.zeroSlopeTraceOption = TerrainModel.ZeroSlopeTraceOption.Pond;
      
      const startPoint = TerrainModel.createPoint3D(50, 50, 0);
      const result = waterAnalysis.doTrace(startPoint);
      
      assert(result, 'Water analysis result should be returned');
      assert(typeof result.totalWaterVolume === 'number', 'Total water volume should be a number');
      
      waterAnalysis.dispose();
    });

    it('should design ponds', function() {
      const pondDesign = new TerrainModel.DTMPond(dtm);
      assert(pondDesign, 'Pond design should be created');
      
      const boundary = [
        TerrainModel.createPoint3D(40, 40, 0),
        TerrainModel.createPoint3D(60, 40, 0),
        TerrainModel.createPoint3D(60, 60, 0),
        TerrainModel.createPoint3D(40, 60, 0)
      ];
      
      pondDesign.pondBoundary = boundary;
      const result = pondDesign.calculatePondByElevation(110);
      
      assert(result, 'Pond calculation result should be returned');
      assert(typeof result.volume === 'number', 'Pond volume should be a number');
      
      pondDesign.dispose();
    });

    it('should use caching for performance', function() {
      const dtmCaching = new TerrainModel.DTMCaching(dtm);
      assert(dtmCaching, 'DTM Caching should be created');
      
      dtmCaching.cachingEnabled = true;
      assert(dtmCaching.cachingEnabled, 'Caching should be enabled');
      
      const stats = dtmCaching.getCacheStatistics();
      assert(stats, 'Cache statistics should be returned');
      assert(typeof stats.totalMemoryUsage === 'number', 'Memory usage should be a number');
      
      dtmCaching.dispose();
    });
  });

  describe('Feature Management', function() {
    it('should enumerate features', function() {
      // Add some features
      dtm.addPointFeature(TerrainModel.createPoint3D(0, 0, 100), 1);
      dtm.addPointFeature(TerrainModel.createPoint3D(100, 100, 110), 2);
      
      const breaklinePoints = [
        TerrainModel.createPoint3D(0, 50, 105),
        TerrainModel.createPoint3D(100, 50, 108)
      ];
      dtm.addLinearFeature(breaklinePoints, TerrainModel.DTMFeatureType.BreakLine, 10);
      
      const enumerator = new TerrainModel.DTMFeatureEnumerator(dtm);
      enumerator.includeAllFeatures();
      
      let featureCount = 0;
      while (enumerator.moveNext()) {
        const feature = enumerator.current();
        if (feature) {
          featureCount++;
          assert(typeof feature.featureId === 'number', 'Feature ID should be a number');
          assert(typeof feature.featureType === 'number', 'Feature type should be a number');
        }
      }
      
      assert(featureCount > 0, 'Should enumerate at least one feature');
      enumerator.dispose();
    });

    it('should delete features by ID', function() {
      const featureId = dtm.addPointFeature(TerrainModel.createPoint3D(50, 50, 105), 5);
      
      const feature = dtm.getFeatureById(featureId);
      assert(feature, 'Feature should exist before deletion');
      
      dtm.deleteFeatureById(featureId);
      
      const deletedFeature = dtm.getFeatureById(featureId);
      assert(!deletedFeature, 'Feature should not exist after deletion');
    });
  });

  describe('Analysis Operations', function() {
    beforeEach(function() {
      // Create a more complex terrain for analysis
      const points = [];
      for (let x = 0; x <= 200; x += 25) {
        for (let y = 0; y <= 200; y += 25) {
          const z = 100 + Math.sin(x / 50) * 10 + Math.cos(y / 50) * 5;
          points.push(TerrainModel.createPoint3D(x, y, z));
        }
      }
      
      dtm.addPoints(points);
      dtm.triangulate();
    });

    it('should calculate slope area', function() {
      const slopeArea = dtm.calculateSlopeArea();
      
      assert(slopeArea, 'Slope area result should be returned');
      assert(typeof slopeArea.area === 'number', 'Area should be a number');
      assert(typeof slopeArea.slopeArea === 'number', 'Slope area should be a number');
      assert(slopeArea.area > 0, 'Area should be positive');
    });

    it('should calculate volume', function() {
      const volume = dtm.calculateVolume(105);
      
      assert(typeof volume === 'number', 'Volume should be a number');
    });

    it('should get DTM range', function() {
      const range = dtm.getRange3d();
      
      assert(range, 'Range should be returned');
      assert(range.low, 'Range should have low point');
      assert(range.high, 'Range should have high point');
      assert(range.low.x <= range.high.x, 'Low X should be <= High X');
      assert(range.low.y <= range.high.y, 'Low Y should be <= High Y');
      assert(range.low.z <= range.high.z, 'Low Z should be <= High Z');
    });
  });

  describe('File Operations', function() {
    it('should save and load DTM files', function() {
      // Add some data
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(50, 100, 110)
      ];
      
      dtm.addPoints(points);
      dtm.triangulate();
      
      const testFileName = path.join(__dirname, 'test_dtm.tin');
      
      // Save DTM
      dtm.save(testFileName);
      
      // Load DTM
      const loadedDTM = TerrainModel.DTM.createFromFile(testFileName);
      assert(loadedDTM, 'Loaded DTM should exist');
      assert(loadedDTM.isTriangulated, 'Loaded DTM should be triangulated');
      
      loadedDTM.dispose();
      
      // Clean up test file
      try {
        require('fs').unlinkSync(testFileName);
      } catch (e) {
        // Ignore cleanup errors
      }
    });
  });

  describe('Error Handling', function() {
    it('should handle invalid operations gracefully', function() {
      assert.throws(() => {
        dtm.drapePoint(null);
      }, 'Should throw error for null point');
      
      assert.throws(() => {
        dtm.addPointFeature({ x: 'invalid', y: 0, z: 0 });
      }, 'Should throw error for invalid point');
    });

    it('should handle disposed objects', function() {
      dtm.dispose();
      
      assert.throws(() => {
        dtm.addPointFeature(TerrainModel.createPoint3D(0, 0, 100));
      }, 'Should throw error when using disposed DTM');
    });
  });
});

// Run tests if this file is executed directly
if (require.main === module) {
  console.log('Running TerrainModel comprehensive tests...');
  
  // Simple test runner
  const tests = [];
  global.describe = (name, fn) => tests.push({ name, fn });
  global.it = (name, fn) => ({ name, fn });
  global.beforeEach = (fn) => ({ beforeEach: fn });
  global.afterEach = (fn) => ({ afterEach: fn });
  
  // Load test definitions
  eval(require('fs').readFileSync(__filename, 'utf8'));
  
  // Run tests
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`\nRunning test suite: ${test.name}`);
      test.fn();
      console.log(`✓ ${test.name} passed`);
      passed++;
    } catch (error) {
      console.error(`✗ ${test.name} failed:`, error.message);
      failed++;
    }
  }
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  process.exit(failed > 0 ? 1 : 0);
}
