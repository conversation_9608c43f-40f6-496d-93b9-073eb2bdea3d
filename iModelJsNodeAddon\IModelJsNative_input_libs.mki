#---------------------------------------------------------------------------------------------
#  Copyright (c) Bentley Systems, Incorporated. All rights reserved.
#  See COPYRIGHT.md in the repository root for full copyright notice.
#---------------------------------------------------------------------------------------------
%ifdef __unix

THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBRepCore.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBaseGeoCoord.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeCsmapStatic.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeCurl.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeFolly.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeIcu4c.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeJpeg.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeJsonCpp.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeLibJpegTurbo.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeLibxml2.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBentley.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBentleyGeom.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBentleyGeomSerialization.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBePng.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeSQLite.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeSQLiteEC.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeXml.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libBeZlib.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libDgnPlatform.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libECObjects.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libECPresentation.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libfreetype2.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)liblzma.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libsnappy.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libUnits.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libLicensing.a
THIN_ARCHIVE_INPUTS +  $(ContextSubpartsLibs)libWebServicesClient.a
THIN_ARCHIVE_INPUTS  + $(ContextSubpartsLibs)libBeHttp.a
THIN_ARCHIVE_INPUTS  + $(ContextSubpartsLibs)libBeOpenSSL.a
THIN_ARCHIVE_INPUTS  + $(ContextSubpartsLibs)libBeSecurity.a

%if defined (BENTLEYCONFIG_PARASOLID)
    THIN_ARCHIVE_INPUTS + $(ContextSubPartsLibs)libpskernel.a
#%else
#    %error parasolid should be available on all platforms that support imodeljs addon
%endif

%if defined (USING_BREAKPAD)
%if $(USING_BREAKPAD) == 1
    THIN_ARCHIVE_INPUTS + $(ContextSubPartsLibs)libbreakpad_client.a
    THIN_ARCHIVE_INPUTS + $(ContextSubPartsLibs)libbreakpad.a
    THIN_ARCHIVE_INPUTS + $(ContextSubPartsLibs)libdisasm.a
%endif
%endif

%if defined(BENTLEYCONFIG_CRASHPAD)
    THIN_ARCHIVE_INPUTS + $(ContextSubPartsLibs)libCrashpadClient.a
%endif

%else

DLM_OBJECT_FILES + $(ContextSubpartsLibs)$(libprefix)BeJsonCpp$(stlibext)

LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)DgnPlatform$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BRepCore$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)ECPresentation$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)ECObjects$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)Units$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeSQLiteEC$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeSQLite$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeFolly$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BentleyGeomSerialization$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BentleyGeom$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BaseGeoCoord$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)Licensing$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)WebServicesClient$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeHttp$(stlibext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeOpenSSL$(libext)
LINKER_LIBRARIES  + $(ContextSubpartsLibs)$(libprefix)BeSecurity$(libext)

%if defined (USING_BREAKPAD)
%if $(USING_BREAKPAD) == 1
    LINKER_LIBRARIES  + $(ContextSubpartsLibs)libbreakpad.lib
    LINKER_LIBRARIES  + $(ContextSubpartsLibs)libbreakpad_client.lib
    LINKER_LIBRARIES  + $(ContextSubpartsLibs)libdisasm.lib
%endif
%endif

%if defined(BENTLEYCONFIG_CRASHPAD)
    LINKER_LIBRARIES + $(ContextSubPartsStaticLibs)$(stlibprefix)CrashpadClient$(stlibext)
%endif

%endif
