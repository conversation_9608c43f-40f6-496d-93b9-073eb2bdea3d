/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Utility class for terrain model operations and type conversions
//=======================================================================================
class TerrainModelUtils
{
public:
    // Initialization
    static void Initialize(Napi::Env env);
    static void Cleanup();

    // Factory methods exposed to JavaScript
    static Napi::Value CreateDTM(const Napi::CallbackInfo& info);
    static Napi::Value LoadDTMFromFile(const Napi::CallbackInfo& info);
    static Napi::Value SetLogLevel(const Napi::CallbackInfo& info);

    // Type conversion utilities
    static Point3D ConvertDPoint3dToPoint3D(const DPoint3d& point);
    static DPoint3d ConvertPoint3DToDPoint3d(const Point3D& point);
    static std::vector<Point3D> ConvertDPoint3dArrayToPoint3DVector(const DPoint3d* points, size_t count);
    static std::vector<DPoint3d> ConvertPoint3DVectorToDPoint3dVector(const std::vector<Point3D>& points);

    // JavaScript array conversions
    static std::vector<Point3D> ConvertJSArrayToPoint3DVector(Napi::Array jsArray);
    static Napi::Array ConvertPoint3DVectorToJSArray(Napi::Env env, const std::vector<Point3D>& points);
    static std::vector<double> ConvertJSArrayToDoubleVector(Napi::Array jsArray);
    static Napi::Array ConvertDoubleVectorToJSArray(Napi::Env env, const std::vector<double>& values);

    // Feature type conversions
    static DTMFeatureType ConvertJSValueToFeatureType(Napi::Value jsValue);
    static Napi::Value ConvertFeatureTypeToJSValue(Napi::Env env, DTMFeatureType featureType);
    static std::string GetFeatureTypeName(DTMFeatureType featureType);

    // Error handling utilities
    static void ThrowDTMException(Napi::Env env, DTMStatusInt status, const std::string& operation);
    static std::string GetDTMStatusMessage(DTMStatusInt status);
    static bool CheckDTMStatus(Napi::Env env, DTMStatusInt status, const std::string& operation);

    // Range and bounds utilities
    static Range3D ConvertDRange3dToRange3D(const DRange3d& range);
    static DRange3d ConvertRange3DToDRange3d(const Range3D& range);
    static Napi::Object ConvertRange3DToJSObject(Napi::Env env, const Range3D& range);
    static Range3D ConvertJSObjectToRange3D(Napi::Object jsObject);

    // Memory management utilities
    static void RegisterNativeObject(void* nativePtr, size_t estimatedSize);
    static void UnregisterNativeObject(void* nativePtr);
    static size_t GetTotalNativeMemoryUsage();

    // Validation utilities
    static bool IsValidPoint3D(const Point3D& point);
    static bool IsValidRange3D(const Range3D& range);
    static bool IsValidFeatureType(DTMFeatureType featureType);
    static bool IsValidUserTag(DTMUserTag userTag);

    // String utilities
    static std::string ConvertJSStringToStdString(Napi::Value jsString);
    static Napi::String ConvertStdStringToJSString(Napi::Env env, const std::string& str);
    static std::wstring ConvertStdStringToWString(const std::string& str);
    static std::string ConvertWStringToStdString(const std::wstring& wstr);

    // Async operation utilities
    template<typename T>
    static void ExecuteAsync(Napi::Env env, const std::string& resourceName, 
                           std::function<T()> execute, 
                           std::function<Napi::Value(Napi::Env, T)> complete,
                           Napi::Function callback);

    // Configuration and settings
    static void SetDefaultTriangulationParameters(double pointTol, double lineTol);
    static void GetDefaultTriangulationParameters(double& pointTol, double& lineTol);
    static void SetMemoryManagementEnabled(bool enabled);
    static bool IsMemoryManagementEnabled();

private:
    static bool s_initialized;
    static bool s_memoryManagementEnabled;
    static double s_defaultPointTolerance;
    static double s_defaultLineTolerance;
    static std::map<void*, size_t> s_nativeObjectRegistry;
    static std::mutex s_registryMutex;

    // Private helper methods
    static void InitializeLogging();
    static void InitializeTerrainModelLibrary();
    static void CleanupTerrainModelLibrary();
};

//=======================================================================================
// Template implementation for async operations
//=======================================================================================
template<typename T>
void TerrainModelUtils::ExecuteAsync(Napi::Env env, const std::string& resourceName,
                                    std::function<T()> execute,
                                    std::function<Napi::Value(Napi::Env, T)> complete,
                                    Napi::Function callback)
{
    class AsyncWorker : public Napi::AsyncWorker
    {
    public:
        AsyncWorker(Napi::Function& callback, const std::string& resourceName,
                   std::function<T()> execute, std::function<Napi::Value(Napi::Env, T)> complete)
            : Napi::AsyncWorker(callback), m_execute(execute), m_complete(complete) {}

        void Execute() override
        {
            try {
                m_result = m_execute();
            } catch (const std::exception& e) {
                SetError(e.what());
            }
        }

        void OnOK() override
        {
            Napi::HandleScope scope(Env());
            Napi::Value result = m_complete(Env(), m_result);
            Callback().Call({Env().Null(), result});
        }

    private:
        std::function<T()> m_execute;
        std::function<Napi::Value(Napi::Env, T)> m_complete;
        T m_result;
    };

    auto worker = new AsyncWorker(callback, resourceName, execute, complete);
    worker->Queue();
}

} // namespace TerrainModelNodeAddon
