/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const TerrainModel = require('../index');

// Benchmark utilities
class BenchmarkUtils {
  static formatTime(milliseconds) {
    if (milliseconds < 1) {
      return `${(milliseconds * 1000).toFixed(2)}μs`;
    } else if (milliseconds < 1000) {
      return `${milliseconds.toFixed(2)}ms`;
    } else {
      return `${(milliseconds / 1000).toFixed(2)}s`;
    }
  }
  
  static formatMemory(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)}${units[unitIndex]}`;
  }
  
  static createRandomPoints(count, bounds = { minX: 0, maxX: 1000, minY: 0, maxY: 1000, minZ: 100, maxZ: 200 }) {
    const points = [];
    for (let i = 0; i < count; i++) {
      points.push({
        x: bounds.minX + Math.random() * (bounds.maxX - bounds.minX),
        y: bounds.minY + Math.random() * (bounds.maxY - bounds.minY),
        z: bounds.minZ + Math.random() * (bounds.maxZ - bounds.minZ)
      });
    }
    return points;
  }
  
  static createGridPoints(width, height, spacing = 10, baseElevation = 100) {
    const points = [];
    for (let x = 0; x < width; x++) {
      for (let y = 0; y < height; y++) {
        const elevation = baseElevation + 
          Math.sin(x * 0.1) * 10 + 
          Math.cos(y * 0.1) * 5 + 
          Math.random() * 2;
        
        points.push({
          x: x * spacing,
          y: y * spacing,
          z: elevation
        });
      }
    }
    return points;
  }
  
  static measureMemory() {
    if (global.gc) {
      global.gc();
    }
    return process.memoryUsage();
  }
  
  static async benchmark(name, fn, iterations = 1) {
    console.log(`\nBenchmarking: ${name}`);
    
    const memBefore = this.measureMemory();
    const startTime = process.hrtime.bigint();
    
    let result;
    for (let i = 0; i < iterations; i++) {
      result = await fn();
    }
    
    const endTime = process.hrtime.bigint();
    const memAfter = this.measureMemory();
    
    const elapsedMs = Number(endTime - startTime) / 1000000 / iterations;
    const memDelta = memAfter.heapUsed - memBefore.heapUsed;
    
    console.log(`  Time: ${this.formatTime(elapsedMs)} (avg over ${iterations} iterations)`);
    console.log(`  Memory: ${this.formatMemory(memDelta)} delta`);
    console.log(`  Heap Used: ${this.formatMemory(memAfter.heapUsed)}`);
    
    return { elapsedMs, memDelta, result };
  }
}

// Benchmark test suite
class TerrainModelBenchmarks {
  constructor() {
    this.results = {};
  }
  
  async runAll() {
    console.log('TerrainModel Performance Benchmarks');
    console.log('====================================');
    
    await this.benchmarkDTMCreation();
    await this.benchmarkPointAddition();
    await this.benchmarkTriangulation();
    await this.benchmarkDraping();
    await this.benchmarkFeatureManagement();
    await this.benchmarkAdvancedOperations();
    
    this.printSummary();
  }
  
  async benchmarkDTMCreation() {
    console.log('\n--- DTM Creation Benchmarks ---');
    
    // Empty DTM creation
    this.results.emptyDTMCreation = await BenchmarkUtils.benchmark(
      'Empty DTM Creation',
      () => {
        const dtm = TerrainModel.createDTM();
        dtm.dispose();
        return dtm;
      },
      1000
    );
    
    // DTM with capacity
    this.results.dtmWithCapacity = await BenchmarkUtils.benchmark(
      'DTM Creation with Capacity (10000, 5000)',
      () => {
        const dtm = TerrainModel.createDTM(10000, 5000);
        dtm.dispose();
        return dtm;
      },
      100
    );
    
    // DTM from points
    const testPoints = BenchmarkUtils.createRandomPoints(1000);
    this.results.dtmFromPoints = await BenchmarkUtils.benchmark(
      'DTM Creation from 1000 Points',
      () => {
        const dtm = TerrainModel.createDTMFromPoints(testPoints);
        dtm.dispose();
        return dtm;
      },
      10
    );
  }
  
  async benchmarkPointAddition() {
    console.log('\n--- Point Addition Benchmarks ---');
    
    // Single point addition
    this.results.singlePointAddition = await BenchmarkUtils.benchmark(
      'Single Point Addition',
      () => {
        const dtm = TerrainModel.createDTM();
        const point = TerrainModel.createPoint3D(Math.random() * 100, Math.random() * 100, 100);
        const featureId = dtm.addPointFeature(point);
        dtm.dispose();
        return featureId;
      },
      1000
    );
    
    // Batch point addition
    const batchPoints = BenchmarkUtils.createRandomPoints(10000);
    this.results.batchPointAddition = await BenchmarkUtils.benchmark(
      'Batch Addition of 10000 Points',
      () => {
        const dtm = TerrainModel.createDTM();
        dtm.addPoints(batchPoints);
        const count = dtm.getVerticesCount();
        dtm.dispose();
        return count;
      },
      5
    );
  }
  
  async benchmarkTriangulation() {
    console.log('\n--- Triangulation Benchmarks ---');
    
    // Small triangulation
    const smallPoints = BenchmarkUtils.createGridPoints(10, 10);
    this.results.smallTriangulation = await BenchmarkUtils.benchmark(
      'Triangulation of 100 Points (10x10 grid)',
      () => {
        const dtm = TerrainModel.createDTM();
        dtm.addPoints(smallPoints);
        const result = dtm.triangulate();
        dtm.dispose();
        return result;
      },
      50
    );
    
    // Medium triangulation
    const mediumPoints = BenchmarkUtils.createGridPoints(50, 50);
    this.results.mediumTriangulation = await BenchmarkUtils.benchmark(
      'Triangulation of 2500 Points (50x50 grid)',
      () => {
        const dtm = TerrainModel.createDTM();
        dtm.addPoints(mediumPoints);
        const result = dtm.triangulate();
        dtm.dispose();
        return result;
      },
      10
    );
    
    // Large triangulation
    const largePoints = BenchmarkUtils.createRandomPoints(10000);
    this.results.largeTriangulation = await BenchmarkUtils.benchmark(
      'Triangulation of 10000 Random Points',
      () => {
        const dtm = TerrainModel.createDTM();
        dtm.addPoints(largePoints);
        const result = dtm.triangulate();
        dtm.dispose();
        return result;
      },
      3
    );
  }
  
  async benchmarkDraping() {
    console.log('\n--- Draping Benchmarks ---');
    
    // Setup triangulated DTM
    const setupPoints = BenchmarkUtils.createGridPoints(100, 100);
    const dtm = TerrainModel.createDTM();
    dtm.addPoints(setupPoints);
    dtm.triangulate();
    
    // Single point draping
    this.results.singlePointDraping = await BenchmarkUtils.benchmark(
      'Single Point Draping',
      () => {
        const testPoint = TerrainModel.createPoint3D(
          Math.random() * 990,
          Math.random() * 990,
          0
        );
        return dtm.drapePoint(testPoint);
      },
      10000
    );
    
    // Batch point draping
    const drapingPoints = BenchmarkUtils.createRandomPoints(1000, { minX: 0, maxX: 990, minY: 0, maxY: 990, minZ: 0, maxZ: 0 });
    this.results.batchPointDraping = await BenchmarkUtils.benchmark(
      'Batch Draping of 1000 Points',
      () => {
        return dtm.drapePoints(drapingPoints);
      },
      10
    );
    
    dtm.dispose();
  }
  
  async benchmarkFeatureManagement() {
    console.log('\n--- Feature Management Benchmarks ---');
    
    const dtm = TerrainModel.createDTM();
    
    // Feature addition
    this.results.featureAddition = await BenchmarkUtils.benchmark(
      'Adding 1000 Point Features',
      () => {
        const points = BenchmarkUtils.createRandomPoints(1000);
        const featureIds = [];
        for (const point of points) {
          featureIds.push(dtm.addPointFeature(point, Math.floor(Math.random() * 100)));
        }
        return featureIds;
      },
      5
    );
    
    // Feature enumeration
    this.results.featureEnumeration = await BenchmarkUtils.benchmark(
      'Enumerating All Features',
      () => {
        const enumerator = new TerrainModel.DTMFeatureEnumerator(dtm);
        enumerator.includeAllFeatures();
        
        let count = 0;
        while (enumerator.moveNext()) {
          const feature = enumerator.current();
          if (feature) count++;
        }
        
        enumerator.dispose();
        return count;
      },
      100
    );
    
    dtm.dispose();
  }
  
  async benchmarkAdvancedOperations() {
    console.log('\n--- Advanced Operations Benchmarks ---');
    
    // Setup complex DTM
    const complexPoints = BenchmarkUtils.createGridPoints(50, 50);
    const dtm = TerrainModel.createDTM();
    dtm.addPoints(complexPoints);
    dtm.triangulate();
    
    // Volume calculation
    this.results.volumeCalculation = await BenchmarkUtils.benchmark(
      'Volume Calculation',
      () => {
        return dtm.calculateVolume(120);
      },
      100
    );
    
    // Slope area calculation
    this.results.slopeAreaCalculation = await BenchmarkUtils.benchmark(
      'Slope Area Calculation',
      () => {
        return dtm.calculateSlopeArea();
      },
      50
    );
    
    // Statistics calculation
    this.results.statisticsCalculation = await BenchmarkUtils.benchmark(
      'Statistics Calculation',
      () => {
        return dtm.getStatistics();
      },
      100
    );
    
    dtm.dispose();
  }
  
  printSummary() {
    console.log('\n--- Performance Summary ---');
    console.log('Operation                          | Time        | Memory');
    console.log('-----------------------------------|-------------|-------------');
    
    const operations = [
      ['Empty DTM Creation', 'emptyDTMCreation'],
      ['DTM with Capacity', 'dtmWithCapacity'],
      ['DTM from Points', 'dtmFromPoints'],
      ['Single Point Addition', 'singlePointAddition'],
      ['Batch Point Addition', 'batchPointAddition'],
      ['Small Triangulation', 'smallTriangulation'],
      ['Medium Triangulation', 'mediumTriangulation'],
      ['Large Triangulation', 'largeTriangulation'],
      ['Single Point Draping', 'singlePointDraping'],
      ['Batch Point Draping', 'batchPointDraping'],
      ['Feature Addition', 'featureAddition'],
      ['Feature Enumeration', 'featureEnumeration'],
      ['Volume Calculation', 'volumeCalculation'],
      ['Slope Area Calculation', 'slopeAreaCalculation'],
      ['Statistics Calculation', 'statisticsCalculation']
    ];
    
    for (const [name, key] of operations) {
      const result = this.results[key];
      if (result) {
        const timeStr = BenchmarkUtils.formatTime(result.elapsedMs).padStart(11);
        const memStr = BenchmarkUtils.formatMemory(Math.abs(result.memDelta)).padStart(11);
        console.log(`${name.padEnd(34)} | ${timeStr} | ${memStr}`);
      }
    }
    
    console.log('\nBenchmark completed successfully!');
  }
}

// Memory leak detection
class MemoryLeakDetector {
  constructor() {
    this.initialMemory = null;
    this.samples = [];
  }
  
  start() {
    if (global.gc) global.gc();
    this.initialMemory = process.memoryUsage();
    this.samples = [this.initialMemory];
    console.log('Memory leak detection started');
  }
  
  sample(label = '') {
    if (global.gc) global.gc();
    const memory = process.memoryUsage();
    this.samples.push(memory);
    
    if (label) {
      const delta = memory.heapUsed - this.initialMemory.heapUsed;
      console.log(`Memory sample (${label}): ${BenchmarkUtils.formatMemory(delta)} delta`);
    }
  }
  
  analyze() {
    if (this.samples.length < 2) {
      console.log('Insufficient samples for leak detection');
      return false;
    }
    
    const initial = this.samples[0];
    const final = this.samples[this.samples.length - 1];
    const delta = final.heapUsed - initial.heapUsed;
    
    console.log('\nMemory Leak Analysis:');
    console.log(`Initial heap: ${BenchmarkUtils.formatMemory(initial.heapUsed)}`);
    console.log(`Final heap: ${BenchmarkUtils.formatMemory(final.heapUsed)}`);
    console.log(`Delta: ${BenchmarkUtils.formatMemory(delta)}`);
    
    const leakThreshold = 1024 * 1024; // 1MB
    const hasLeak = delta > leakThreshold;
    
    if (hasLeak) {
      console.log('⚠️  Potential memory leak detected!');
    } else {
      console.log('✅ No significant memory leak detected');
    }
    
    return hasLeak;
  }
}

// Main execution
async function main() {
  try {
    // Run benchmarks
    const benchmarks = new TerrainModelBenchmarks();
    await benchmarks.runAll();
    
    // Memory leak detection
    console.log('\n--- Memory Leak Detection ---');
    const leakDetector = new MemoryLeakDetector();
    leakDetector.start();
    
    // Perform operations that might leak
    for (let i = 0; i < 100; i++) {
      const dtm = TerrainModel.createDTM();
      const points = BenchmarkUtils.createRandomPoints(100);
      dtm.addPoints(points);
      dtm.triangulate();
      dtm.dispose();
      
      if (i % 20 === 0) {
        leakDetector.sample(`Iteration ${i}`);
      }
    }
    
    leakDetector.analyze();
    
  } catch (error) {
    console.error('Benchmark failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { BenchmarkUtils, TerrainModelBenchmarks, MemoryLeakDetector };
