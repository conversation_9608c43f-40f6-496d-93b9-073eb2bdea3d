#!/usr/bin/env node

/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const os = require('os');

// Build configuration
const BUILD_CONFIG = {
  // Build types
  buildTypes: ['Debug', 'Release', 'RelWithDebInfo'],
  defaultBuildType: 'Release',
  
  // Platforms
  platforms: ['win32', 'linux', 'darwin'],
  currentPlatform: os.platform(),
  
  // Directories
  sourceDir: path.resolve(__dirname, '..'),
  buildDir: path.resolve(__dirname, '..', 'build'),
  distDir: path.resolve(__dirname, '..', 'dist'),
  
  // Tools
  cmake: 'cmake',
  nodeGyp: 'node-gyp',
  npm: 'npm',
  
  // Options
  parallel: true,
  verbose: false,
  clean: false,
  test: false,
  package: false
};

// Utility functions
class BuildUtils {
  static log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      debug: '🔍'
    }[level] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }
  
  static exec(command, options = {}) {
    this.log(`Executing: ${command}`, 'debug');
    
    try {
      const result = execSync(command, {
        stdio: BUILD_CONFIG.verbose ? 'inherit' : 'pipe',
        encoding: 'utf8',
        ...options
      });
      
      return { success: true, output: result };
    } catch (error) {
      this.log(`Command failed: ${error.message}`, 'error');
      return { success: false, error: error.message, output: error.stdout };
    }
  }
  
  static async execAsync(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      this.log(`Executing: ${command} ${args.join(' ')}`, 'debug');
      
      const child = spawn(command, args, {
        stdio: BUILD_CONFIG.verbose ? 'inherit' : 'pipe',
        ...options
      });
      
      let stdout = '';
      let stderr = '';
      
      if (child.stdout) {
        child.stdout.on('data', (data) => {
          stdout += data.toString();
        });
      }
      
      if (child.stderr) {
        child.stderr.on('data', (data) => {
          stderr += data.toString();
        });
      }
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output: stdout });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });
      
      child.on('error', (error) => {
        reject(error);
      });
    });
  }
  
  static ensureDirectory(dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      this.log(`Created directory: ${dir}`);
    }
  }
  
  static removeDirectory(dir) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      this.log(`Removed directory: ${dir}`);
    }
  }
  
  static copyFile(src, dest) {
    this.ensureDirectory(path.dirname(dest));
    fs.copyFileSync(src, dest);
    this.log(`Copied: ${src} -> ${dest}`);
  }
  
  static getNodeVersion() {
    return process.version;
  }
  
  static getNodeABI() {
    return process.versions.modules;
  }
  
  static getPlatformInfo() {
    return {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      nodeVersion: this.getNodeVersion(),
      nodeABI: this.getNodeABI()
    };
  }
}

// Build system implementations
class CMakeBuild {
  constructor(config) {
    this.config = config;
  }
  
  async configure(buildType = 'Release') {
    BuildUtils.log(`Configuring CMake build (${buildType})...`);
    
    const buildDir = path.join(this.config.buildDir, 'cmake', buildType);
    BuildUtils.ensureDirectory(buildDir);
    
    const args = [
      '-S', this.config.sourceDir,
      '-B', buildDir,
      `-DCMAKE_BUILD_TYPE=${buildType}`,
      '-DBUILD_TESTS=ON',
      '-DBUILD_EXAMPLES=ON'
    ];
    
    // Platform-specific configuration
    if (this.config.currentPlatform === 'win32') {
      args.push('-A', os.arch() === 'x64' ? 'x64' : 'Win32');
    }
    
    try {
      await BuildUtils.execAsync(this.config.cmake, args);
      BuildUtils.log('CMake configuration completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`CMake configuration failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async build(buildType = 'Release') {
    BuildUtils.log(`Building with CMake (${buildType})...`);
    
    const buildDir = path.join(this.config.buildDir, 'cmake', buildType);
    
    const args = [
      '--build', buildDir,
      '--config', buildType
    ];
    
    if (this.config.parallel) {
      const cores = os.cpus().length;
      args.push('--parallel', cores.toString());
    }
    
    try {
      await BuildUtils.execAsync(this.config.cmake, args);
      BuildUtils.log('CMake build completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`CMake build failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async install(buildType = 'Release') {
    BuildUtils.log(`Installing CMake build (${buildType})...`);
    
    const buildDir = path.join(this.config.buildDir, 'cmake', buildType);
    
    const args = [
      '--install', buildDir,
      '--config', buildType
    ];
    
    try {
      await BuildUtils.execAsync(this.config.cmake, args);
      BuildUtils.log('CMake install completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`CMake install failed: ${error.message}`, 'error');
      return false;
    }
  }
}

class NodeGypBuild {
  constructor(config) {
    this.config = config;
  }
  
  async configure() {
    BuildUtils.log('Configuring node-gyp build...');
    
    const args = ['configure'];
    
    if (BUILD_CONFIG.verbose) {
      args.push('--verbose');
    }
    
    try {
      await BuildUtils.execAsync(this.config.nodeGyp, args, {
        cwd: this.config.sourceDir
      });
      BuildUtils.log('node-gyp configuration completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`node-gyp configuration failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async build() {
    BuildUtils.log('Building with node-gyp...');
    
    const args = ['build'];
    
    if (BUILD_CONFIG.verbose) {
      args.push('--verbose');
    }
    
    if (this.config.parallel) {
      args.push(`--jobs=${os.cpus().length}`);
    }
    
    try {
      await BuildUtils.execAsync(this.config.nodeGyp, args, {
        cwd: this.config.sourceDir
      });
      BuildUtils.log('node-gyp build completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`node-gyp build failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async rebuild() {
    BuildUtils.log('Rebuilding with node-gyp...');
    
    const args = ['rebuild'];
    
    if (BUILD_CONFIG.verbose) {
      args.push('--verbose');
    }
    
    try {
      await BuildUtils.execAsync(this.config.nodeGyp, args, {
        cwd: this.config.sourceDir
      });
      BuildUtils.log('node-gyp rebuild completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`node-gyp rebuild failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async clean() {
    BuildUtils.log('Cleaning node-gyp build...');
    
    try {
      await BuildUtils.execAsync(this.config.nodeGyp, ['clean'], {
        cwd: this.config.sourceDir
      });
      BuildUtils.log('node-gyp clean completed', 'success');
      return true;
    } catch (error) {
      BuildUtils.log(`node-gyp clean failed: ${error.message}`, 'error');
      return false;
    }
  }
}

// Main build orchestrator
class BuildOrchestrator {
  constructor() {
    this.cmakeBuild = new CMakeBuild(BUILD_CONFIG);
    this.nodeGypBuild = new NodeGypBuild(BUILD_CONFIG);
  }
  
  async checkPrerequisites() {
    BuildUtils.log('Checking build prerequisites...');
    
    const checks = [
      { name: 'Node.js', command: 'node --version' },
      { name: 'npm', command: 'npm --version' },
      { name: 'node-gyp', command: 'node-gyp --version' },
      { name: 'CMake', command: 'cmake --version' }
    ];
    
    let allPassed = true;
    
    for (const check of checks) {
      const result = BuildUtils.exec(check.command);
      if (result.success) {
        BuildUtils.log(`${check.name}: ✅ Available`);
      } else {
        BuildUtils.log(`${check.name}: ❌ Not found`, 'error');
        allPassed = false;
      }
    }
    
    // Platform info
    const platformInfo = BuildUtils.getPlatformInfo();
    BuildUtils.log(`Platform: ${platformInfo.platform}-${platformInfo.arch}`);
    BuildUtils.log(`Node.js: ${platformInfo.nodeVersion} (ABI: ${platformInfo.nodeABI})`);
    
    return allPassed;
  }
  
  async installDependencies() {
    BuildUtils.log('Installing dependencies...');
    
    const result = BuildUtils.exec('npm install', {
      cwd: BUILD_CONFIG.sourceDir
    });
    
    if (result.success) {
      BuildUtils.log('Dependencies installed', 'success');
      return true;
    } else {
      BuildUtils.log('Failed to install dependencies', 'error');
      return false;
    }
  }
  
  async clean() {
    if (BUILD_CONFIG.clean) {
      BuildUtils.log('Cleaning build directories...');
      
      BuildUtils.removeDirectory(BUILD_CONFIG.buildDir);
      BuildUtils.removeDirectory(BUILD_CONFIG.distDir);
      
      await this.nodeGypBuild.clean();
      
      BuildUtils.log('Clean completed', 'success');
    }
  }
  
  async buildWithCMake(buildType = 'Release') {
    BuildUtils.log(`Building with CMake (${buildType})...`);
    
    const success = await this.cmakeBuild.configure(buildType) &&
                   await this.cmakeBuild.build(buildType);
    
    if (success) {
      BuildUtils.log('CMake build completed successfully', 'success');
    } else {
      BuildUtils.log('CMake build failed', 'error');
    }
    
    return success;
  }
  
  async buildWithNodeGyp() {
    BuildUtils.log('Building with node-gyp...');
    
    const success = await this.nodeGypBuild.configure() &&
                   await this.nodeGypBuild.build();
    
    if (success) {
      BuildUtils.log('node-gyp build completed successfully', 'success');
    } else {
      BuildUtils.log('node-gyp build failed', 'error');
    }
    
    return success;
  }
  
  async runTests() {
    if (BUILD_CONFIG.test) {
      BuildUtils.log('Running tests...');
      
      const testCommands = [
        'npm test',
        'node test/unit-tests.js',
        'node test/benchmark.js'
      ];
      
      for (const command of testCommands) {
        const result = BuildUtils.exec(command, {
          cwd: BUILD_CONFIG.sourceDir
        });
        
        if (!result.success) {
          BuildUtils.log(`Test failed: ${command}`, 'error');
          return false;
        }
      }
      
      BuildUtils.log('All tests passed', 'success');
    }
    
    return true;
  }
  
  async packageBuild() {
    if (BUILD_CONFIG.package) {
      BuildUtils.log('Packaging build...');
      
      BuildUtils.ensureDirectory(BUILD_CONFIG.distDir);
      
      // Copy built addon
      const addonFile = path.join(BUILD_CONFIG.sourceDir, 'build', 'Release', 'TerrainModelNodeAddon.node');
      if (fs.existsSync(addonFile)) {
        BuildUtils.copyFile(addonFile, path.join(BUILD_CONFIG.distDir, 'TerrainModelNodeAddon.node'));
      }
      
      // Copy package files
      const packageFiles = ['package.json', 'index.js', 'index.d.ts', 'README.md'];
      for (const file of packageFiles) {
        const srcFile = path.join(BUILD_CONFIG.sourceDir, file);
        if (fs.existsSync(srcFile)) {
          BuildUtils.copyFile(srcFile, path.join(BUILD_CONFIG.distDir, file));
        }
      }
      
      BuildUtils.log('Packaging completed', 'success');
    }
    
    return true;
  }
  
  async build() {
    try {
      BuildUtils.log('Starting TerrainModel build process...');
      
      // Check prerequisites
      if (!await this.checkPrerequisites()) {
        throw new Error('Prerequisites check failed');
      }
      
      // Install dependencies
      if (!await this.installDependencies()) {
        throw new Error('Failed to install dependencies');
      }
      
      // Clean if requested
      await this.clean();
      
      // Build with preferred method
      let buildSuccess = false;
      
      // Try CMake first, fallback to node-gyp
      try {
        buildSuccess = await this.buildWithCMake(BUILD_CONFIG.defaultBuildType);
      } catch (error) {
        BuildUtils.log('CMake build failed, trying node-gyp...', 'warning');
        buildSuccess = await this.buildWithNodeGyp();
      }
      
      if (!buildSuccess) {
        throw new Error('Build failed');
      }
      
      // Run tests
      if (!await this.runTests()) {
        throw new Error('Tests failed');
      }
      
      // Package
      if (!await this.packageBuild()) {
        throw new Error('Packaging failed');
      }
      
      BuildUtils.log('Build completed successfully! 🎉', 'success');
      
    } catch (error) {
      BuildUtils.log(`Build failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Command line interface
function parseArguments() {
  const args = process.argv.slice(2);
  
  for (const arg of args) {
    switch (arg) {
      case '--verbose':
      case '-v':
        BUILD_CONFIG.verbose = true;
        break;
      case '--clean':
      case '-c':
        BUILD_CONFIG.clean = true;
        break;
      case '--test':
      case '-t':
        BUILD_CONFIG.test = true;
        break;
      case '--package':
      case '-p':
        BUILD_CONFIG.package = true;
        break;
      case '--debug':
        BUILD_CONFIG.defaultBuildType = 'Debug';
        break;
      case '--release':
        BUILD_CONFIG.defaultBuildType = 'Release';
        break;
      case '--help':
      case '-h':
        console.log(`
TerrainModel Build Script

Usage: node build.js [options]

Options:
  --verbose, -v     Enable verbose output
  --clean, -c       Clean build directories before building
  --test, -t        Run tests after building
  --package, -p     Package the build output
  --debug           Build in Debug mode
  --release         Build in Release mode (default)
  --help, -h        Show this help message

Examples:
  node build.js --clean --test --package
  node build.js --verbose --debug
        `);
        process.exit(0);
        break;
    }
  }
}

// Main execution
if (require.main === module) {
  parseArguments();
  
  const orchestrator = new BuildOrchestrator();
  orchestrator.build().catch((error) => {
    BuildUtils.log(`Unhandled error: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { BuildOrchestrator, BuildUtils, BUILD_CONFIG };
