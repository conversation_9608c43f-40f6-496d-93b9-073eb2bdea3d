<?xml version="1.0" encoding="utf-8"?>

<BuildContext xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../bsicommon/build/PartFile.xsd">

  <Part Name="iModelJsNodeAddon.Dev" PrgOutputDir="iModelJsNodeAddon">
    <SubPart PartName="iModelCorePRG" PartFile="iModelCore/iModelCore"/>
    <SubPart PartName="iModelJsMakePackages"/>
    <SubPart PartName="RunUnitTests"/>
  </Part>


  <Part Name="iModelJsApiDeclarations" BentleyBuildMakeFile="api_package/ts/makeTs.mke">
    <NpmDependency>${SrcRoot}imodel02/iModelJsNodeAddon/api_package/ts/</NpmDependency>
    <Bindings>
      <Files>
        Delivery/lib/imodeljs-native.js
        Delivery/lib/imodeljs-native.js.map
        Delivery/lib/imodeljs-native.d.ts
        Delivery/lib/imodeljs-native.d.ts.map
        Delivery/lib/NativeLibrary.js
        Delivery/lib/NativeLibrary.js.map
        Delivery/lib/NativeLibrary.d.ts
        Delivery/lib/NativeLibrary.d.ts.map
        Delivery/lib/BlobDaemon.js
        Delivery/lib/BlobDaemon.js.map
        Delivery/lib/BlobDaemon.d.ts
        Delivery/lib/BlobDaemon.d.ts.map
      </Files>
    </Bindings>
  </Part>

  <Part Name="iModelJsMakePackages" BentleyBuildMakeFile="makePackages.mke" OnlyPlatforms="x64,LinuxX64,MacOSx64" PrgOutputDir="iModelJsNodeAddon">
    <SubProduct ProductName="iModelJsNodeAddon"/>
    <SubPart PartName="iModelJsApiDeclarations"/>
  </Part>

  <Product Name="iModelJsNodeAddon">
    <SubProduct ProductName="iModelJsNative-Windows" />
    <SubProduct ProductName="iModelJsNative-Linux" />
    <SubProduct ProductName="iModelJsNative-Darwin" />
  </Product>

  <Product Name="iModelJsNative-Windows">
    <SubPart PartName="iModelJsNative-Dynamic" LibType="Dynamic" />
    <Directories DirectoryListName="iModelJsNodeAddonDeliveryList" />
    <FeatureExclude>GeoCoordAssetsLarge</FeatureExclude>
  </Product>

  <Product Name="iModelJsNative-Linux">
    <SubPart PartName="iModelJsNative-Static" LibType="Static" />
    <Directories DirectoryListName="iModelJsNodeAddonDeliveryList" />
    <FeatureExclude>GeoCoordAssetsLarge</FeatureExclude>
  </Product>

  <Product Name="iModelJsNative-Darwin">
    <SubPart PartName="iModelJsNative-Static" LibType="Static" />
    <Directories DirectoryListName="iModelJsNodeAddonDeliveryList" />
    <FeatureExclude>GeoCoordAssetsLarge</FeatureExclude>
  </Product>

  <ProductDirectoryList ListName="iModelJsNodeAddonDeliveryList">
    <ProductDirectory Name="AddonRoot" Path="" />
    <ProductDirectory Name="Assemblies" Path="" RelativeTo="AddonRoot"/>
    <ProductDirectory Name="StaticAssemblies" LibType="Static" Path="" RelativeTo="AddonRoot"/>
    <ProductDirectory Name="Assemblies" Deliver="false" LibType="Static"/>
    <ProductDirectory Name="Assets" Path="Assets" RelativeTo="AddonRoot"/>
    <ProductDirectory Name="Assets" Path="Assets" RelativeTo="AddonRoot" LibType="Static"/>
    <ProductDirectory Name="VendorNotices"  RelativeTo="AddonRoot" Path="Notices"/>
    <ProductDirectory Name="VendorNotices"  RelativeTo="AddonRoot" Path="Notices" LibType="static"/>
    <ProductDirectory Name="CrashpadHandler" RelativeTo="AddonRoot" LibType="Static"/>
  </ProductDirectoryList>

  <Part Name="iModelJsNative-Mobile" BentleyBuildMakeFile="IModelJsNative.mke" BentleyBuildMakeOptions="-dNODE_ADDON_API=iModelJs -dBUILD_FOR_IMODELJS_MOBILE=1" >
    <!-- Get the node-addon-api (declaration) from iModelJs, not from node! -->
    <!-- SubPart PartName="VendorAPI" PartFile="iModelJsMobile" Repository="iModelJsMobile"/-->
    <SubPart PartName="iModelJsNativeLibCommon"/>
    <SubPart PartName="iModelJsNativeLibSqlang"/>
    <SubPart PartName="iModelHubClient-PublicAPI" PartFile="iModelCore/WSClient/WSClient" Repository="imodel02" />
    <Bindings>
        <Directory SourceName="Delivery/imodeljs-addon-objs" SubPartDirectory="imodeljs-addon-objs" />
        <Files SubPartDirectory="imodeljs-addon-build-tools">
            Delivery/IModelJsNative_input_libs.mki
        </Files>
    </Bindings>
  </Part>

  <Part Name="iModelJsLib" ExcludeLibType="Static" BentleyBuildMakeFile="IModelJsNative.mke">
    <RequiredRepository>Thirdparty-breakpad_54fa71efbe50</RequiredRepository>
    <SubPart PartName="napi-lib" PartFile="node-addon-api/node-addon-api"/>
    <SubPart PartName="iModelJsNativeLibCommon"/>
    <SubPart PartName="iModelJsNativeLibSqlang"/>
    <SubPart PartName="iModelHubClient-PublicAPI" PartFile="iModelCore/WSClient/WSClient" />
    <SubPart PartName="BeBlobDaemon-exe" PartFile="iModelCore/BeSQLite/BeSQLite" />
    <Bindings>
      <Files ProductDirectoryName="Assemblies">Delivery/imodeljs.node</Files>
      <VendorNotices IfNotPresent="Continue">
        Delivery/breakpad_notice.txt,
        Delivery/libdisasm_notice.txt
      </VendorNotices>
    </Bindings>
  </Part>

  <Part Name="iModelJsNative-Dynamic" OnlyPlatforms="x64" ExcludeLibType="Static" >
    <SubPart PartName="iModelJsLib"/>
    <SubPart PartName="napi-dll-win" PartFile="node-addon-api/node-addon-api"/>
  </Part>

  <Part Name="CrashpadShim" OnlyPlatforms="linux*">
    <SubPart PartName="CrashpadHandler" PartFile="iModelCore/libsrc/crashpad/crashpad"/>
  </Part>

  <Part Name="iModelJsNative-Static" OnlyPlatforms="Linux*,MacOS*" ExcludeLibType="Dynamic" BentleyBuildMakeFile="IModelJsNative.mke">
    <SubPart PartName="napi-lib" PartFile="node-addon-api/node-addon-api"/>
    <SubPart PartName="iModelJsNativeLibCommon"/>
    <SubPart PartName="iModelJsNativeLibSqlang"/>
    <SubPart PartName="CrashpadShim"/>
    <Bindings>
        <Files ProductDirectoryName="StaticAssemblies">Delivery/imodeljs.node</Files>
    </Bindings>
  </Part>

  <Part Name="iModelJsNativeLibSqlang" BentleyBuildMakeFile="${SrcRoot}bsicommon/sqlang/mki/XliffsToDb.mke" BentleyBuildMakeOptions="-dSQLANG_OutputDb=$(OutputRootDir)build/iModelJsNodeAddon/iModelJsNodeAddon_en.sqlang.db3 -dSQLANG_XliffWildcard=$(BuildContext)SubParts/xliffs/*.xliff">
    <SubPart PartName="iModelJsNativeLibCommon" />
    <Bindings>
      <Files ProductDirectoryName="Assets" ProductSubDirectory="sqlang">Delivery/sqlang/iModelJsNodeAddon_en.sqlang.db3</Files>
    </Bindings>
  </Part>

  <!-- This part builds our node native code addon -->
  <Part Name="iModelJsNativeLibCommon">
    <SubPart PartName="DgnPlatformDLL" PartFile="iModelCore/DgnPlatform/DgnPlatform"/>
    <SubPart PartName="folly" PartFile="iModelCore/libsrc/facebook/facebook" Repository="imodel02"/>
    <SubPart PartName="ECPresentation" PartFile="iModelCore/ECPresentation/ECPresentation"/>
    <SubPart PartName="ECObjectsNative"     PartFile="iModelCore/ecobjects/ECObjects"    />
    <SubPart PartName="BeRapidJson" PartFile="iModelCore/Bentley/Bentley"/>
    <SubPart PartName="Licensing" PartFile="iModelCore/LicensingCrossPlatform/LicensingCrossPlatform" />
    <SubPart PartName="WebServicesClient" PartFile="iModelCore/WSClient/WSClient"/>
    <SubPart PartName="iModelHubClient" PartFile="iModelCore/WSClient/WSClient"/>
  </Part>

  <Part Name="RunUnitTests" DeferType="RunUnitTests" BentleyBuildMakeFile="api_package/ts/runTests.mke">
      <SubPart PartName="iModelJsMakePackages"/>
  </Part>

</BuildContext>
