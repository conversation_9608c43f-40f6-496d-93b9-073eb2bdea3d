/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const TerrainModel = require('./index');

// Example usage of the TerrainModel Node.js addon
async function demonstrateTerrainModel() {
  console.log('TerrainModel Node.js Addon Example');
  console.log('Version:', TerrainModel.version);
  console.log('');

  try {
    // Create a new DTM
    console.log('1. Creating a new DTM...');
    const dtm = TerrainModel.createDTM(1000, 500);
    console.log('DTM created successfully');
    console.log('Initial vertices count:', dtm.getVerticesCount());
    console.log('');

    // Add some sample points
    console.log('2. Adding sample points...');
    const samplePoints = [
      TerrainModel.createPoint3D(0, 0, 100),
      TerrainModel.createPoint3D(100, 0, 105),
      TerrainModel.createPoint3D(50, 100, 110),
      TerrainModel.createPoint3D(0, 100, 108),
      TerrainModel.createPoint3D(100, 100, 112),
      TerrainModel.createPoint3D(50, 50, 107)
    ];

    // Add points as features
    for (let i = 0; i < samplePoints.length; i++) {
      const featureId = dtm.addPointFeature(samplePoints[i], i + 1);
      console.log(`Added point feature ${featureId} at (${samplePoints[i].x}, ${samplePoints[i].y}, ${samplePoints[i].z})`);
    }

    // Add a breakline
    console.log('');
    console.log('3. Adding a breakline...');
    const breaklinePoints = [
      TerrainModel.createPoint3D(25, 25, 106),
      TerrainModel.createPoint3D(75, 25, 109),
      TerrainModel.createPoint3D(75, 75, 111),
      TerrainModel.createPoint3D(25, 75, 108)
    ];
    const breaklineId = dtm.addLinearFeature(breaklinePoints, TerrainModel.DTMFeatureType.BreakLine, 100);
    console.log(`Added breakline feature ${breaklineId} with ${breaklinePoints.length} points`);
    console.log('');

    // Set triangulation parameters
    console.log('4. Setting triangulation parameters...');
    const success = dtm.setTriangulationParameters(
      TerrainModel.Constants.DEFAULT_POINT_TOLERANCE,
      TerrainModel.Constants.DEFAULT_LINE_TOLERANCE,
      TerrainModel.DTMEdgeOption.RemoveSliver,
      TerrainModel.Constants.DEFAULT_MAX_SIDE
    );
    console.log('Triangulation parameters set:', success);
    console.log('');

    // Triangulate the DTM
    console.log('5. Triangulating the DTM...');
    const triangulationReport = await dtm.triangulateAsync();
    console.log('Triangulation result:', triangulationReport);
    
    if (triangulationReport.success) {
      console.log(`Successfully triangulated with ${triangulationReport.verticesCount} vertices and ${triangulationReport.trianglesCount} triangles`);
    } else {
      console.log('Triangulation failed with status:', triangulationReport.status);
    }
    console.log('');

    // Get DTM properties
    console.log('6. Getting DTM properties...');
    const range = dtm.getRange3d();
    console.log('DTM Range:');
    console.log(`  Low: (${range.low.x}, ${range.low.y}, ${range.low.z})`);
    console.log(`  High: (${range.high.x}, ${range.high.y}, ${range.high.z})`);
    
    const boundary = dtm.getBoundary();
    console.log(`DTM Boundary: ${boundary.length} points`);
    
    const stats = dtm.calculateFeatureStatistics();
    console.log('Feature Statistics:', stats);
    console.log('');

    // Drape some points
    console.log('7. Draping points onto the DTM...');
    const drapeTestPoints = [
      TerrainModel.createPoint3D(30, 30, 0),
      TerrainModel.createPoint3D(60, 60, 0),
      TerrainModel.createPoint3D(90, 90, 0),
      TerrainModel.createPoint3D(150, 150, 0) // Outside DTM
    ];

    for (const testPoint of drapeTestPoints) {
      const drapedPoint = dtm.drapePoint(testPoint);
      console.log(`Draping (${testPoint.x}, ${testPoint.y}) -> ` +
                 `(${drapedPoint.drapedPoint.x}, ${drapedPoint.drapedPoint.y}, ${drapedPoint.drapedPoint.z}) ` +
                 `Code: ${drapedPoint.drapeCode}, Valid: ${drapedPoint.isValid}`);
    }
    console.log('');

    // Calculate slope area
    console.log('8. Calculating slope area...');
    const slopeArea = dtm.calculateSlopeArea();
    console.log('Slope Area Result:', slopeArea);
    console.log('');

    // Enumerate features
    console.log('9. Enumerating features...');
    const featureEnumerator = new TerrainModel.DTMFeatureEnumerator(dtm);
    featureEnumerator.includeAllFeatures();
    
    let featureCount = 0;
    while (featureEnumerator.moveNext()) {
      const feature = featureEnumerator.current();
      if (feature) {
        featureCount++;
        console.log(`Feature ${feature.featureId}: Type ${feature.featureType}, ` +
                   `UserTag ${feature.userTag}, Points: ${feature.pointCount}`);
      }
    }
    console.log(`Total features enumerated: ${featureCount}`);
    featureEnumerator.dispose();
    console.log('');

    // Save the DTM
    console.log('10. Saving DTM to file...');
    const outputFileName = 'example_dtm.tin';
    await dtm.saveAsync(outputFileName);
    console.log(`DTM saved to ${outputFileName}`);
    console.log('');

    // Advanced features demonstration
    console.log('11. Demonstrating advanced features...');

    // DTM TIN Editor
    const tinEditor = new TerrainModel.DTMTinEditor(dtm);
    console.log('TIN Editor created');

    // Water Analysis
    const waterAnalysis = new TerrainModel.WaterAnalysis(dtm);
    waterAnalysis.zeroSlopeTraceOption = TerrainModel.ZeroSlopeTraceOption.Pond;
    const waterResult = waterAnalysis.doTrace(TerrainModel.createPoint3D(50, 50, 0));
    console.log('Water analysis completed:', waterResult.totalWaterVolume);

    // DTM Pond Design
    const pondDesign = new TerrainModel.DTMPond(dtm);
    pondDesign.targetVolume = 1000.0;
    const pondResult = pondDesign.calculatePondByVolume(1000.0);
    console.log('Pond design result:', pondResult.calculated ? 'Success' : 'Failed');

    // DTM Caching for performance
    const dtmCaching = new TerrainModel.DTMCaching(dtm);
    dtmCaching.cachingEnabled = true;
    const cacheStats = dtmCaching.getCacheStatistics();
    console.log('Cache statistics:', cacheStats);

    // Clean up advanced objects
    tinEditor.dispose();
    waterAnalysis.dispose();
    pondDesign.dispose();
    dtmCaching.dispose();
    console.log('Advanced objects disposed');

    // Clean up
    console.log('12. Cleaning up...');
    dtm.dispose();
    console.log('DTM disposed');

  } catch (error) {
    console.error('Error during demonstration:', error);
    if (error instanceof TerrainModel.TerrainModelError) {
      console.error('TerrainModel Error Code:', error.code);
    }
  }
}

// Run the demonstration
if (require.main === module) {
  demonstrateTerrainModel()
    .then(() => {
      console.log('\nDemonstration completed successfully!');
    })
    .catch((error) => {
      console.error('\nDemonstration failed:', error);
      process.exit(1);
    });
}

module.exports = { demonstrateTerrainModel };
