/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "DTM.h"
#include "GeometryUtils.h"
#include "TriangulationEngine.h"
#include "SpatialIndex.h"
#include "FileIO.h"
#include <algorithm>
#include <chrono>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// DTM implementation
//=======================================================================================
DTM::DTM()
    : m_isTriangulated(false)
    , m_nextVertexId(1)
    , m_nextTriangleId(1)
    , m_nextEdgeId(1)
    , m_nextFeatureId(1)
    , m_spatialIndex(SpatialIndex::Create("adaptive"))
    , m_triangulationEngine(std::make_unique<TriangulationEngine>())
{
    // Initialize bounds to invalid state
    m_bounds.low = Point3D(std::numeric_limits<double>::max(), 
                          std::numeric_limits<double>::max(), 
                          std::numeric_limits<double>::max());
    m_bounds.high = Point3D(std::numeric_limits<double>::lowest(), 
                           std::numeric_limits<double>::lowest(), 
                           std::numeric_limits<double>::lowest());
}

DTM::DTM(size_t initialVertexCapacity, size_t incrementVertexCapacity)
    : DTM()
{
    // Reserve capacity for better performance
    if (initialVertexCapacity > 0) {
        m_vertices.reserve(initialVertexCapacity);
    }
    
    // Store capacity hints for future use
    m_initialVertexCapacity = initialVertexCapacity;
    m_incrementVertexCapacity = incrementVertexCapacity;
}

DTM::~DTM() = default;

//=======================================================================================
// Factory methods
//=======================================================================================
DTMPtr DTM::Create() {
    return std::make_shared<DTM>();
}

DTMPtr DTM::Create(size_t initialVertexCapacity, size_t incrementVertexCapacity) {
    return std::make_shared<DTM>(initialVertexCapacity, incrementVertexCapacity);
}

DTMPtr DTM::CreateFromPoints(const std::vector<Point3D>& points) {
    auto dtm = Create(points.size(), points.size() / 4);
    
    // Add all points as features
    for (const auto& point : points) {
        dtm->AddPointFeature(point);
    }
    
    // Triangulate
    auto result = dtm->Triangulate();
    if (!result.isSuccess()) {
        return nullptr;
    }
    
    return dtm;
}

DTMPtr DTM::CreateFromFile(const std::string& fileName) {
    auto dtm = Create();
    
    auto& fileManager = FileIOManager::Instance();
    FileIOResult result = fileManager.LoadDTM(fileName, *dtm);
    
    if (!result.success) {
        return nullptr;
    }
    
    return dtm;
}

//=======================================================================================
// Feature management
//=======================================================================================
DTMFeatureId DTM::AddPointFeature(const Point3D& point, int userTag) {
    // Validate point
    if (!IsValidPoint(point)) {
        return Constants::INVALID_ID;
    }
    
    // Create vertex
    DTMVertexId vertexId = CreateVertex(point);
    if (vertexId == Constants::INVALID_ID) {
        return Constants::INVALID_ID;
    }
    
    // Create feature
    DTMFeatureId featureId = m_nextFeatureId++;
    auto feature = std::make_shared<DTMFeature>(featureId, DTMFeatureType::Point);
    feature->SetUserTag(userTag);
    feature->AddPoint(point);
    
    m_features[featureId] = feature;
    
    // Update bounds
    UpdateBounds(point);
    
    // Invalidate triangulation
    m_isTriangulated = false;
    
    return featureId;
}

DTMFeatureId DTM::AddLinearFeature(const std::vector<Point3D>& points, DTMFeatureType featureType, int userTag) {
    if (points.size() < 2) {
        return Constants::INVALID_ID;
    }
    
    // Validate all points
    for (const auto& point : points) {
        if (!IsValidPoint(point)) {
            return Constants::INVALID_ID;
        }
    }
    
    // Create vertices for all points
    std::vector<DTMVertexId> vertexIds;
    for (const auto& point : points) {
        DTMVertexId vertexId = CreateVertex(point);
        if (vertexId != Constants::INVALID_ID) {
            vertexIds.push_back(vertexId);
            UpdateBounds(point);
        }
    }
    
    if (vertexIds.empty()) {
        return Constants::INVALID_ID;
    }
    
    // Create feature
    DTMFeatureId featureId = m_nextFeatureId++;
    auto feature = std::make_shared<DTMFeature>(featureId, featureType);
    feature->SetUserTag(userTag);
    
    for (const auto& point : points) {
        feature->AddPoint(point);
    }
    
    m_features[featureId] = feature;
    
    // Invalidate triangulation
    m_isTriangulated = false;
    
    return featureId;
}

bool DTM::DeleteFeature(DTMFeatureId featureId) {
    auto it = m_features.find(featureId);
    if (it == m_features.end()) {
        return false;
    }
    
    // TODO: Remove associated vertices if they're not used by other features
    
    m_features.erase(it);
    
    // Invalidate triangulation
    m_isTriangulated = false;
    
    return true;
}

DTMFeaturePtr DTM::GetFeature(DTMFeatureId featureId) const {
    auto it = m_features.find(featureId);
    return (it != m_features.end()) ? it->second : nullptr;
}

std::vector<DTMFeaturePtr> DTM::GetFeatures() const {
    std::vector<DTMFeaturePtr> features;
    features.reserve(m_features.size());
    
    for (const auto& pair : m_features) {
        features.push_back(pair.second);
    }
    
    return features;
}

std::vector<DTMFeaturePtr> DTM::GetFeaturesByType(DTMFeatureType featureType) const {
    std::vector<DTMFeaturePtr> features;
    
    for (const auto& pair : m_features) {
        if (pair.second->GetFeatureType() == featureType) {
            features.push_back(pair.second);
        }
    }
    
    return features;
}

//=======================================================================================
// Triangulation
//=======================================================================================
TriangulationResult DTM::Triangulate() {
    return Triangulate(TriangulationParameters());
}

TriangulationResult DTM::Triangulate(const TriangulationParameters& parameters) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    try {
        // Check if we have enough points
        if (m_vertices.size() < 3) {
            result.status = DTMStatus::InsufficientData;
            result.message = "Need at least 3 points for triangulation";
            return result;
        }
        
        // Configure triangulation engine
        TriangulationEngine::Configuration config(parameters);
        m_triangulationEngine->SetConfiguration(config);
        
        // Extract points for triangulation
        std::vector<Point3D> points;
        points.reserve(m_vertices.size());
        
        for (const auto& pair : m_vertices) {
            if (pair.second->IsActive()) {
                points.push_back(pair.second->GetPosition());
            }
        }
        
        // Perform triangulation
        TriangulationResult engineResult = m_triangulationEngine->Triangulate(points);
        
        if (engineResult.isSuccess()) {
            // Copy results from engine
            const auto& engineVertices = m_triangulationEngine->GetVertices();
            const auto& engineTriangles = m_triangulationEngine->GetTriangles();
            const auto& engineEdges = m_triangulationEngine->GetEdges();
            
            // Clear existing triangulation data
            m_triangles.clear();
            m_edges.clear();
            
            // Copy triangles
            for (const auto& pair : engineTriangles) {
                m_triangles[pair.first] = pair.second;
            }
            
            // Copy edges
            for (const auto& pair : engineEdges) {
                m_edges[pair.first] = pair.second;
            }
            
            // Update spatial index
            UpdateSpatialIndex();
            
            // Mark as triangulated
            m_isTriangulated = true;
            
            // Set result data
            result.verticesCount = m_vertices.size();
            result.trianglesCount = m_triangles.size();
            result.edgesCount = m_edges.size();
            result.message = "Triangulation completed successfully";
            
        } else {
            result.status = DTMStatus::TriangulationFailed;
            result.message = engineResult.message;
            result.warnings = engineResult.warnings;
        }
        
    } catch (const std::exception& e) {
        result.status = DTMStatus::TriangulationFailed;
        result.message = "Triangulation failed: " + std::string(e.what());
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.elapsedTime = duration.count() / 1000.0;
    
    return result;
}

//=======================================================================================
// Draping operations
//=======================================================================================
DrapedPoint DTM::DrapePoint(const Point3D& point) const {
    DrapedPoint result;
    result.originalPoint = point;
    result.drapedPoint = point;
    result.isValid = false;
    result.drapeCode = DTMDrapedPointCode::External;
    
    if (!m_isTriangulated) {
        result.drapeCode = DTMDrapedPointCode::NotTriangulated;
        return result;
    }
    
    // Find triangles containing the point
    auto triangleIds = m_spatialIndex->FindTrianglesContaining(point);
    
    for (DTMTriangleId triangleId : triangleIds) {
        auto triangleIt = m_triangles.find(triangleId);
        if (triangleIt == m_triangles.end()) {
            continue;
        }
        
        const auto& triangle = triangleIt->second;
        if (triangle->ContainsPoint(point, m_vertices)) {
            // Interpolate elevation
            result.drapedPoint = triangle->InterpolateElevation(point, m_vertices);
            result.isValid = true;
            result.drapeCode = DTMDrapedPointCode::Success;
            result.triangleId = triangleId;
            return result;
        }
    }
    
    // Point not found in any triangle
    result.drapeCode = DTMDrapedPointCode::External;
    return result;
}

std::vector<DrapedPoint> DTM::DrapePoints(const std::vector<Point3D>& points) const {
    std::vector<DrapedPoint> results;
    results.reserve(points.size());
    
    for (const auto& point : points) {
        results.push_back(DrapePoint(point));
    }
    
    return results;
}

//=======================================================================================
// Analysis operations
//=======================================================================================
double DTM::CalculateVolume(double referenceElevation) const {
    if (!m_isTriangulated) {
        return 0.0;
    }
    
    double totalVolume = 0.0;
    
    for (const auto& pair : m_triangles) {
        const auto& triangle = pair.second;
        if (!triangle->IsActive()) {
            continue;
        }
        
        // Get triangle vertices
        auto v1It = m_vertices.find(triangle->GetVertex1Id());
        auto v2It = m_vertices.find(triangle->GetVertex2Id());
        auto v3It = m_vertices.find(triangle->GetVertex3Id());
        
        if (v1It == m_vertices.end() || v2It == m_vertices.end() || v3It == m_vertices.end()) {
            continue;
        }
        
        const Point3D& p1 = v1It->second->GetPosition();
        const Point3D& p2 = v2It->second->GetPosition();
        const Point3D& p3 = v3It->second->GetPosition();
        
        // Calculate triangle area
        double area = GeometryUtils::TriangleArea2D(p1, p2, p3);
        
        // Calculate average height above reference
        double avgHeight = ((p1.z + p2.z + p3.z) / 3.0) - referenceElevation;
        
        // Add to total volume (can be negative for cut)
        totalVolume += area * avgHeight;
    }
    
    return totalVolume;
}

DTMStatistics DTM::GetStatistics() const {
    DTMStatistics stats;
    
    if (m_vertices.empty()) {
        return stats;
    }
    
    // Basic counts
    stats.vertexCount = m_vertices.size();
    stats.triangleCount = m_triangles.size();
    stats.edgeCount = m_edges.size();
    stats.featureCount = m_features.size();
    
    // Bounds
    stats.bounds = m_bounds;
    
    // Elevation statistics
    std::vector<double> elevations;
    elevations.reserve(m_vertices.size());
    
    for (const auto& pair : m_vertices) {
        if (pair.second->IsActive()) {
            elevations.push_back(pair.second->GetPosition().z);
        }
    }
    
    if (!elevations.empty()) {
        std::sort(elevations.begin(), elevations.end());
        
        stats.minElevation = elevations.front();
        stats.maxElevation = elevations.back();
        
        // Calculate mean
        double sum = 0.0;
        for (double elev : elevations) {
            sum += elev;
        }
        stats.meanElevation = sum / elevations.size();
        
        // Calculate median
        size_t mid = elevations.size() / 2;
        if (elevations.size() % 2 == 0) {
            stats.medianElevation = (elevations[mid - 1] + elevations[mid]) / 2.0;
        } else {
            stats.medianElevation = elevations[mid];
        }
        
        // Calculate standard deviation
        double variance = 0.0;
        for (double elev : elevations) {
            double diff = elev - stats.meanElevation;
            variance += diff * diff;
        }
        stats.stdDevElevation = std::sqrt(variance / elevations.size());
    }
    
    // Area calculation
    if (m_isTriangulated) {
        stats.surfaceArea = CalculateSurfaceArea();
    }
    
    return stats;
}

//=======================================================================================
// File I/O operations
//=======================================================================================
bool DTM::SaveToFile(const std::string& fileName) const {
    auto& fileManager = FileIOManager::Instance();
    FileIOResult result = fileManager.SaveDTM(fileName, *this);
    return result.success;
}

bool DTM::LoadFromFile(const std::string& fileName) {
    auto& fileManager = FileIOManager::Instance();
    FileIOResult result = fileManager.LoadDTM(fileName, *this);
    return result.success;
}

//=======================================================================================
// Internal helper methods
//=======================================================================================
DTMVertexId DTM::CreateVertex(const Point3D& position) {
    // Check for duplicate vertices within tolerance
    const double tolerance = Constants::DEFAULT_POINT_TOLERANCE;
    
    for (const auto& pair : m_vertices) {
        if (GeometryUtils::Distance3D(pair.second->GetPosition(), position) < tolerance) {
            return pair.first; // Return existing vertex
        }
    }
    
    // Create new vertex
    DTMVertexId vertexId = m_nextVertexId++;
    auto vertex = std::make_shared<DTMVertex>(vertexId, position);
    m_vertices[vertexId] = vertex;
    
    // Add to spatial index
    m_spatialIndex->InsertVertex(vertexId, position);
    
    return vertexId;
}

void DTM::UpdateBounds(const Point3D& point) {
    if (m_vertices.size() == 1) {
        // First point - initialize bounds
        m_bounds.low = m_bounds.high = point;
    } else {
        // Expand bounds
        m_bounds.low.x = std::min(m_bounds.low.x, point.x);
        m_bounds.low.y = std::min(m_bounds.low.y, point.y);
        m_bounds.low.z = std::min(m_bounds.low.z, point.z);
        
        m_bounds.high.x = std::max(m_bounds.high.x, point.x);
        m_bounds.high.y = std::max(m_bounds.high.y, point.y);
        m_bounds.high.z = std::max(m_bounds.high.z, point.z);
    }
}

void DTM::UpdateSpatialIndex() {
    m_spatialIndex->Clear();
    
    // Add all vertices
    for (const auto& pair : m_vertices) {
        if (pair.second->IsActive()) {
            m_spatialIndex->InsertVertex(pair.first, pair.second->GetPosition());
        }
    }
    
    // Add all triangles
    for (const auto& pair : m_triangles) {
        if (pair.second->IsActive()) {
            Range3D triangleBounds = CalculateTriangleBounds(pair.second);
            m_spatialIndex->InsertTriangle(pair.first, triangleBounds);
        }
    }
}

Range3D DTM::CalculateTriangleBounds(DTMTrianglePtr triangle) const {
    auto v1It = m_vertices.find(triangle->GetVertex1Id());
    auto v2It = m_vertices.find(triangle->GetVertex2Id());
    auto v3It = m_vertices.find(triangle->GetVertex3Id());
    
    if (v1It == m_vertices.end() || v2It == m_vertices.end() || v3It == m_vertices.end()) {
        return Range3D();
    }
    
    const Point3D& p1 = v1It->second->GetPosition();
    const Point3D& p2 = v2It->second->GetPosition();
    const Point3D& p3 = v3It->second->GetPosition();
    
    Range3D bounds;
    bounds.low.x = std::min({p1.x, p2.x, p3.x});
    bounds.low.y = std::min({p1.y, p2.y, p3.y});
    bounds.low.z = std::min({p1.z, p2.z, p3.z});
    
    bounds.high.x = std::max({p1.x, p2.x, p3.x});
    bounds.high.y = std::max({p1.y, p2.y, p3.y});
    bounds.high.z = std::max({p1.z, p2.z, p3.z});
    
    return bounds;
}

bool DTM::IsValidPoint(const Point3D& point) const {
    return std::isfinite(point.x) && std::isfinite(point.y) && std::isfinite(point.z);
}

double DTM::CalculateSurfaceArea() const {
    double totalArea = 0.0;
    
    for (const auto& pair : m_triangles) {
        if (pair.second->IsActive()) {
            totalArea += pair.second->GetArea(m_vertices);
        }
    }
    
    return totalArea;
}

} // namespace TerrainModel
