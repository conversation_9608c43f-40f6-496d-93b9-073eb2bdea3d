/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "NativeDTMCaching.h"
#include "TerrainModelUtils.h"
#include <chrono>
#include <sstream>
#include <algorithm>

namespace TerrainModelNodeAddon {

//=======================================================================================
// DTMFenceParams implementation
//=======================================================================================
Napi::Object DTMFenceParams::ToJavaScript(Napi::Env env) const {
    Napi::Object obj = Napi::Object::New(env);
    
    obj.Set("points", TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, points));
    obj.Set("fenceType", Napi::Number::New(env, static_cast<int>(fenceType)));
    obj.Set("fenceOption", Napi::Number::New(env, static_cast<int>(fenceOption)));
    
    return obj;
}

DTMFenceParams DTMFenceParams::FromJavaScript(Napi::Object obj) {
    DTMFenceParams params;
    
    if (obj.Has("points") && obj.Get("points").IsArray()) {
        params.points = TerrainModelUtils::ConvertJSArrayToPoint3DVector(obj.Get("points").As<Napi::Array>());
    }
    
    if (obj.Has("fenceType") && obj.Get("fenceType").IsNumber()) {
        params.fenceType = static_cast<DTMFenceType>(obj.Get("fenceType").As<Napi::Number>().Int32Value());
    }
    
    if (obj.Has("fenceOption") && obj.Get("fenceOption").IsNumber()) {
        params.fenceOption = static_cast<DTMFenceOption>(obj.Get("fenceOption").As<Napi::Number>().Int32Value());
    }
    
    return params;
}

//=======================================================================================
// VisibilityAnalysisResult implementation
//=======================================================================================
Napi::Object VisibilityAnalysisResult::ToJavaScript(Napi::Env env) const {
    Napi::Object obj = Napi::Object::New(env);
    
    obj.Set("visibility", Napi::Number::New(env, static_cast<int>(visibility)));
    obj.Set("visiblePoints", TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, visiblePoints));
    obj.Set("hiddenPoints", TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, hiddenPoints));
    obj.Set("partiallyVisiblePoints", TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, partiallyVisiblePoints));
    obj.Set("visibilityPercentage", Napi::Number::New(env, visibilityPercentage));
    
    return obj;
}

VisibilityAnalysisResult VisibilityAnalysisResult::FromJavaScript(Napi::Object obj) {
    VisibilityAnalysisResult result;
    
    if (obj.Has("visibility") && obj.Get("visibility").IsNumber()) {
        result.visibility = static_cast<VisibilityType>(obj.Get("visibility").As<Napi::Number>().Int32Value());
    }
    
    if (obj.Has("visiblePoints") && obj.Get("visiblePoints").IsArray()) {
        result.visiblePoints = TerrainModelUtils::ConvertJSArrayToPoint3DVector(obj.Get("visiblePoints").As<Napi::Array>());
    }
    
    if (obj.Has("hiddenPoints") && obj.Get("hiddenPoints").IsArray()) {
        result.hiddenPoints = TerrainModelUtils::ConvertJSArrayToPoint3DVector(obj.Get("hiddenPoints").As<Napi::Array>());
    }
    
    if (obj.Has("partiallyVisiblePoints") && obj.Get("partiallyVisiblePoints").IsArray()) {
        result.partiallyVisiblePoints = TerrainModelUtils::ConvertJSArrayToPoint3DVector(obj.Get("partiallyVisiblePoints").As<Napi::Array>());
    }
    
    if (obj.Has("visibilityPercentage") && obj.Get("visibilityPercentage").IsNumber()) {
        result.visibilityPercentage = obj.Get("visibilityPercentage").As<Napi::Number>().DoubleValue();
    }
    
    return result;
}

//=======================================================================================
// CacheStatistics implementation
//=======================================================================================
Napi::Object CacheStatistics::ToJavaScript(Napi::Env env) const {
    Napi::Object obj = Napi::Object::New(env);
    
    obj.Set("featureCacheSize", Napi::Number::New(env, static_cast<double>(featureCacheSize)));
    obj.Set("visibilityCacheSize", Napi::Number::New(env, static_cast<double>(visibilityCacheSize)));
    obj.Set("viewShedCacheSize", Napi::Number::New(env, static_cast<double>(viewShedCacheSize)));
    obj.Set("drainageCacheSize", Napi::Number::New(env, static_cast<double>(drainageCacheSize)));
    obj.Set("totalMemoryUsage", Napi::Number::New(env, static_cast<double>(totalMemoryUsage)));
    obj.Set("hitRatio", Napi::Number::New(env, hitRatio));
    obj.Set("totalRequests", Napi::Number::New(env, totalRequests));
    obj.Set("cacheHits", Napi::Number::New(env, cacheHits));
    obj.Set("cacheMisses", Napi::Number::New(env, cacheMisses));
    
    return obj;
}

//=======================================================================================
// NativeDTMCaching implementation
//=======================================================================================
NativeDTMCaching::NativeDTMCaching(const Napi::CallbackInfo& info) 
    : TerrainModelObjectWrap<NativeDTMCaching>(info)
    , m_dtm(nullptr)
    , m_cachingEnabled(true)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsObject()) {
        ThrowTerrainModelError(env, "Expected DTM object as first argument");
        return;
    }

    // Get the DTM object
    Napi::Object dtmObj = info[0].As<Napi::Object>();
    if (!NativeDTM::constructor.InstanceOf(dtmObj)) {
        ThrowTerrainModelError(env, "Expected DTM object");
        return;
    }

    m_dtm = NativeDTM::Unwrap(dtmObj);
    if (!m_dtm || !m_dtm->IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM object");
        return;
    }

    // Keep a reference to the DTM object
    m_dtmRef = Napi::Persistent(dtmObj);

    try {
        InitializeFromDTM(m_dtm);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Error initializing DTM caching: " + std::string(e.what()));
        return;
    }
}

NativeDTMCaching::~NativeDTMCaching() {
    if (!m_dtmRef.IsEmpty()) {
        m_dtmRef.Reset();
    }
    
    // Clear all caches
    m_featureCache.clear();
    m_visibilityCache.clear();
    m_viewShedCache.clear();
    m_drainageCache.clear();
    
    // Clear callbacks
    if (!m_featureBrowsingCallback.IsEmpty()) {
        m_featureBrowsingCallback.Reset();
    }
    if (!m_visibilityAnalysisCallback.IsEmpty()) {
        m_visibilityAnalysisCallback.Reset();
    }
    if (!m_drainageAnalysisCallback.IsEmpty()) {
        m_drainageAnalysisCallback.Reset();
    }
}

void NativeDTMCaching::Init(Napi::Env env, Napi::Object exports) {
    Napi::HandleScope scope(env);
    
    Napi::Function func = DefineClass(env, "DTMCaching", {
        // Properties
        InstanceAccessor("cachingEnabled", &NativeDTMCaching::GetCachingEnabled, &NativeDTMCaching::SetCachingEnabled),
        InstanceAccessor("cacheSize", &NativeDTMCaching::GetCacheSize, nullptr),
        
        // Cache management
        InstanceMethod("emptyCache", &NativeDTMCaching::EmptyCache),
        InstanceMethod("clearFeatureCache", &NativeDTMCaching::ClearFeatureCache),
        InstanceMethod("clearVisibilityCache", &NativeDTMCaching::ClearVisibilityCache),
        InstanceMethod("clearViewShedCache", &NativeDTMCaching::ClearViewShedCache),
        InstanceMethod("clearDrainageCache", &NativeDTMCaching::ClearDrainageCache),
        
        // Feature browsing and caching
        InstanceMethod("browseFeatures", &NativeDTMCaching::BrowseFeatures),
        InstanceMethod("browseFeaturesWithCallback", &NativeDTMCaching::BrowseFeaturesWithCallback),
        InstanceMethod("addFeature", &NativeDTMCaching::AddFeature),
        InstanceMethod("getCachedFeatures", &NativeDTMCaching::GetCachedFeatures),
        
        // Visibility analysis
        InstanceMethod("browseTinPointsVisibility", &NativeDTMCaching::BrowseTinPointsVisibility),
        InstanceMethod("browseTinLinesVisibility", &NativeDTMCaching::BrowseTinLinesVisibility),
        InstanceMethod("analyzePointVisibility", &NativeDTMCaching::AnalyzePointVisibility),
        InstanceMethod("analyzeLineVisibility", &NativeDTMCaching::AnalyzeLineVisibility),
        InstanceMethod("getVisibilityAnalysisResult", &NativeDTMCaching::GetVisibilityAnalysisResult),
        
        // View shed analysis
        InstanceMethod("browseRadialViewSheds", &NativeDTMCaching::BrowseRadialViewSheds),
        InstanceMethod("browseRegionViewSheds", &NativeDTMCaching::BrowseRegionViewSheds),
        InstanceMethod("calculateViewShed", &NativeDTMCaching::CalculateViewShed),
        InstanceMethod("calculateRadialViewShed", &NativeDTMCaching::CalculateRadialViewShed),
        InstanceMethod("getViewShedAnalysisResult", &NativeDTMCaching::GetViewShedAnalysisResult),
        
        // Drainage analysis
        InstanceMethod("browseDrainageFeatures", &NativeDTMCaching::BrowseDrainageFeatures),
        InstanceMethod("analyzeDrainage", &NativeDTMCaching::AnalyzeDrainage),
        InstanceMethod("findLowPoints", &NativeDTMCaching::FindLowPoints),
        InstanceMethod("traceDrainagePath", &NativeDTMCaching::TraceDrainagePath),
        InstanceMethod("getDrainageAnalysisResult", &NativeDTMCaching::GetDrainageAnalysisResult),
        
        // Advanced analysis
        InstanceMethod("performSightlineAnalysis", &NativeDTMCaching::PerformSightlineAnalysis),
        InstanceMethod("calculateIntervisibility", &NativeDTMCaching::CalculateIntervisibility),
        InstanceMethod("analyzeTerrainProfile", &NativeDTMCaching::AnalyzeTerrainProfile),
        InstanceMethod("findOptimalViewpoints", &NativeDTMCaching::FindOptimalViewpoints),
        
        // Callback management
        InstanceMethod("setFeatureBrowsingCallback", &NativeDTMCaching::SetFeatureBrowsingCallback),
        InstanceMethod("setVisibilityAnalysisCallback", &NativeDTMCaching::SetVisibilityAnalysisCallback),
        InstanceMethod("setDrainageAnalysisCallback", &NativeDTMCaching::SetDrainageAnalysisCallback),
        InstanceMethod("fireCallback", &NativeDTMCaching::FireCallback),
        
        // Performance and optimization
        InstanceMethod("optimizeCache", &NativeDTMCaching::OptimizeCache),
        InstanceMethod("getCacheStatistics", &NativeDTMCaching::GetCacheStatistics),
        InstanceMethod("preloadAnalysisData", &NativeDTMCaching::PreloadAnalysisData),
        
        // Utility
        InstanceMethod("dispose", &NativeDTMCaching::Dispose),
    });

    exports.Set("DTMCaching", func);
}

//=======================================================================================
// Property accessors
//=======================================================================================
Napi::Value NativeDTMCaching::GetCachingEnabled(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "getCachingEnabled");
    
    return Napi::Boolean::New(env, m_cachingEnabled);
}

void NativeDTMCaching::SetCachingEnabled(const Napi::CallbackInfo& info, const Napi::Value& value) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "setCachingEnabled");
    
    if (!value.IsBoolean()) {
        Napi::TypeError::New(env, "Expected boolean value").ThrowAsJavaScriptException();
        return;
    }
    
    m_cachingEnabled = value.As<Napi::Boolean>().Value();
    
    // If caching is disabled, clear all caches
    if (!m_cachingEnabled) {
        m_featureCache.clear();
        m_visibilityCache.clear();
        m_viewShedCache.clear();
        m_drainageCache.clear();
    }
}

Napi::Value NativeDTMCaching::GetCacheSize(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "getCacheSize");
    
    size_t totalSize = m_featureCache.size() + m_visibilityCache.size() + 
                      m_viewShedCache.size() + m_drainageCache.size();
    
    return Napi::Number::New(env, static_cast<double>(totalSize));
}

//=======================================================================================
// Cache management
//=======================================================================================
Napi::Value NativeDTMCaching::EmptyCache(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "emptyCache");
    
    m_featureCache.clear();
    m_visibilityCache.clear();
    m_viewShedCache.clear();
    m_drainageCache.clear();
    
    return env.Undefined();
}

Napi::Value NativeDTMCaching::ClearFeatureCache(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "clearFeatureCache");
    
    m_featureCache.clear();
    
    return env.Undefined();
}

Napi::Value NativeDTMCaching::ClearVisibilityCache(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "clearVisibilityCache");
    
    m_visibilityCache.clear();
    
    return env.Undefined();
}

Napi::Value NativeDTMCaching::ClearViewShedCache(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "clearViewShedCache");
    
    m_viewShedCache.clear();
    
    return env.Undefined();
}

Napi::Value NativeDTMCaching::ClearDrainageCache(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "clearDrainageCache");
    
    m_drainageCache.clear();
    
    return env.Undefined();
}

//=======================================================================================
// Feature browsing and caching
//=======================================================================================
Napi::Value NativeDTMCaching::BrowseFeatures(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    CheckDisposed(env, "browseFeatures");
    
    if (!IsValidCaching()) {
        ThrowTerrainModelError(env, "Invalid DTM caching object");
        return env.Null();
    }
    
    try {
        // Generate cache key
        std::string cacheKey = GenerateCacheKey("browseFeatures", {});
        
        // Check cache first
        if (m_cachingEnabled && m_featureCache.find(cacheKey) != m_featureCache.end()) {
            return TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, m_featureCache[cacheKey]);
        }
        
        // Get features from DTM
        std::vector<Point3D> features;
        // TODO: Implement actual feature browsing from DTM
        
        // Cache the result
        if (m_cachingEnabled) {
            AddToFeatureCache(cacheKey, features);
        }
        
        return TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, features);
        
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Browse features failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// Internal helper methods
//=======================================================================================
void NativeDTMCaching::InitializeFromDTM(NativeDTM* dtm) {
    if (!dtm) {
        throw std::invalid_argument("DTM cannot be null");
    }
    
    // Initialize caching system
    m_cachingEnabled = true;
    
    // Pre-allocate cache space for better performance
    m_featureCache.reserve(100);
    m_visibilityCache.reserve(50);
    m_viewShedCache.reserve(50);
    m_drainageCache.reserve(50);
}

std::string NativeDTMCaching::GenerateCacheKey(const std::string& operation, const std::vector<std::string>& parameters) {
    std::ostringstream oss;
    oss << operation;
    
    for (const auto& param : parameters) {
        oss << "_" << param;
    }
    
    // Add timestamp for time-sensitive operations
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
    oss << "_" << timestamp;
    
    return oss.str();
}

bool NativeDTMCaching::IsCacheValid(const std::string& key) {
    // Simple validation - check if key exists
    // A more sophisticated implementation would check timestamps, DTM modifications, etc.
    return m_featureCache.find(key) != m_featureCache.end() ||
           m_visibilityCache.find(key) != m_visibilityCache.end() ||
           m_viewShedCache.find(key) != m_viewShedCache.end() ||
           m_drainageCache.find(key) != m_drainageCache.end();
}

void NativeDTMCaching::InvalidateRelatedCache(const std::string& operation) {
    // Remove cache entries related to the operation
    auto it = m_featureCache.begin();
    while (it != m_featureCache.end()) {
        if (it->first.find(operation) != std::string::npos) {
            it = m_featureCache.erase(it);
        } else {
            ++it;
        }
    }
    
    // Similar logic for other caches...
}

void NativeDTMCaching::AddToFeatureCache(const std::string& key, const std::vector<Point3D>& features) {
    if (m_cachingEnabled) {
        m_featureCache[key] = features;
        
        // Limit cache size to prevent memory issues
        if (m_featureCache.size() > 1000) {
            // Remove oldest entries (simple LRU-like behavior)
            auto it = m_featureCache.begin();
            std::advance(it, 100);
            m_featureCache.erase(m_featureCache.begin(), it);
        }
    }
}

Napi::Value NativeDTMCaching::Dispose(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (!m_disposed) {
        // Clear all caches
        m_featureCache.clear();
        m_visibilityCache.clear();
        m_viewShedCache.clear();
        m_drainageCache.clear();
        
        // Clear callbacks
        if (!m_featureBrowsingCallback.IsEmpty()) {
            m_featureBrowsingCallback.Reset();
        }
        if (!m_visibilityAnalysisCallback.IsEmpty()) {
            m_visibilityAnalysisCallback.Reset();
        }
        if (!m_drainageAnalysisCallback.IsEmpty()) {
            m_drainageAnalysisCallback.Reset();
        }
        
        if (!m_dtmRef.IsEmpty()) {
            m_dtmRef.Reset();
        }
        
        m_dtm = nullptr;
        m_disposed = true;
    }
    
    return env.Undefined();
}

} // namespace TerrainModelNodeAddon
