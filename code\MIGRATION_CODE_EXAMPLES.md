# TerrainModelNET to TerrainModelCore Code Migration Examples

## Overview

This document provides side-by-side code examples showing how TerrainModelNET C++/CLI code has been migrated to pure C++17 in TerrainModelCore.

## 1. DTM Class Creation and Basic Operations

### TerrainModelNET (C++/CLI)
```cpp
// DTM.h
public ref class DTM {
private:
    BcDTM* m_nativeDtm;
    ReleaseMarshaller^ m_marshaller;
    
public:
    DTM(BcDTM* nativeDtm) {
        m_nativeDtm = nativeDtm;
        m_marshaller = gcnew ReleaseMarshaller();
    }
    
    static DTM^ CreateFromFile(String^ fileName) {
        pin_ptr<const wchar_t> ch = PtrToStringChars(fileName);
        BcDTMPtr bcDtmP = BcDTM::CreateFromTinFile(ch);
        if (bcDtmP.IsNull()) 
            DTMException::CheckForErrorStatus(1);
        return gcnew DTM(bcDtmP.get());
    }
    
    DTMFeatureId AddPointFeature(BGEO::DPoint3d point, DTMUserTag userTag) {
        DPoint3d pt;
        DTMFeatureId featureID = DTM_NULL_FEATURE_ID;
        DTMHelpers::Copy(pt, point);
        DTMException::CheckForErrorStatus(
            m_nativeDtm->AddPointFeature(pt, userTag, &featureID));
        CheckMemoryPressure();
        return featureID;
    }
};
```

### TerrainModelCore (Pure C++)
```cpp
// DTM.h
class DTM {
private:
    std::unordered_map<DTMVertexId, DTMVertexPtr> m_vertices;
    std::unordered_map<DTMTriangleId, DTMTrianglePtr> m_triangles;
    std::unique_ptr<SpatialIndex> m_spatialIndex;
    std::unique_ptr<TriangulationEngine> m_triangulationEngine;
    
public:
    DTM() : m_isTriangulated(false),
            m_spatialIndex(SpatialIndex::Create("adaptive")),
            m_triangulationEngine(std::make_unique<TriangulationEngine>()) {
        // Initialize bounds and state
    }
    
    static DTMPtr CreateFromFile(const std::string& fileName) {
        auto dtm = Create();
        auto& fileManager = FileIOManager::Instance();
        FileIOResult result = fileManager.LoadDTM(fileName, *dtm);
        return result.success ? dtm : nullptr;
    }
    
    DTMFeatureId AddPointFeature(const Point3D& point, DTMUserTag userTag = 0) {
        if (!IsValidPoint(point)) {
            return Constants::INVALID_ID;
        }
        
        DTMVertexId vertexId = CreateVertex(point);
        if (vertexId == Constants::INVALID_ID) {
            return Constants::INVALID_ID;
        }
        
        DTMFeatureId featureId = m_nextFeatureId++;
        auto feature = std::make_shared<DTMFeature>(featureId, DTMFeatureType::Point);
        feature->SetUserTag(userTag);
        feature->AddPoint(point);
        
        m_features[featureId] = feature;
        UpdateBounds(point);
        m_isTriangulated = false;
        
        return featureId;
    }
};
```

## 2. Triangulation Implementation

### TerrainModelNET (C++/CLI)
```cpp
// DTM.cpp
TriangulationReport DTM::Triangulate() {
    TriangulationReport ret;
    ret.Success = true;
    
    // Set the autoCleanOptions
    Handle->SetCleanUpOptions(DTMCleanupFlags::All);
    DTMException::CheckForErrorStatus(Handle->Triangulate());
    CheckMemoryPressure();
    
    return ret;
}

bool DTM::SetTriangulationParameters(double pointTol, double lineTol, 
                                    DTMEdgeOption edgeOption, double maxSide) {
    DTMException::CheckForErrorStatus(
        Handle->SetTriangulationParameters(pointTol, lineTol, 
                                         (long)edgeOption, maxSide));
    return true;
}
```

### TerrainModelCore (Pure C++)
```cpp
// DTM.cpp
TriangulationResult DTM::Triangulate(const TriangulationParameters& parameters) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    try {
        if (m_vertices.size() < 3) {
            result.status = DTMStatus::InsufficientData;
            result.message = "Need at least 3 points for triangulation";
            return result;
        }
        
        // Configure triangulation engine
        TriangulationEngine::Configuration config(parameters);
        m_triangulationEngine->SetConfiguration(config);
        
        // Extract points for triangulation
        std::vector<Point3D> points;
        points.reserve(m_vertices.size());
        
        for (const auto& pair : m_vertices) {
            if (pair.second->IsActive()) {
                points.push_back(pair.second->GetPosition());
            }
        }
        
        // Perform triangulation with progress reporting
        TriangulationResult engineResult = m_triangulationEngine->Triangulate(points);
        
        if (engineResult.isSuccess()) {
            // Copy results and update spatial index
            UpdateTriangulationResults(engineResult);
            UpdateSpatialIndex();
            m_isTriangulated = true;
            
            result.verticesCount = m_vertices.size();
            result.trianglesCount = m_triangles.size();
            result.edgesCount = m_edges.size();
            result.message = "Triangulation completed successfully";
        } else {
            result = engineResult;
        }
        
    } catch (const std::exception& e) {
        result.status = DTMStatus::TriangulationFailed;
        result.message = "Triangulation failed: " + std::string(e.what());
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.elapsedTime = duration.count() / 1000.0;
    
    return result;
}

void DTM::SetTriangulationParameters(const TriangulationParameters& params) {
    m_triangulationParams = params;
    if (m_triangulationEngine) {
        TriangulationEngine::Configuration config(params);
        m_triangulationEngine->SetConfiguration(config);
    }
}
```

## 3. Feature Management

### TerrainModelNET (C++/CLI)
```cpp
// DTMFeatureEnumerator.h
public ref class DTMFeatureEnumerator {
private:
    BcDTMFeatureEnumerator* m_native;
    ReleaseMarshaller^ m_marshaller;
    
public:
    DTMFeatureEnumerator(DTM^ dtm) {
        m_native = dtm->Handle->CreateFeatureEnumerator();
        m_marshaller = gcnew ReleaseMarshaller();
    }
    
    void IncludeAllFeatures() {
        m_native->IncludeAllFeatures();
    }
    
    void IncludeFeature(DTMFeatureType type) {
        m_native->IncludeFeature((::DTMFeatureType)type);
    }
    
    virtual IEnumerator<DTMFeatureInfo^>^ GetEnumerator() {
        return gcnew Enumerator(*m_native, m_marshaller);
    }
};
```

### TerrainModelCore (Pure C++)
```cpp
// DTMFeatureEnumerator.h
class DTMFeatureEnumerator {
private:
    const DTM* m_dtm;
    std::set<DTMFeatureType> m_includedTypes;
    DTMUserTagRange m_userTagRange;
    bool m_includeAll;
    
    // Iterator state
    std::unordered_map<DTMFeatureId, DTMFeaturePtr>::const_iterator m_current;
    std::unordered_map<DTMFeatureId, DTMFeaturePtr>::const_iterator m_end;
    
public:
    explicit DTMFeatureEnumerator(const DTM* dtm) 
        : m_dtm(dtm), m_includeAll(false) {
        Reset();
    }
    
    void IncludeAllFeatures() {
        m_includeAll = true;
        m_includedTypes.clear();
        Reset();
    }
    
    void IncludeFeature(DTMFeatureType type) {
        m_includeAll = false;
        m_includedTypes.insert(type);
        Reset();
    }
    
    void ExcludeFeature(DTMFeatureType type) {
        m_includedTypes.erase(type);
        Reset();
    }
    
    bool MoveNext() {
        while (m_current != m_end) {
            const auto& feature = m_current->second;
            ++m_current;
            
            if (ShouldIncludeFeature(feature)) {
                return true;
            }
        }
        return false;
    }
    
    DTMFeaturePtr Current() const {
        if (m_current != m_dtm->GetFeatures().begin()) {
            auto prev = m_current;
            --prev;
            return prev->second;
        }
        return nullptr;
    }
    
private:
    void Reset() {
        const auto& features = m_dtm->GetFeatures();
        m_current = features.begin();
        m_end = features.end();
    }
    
    bool ShouldIncludeFeature(DTMFeaturePtr feature) const {
        if (!feature || !feature->IsActive()) {
            return false;
        }
        
        if (m_includeAll) {
            return true;
        }
        
        if (m_includedTypes.find(feature->GetFeatureType()) == m_includedTypes.end()) {
            return false;
        }
        
        DTMUserTag tag = feature->GetUserTag();
        return tag >= m_userTagRange.minTag && tag <= m_userTagRange.maxTag;
    }
};
```

## 4. Water Analysis Implementation

### TerrainModelNET (C++/CLI)
```cpp
// WaterAnalysis.cpp
public ref class WaterAnalysis {
private:
    BcWaterAnalysis* m_analysis;
    DTM^ m_dtm;
    
public:
    WaterAnalysis(DTM^ dtm) {
        m_dtm = dtm;
        m_analysis = new BcWaterAnalysis(dtm->Handle);
    }
    
    void DoTrace(BGEO::DPoint3d startPt) {
        DPoint3d pt;
        DTMHelpers::Copy(pt, startPt);
        m_analysis->DoTrace(pt);
    }
    
    WaterAnalysisResult^ GetResult() {
        return gcnew WaterAnalysisResult(*m_analysis->GetResult());
    }
};
```

### TerrainModelCore (Pure C++)
```cpp
// WaterAnalysis.cpp
class WaterAnalysis {
private:
    DTMPtr m_dtm;
    std::unordered_map<DTMTriangleId, Point3D> m_flowDirections;
    std::unordered_map<DTMTriangleId, double> m_flowAccumulation;
    double m_flowThreshold;
    int m_maxIterations;
    
public:
    explicit WaterAnalysis(DTMPtr dtm) 
        : m_dtm(dtm), m_flowThreshold(0.001), m_maxIterations(10000) {
        if (!m_dtm || !m_dtm->IsTriangulated()) {
            throw std::runtime_error("DTM must be triangulated for water analysis");
        }
        InitializeFlowNetwork();
    }
    
    WaterFlowResult TraceFlow(const Point3D& startPoint) {
        WaterFlowResult result;
        
        try {
            DrapedPoint drapedStart = m_dtm->DrapePoint(startPoint);
            if (!drapedStart.isValid) {
                return WaterFlowResult::Failure("Start point is outside DTM bounds");
            }
            
            FlowPath flowPath;
            flowPath.startPoint = drapedStart.drapedPoint;
            flowPath.points.push_back(drapedStart.drapedPoint);
            
            Point3D currentPoint = drapedStart.drapedPoint;
            DTMTriangleId currentTriangle = drapedStart.triangleId;
            
            // Trace flow until we reach a sink or boundary
            for (int iteration = 0; iteration < m_maxIterations; ++iteration) {
                Point3D nextPoint;
                DTMTriangleId nextTriangle;
                
                if (!FindNextFlowPoint(currentPoint, currentTriangle, nextPoint, nextTriangle)) {
                    break; // Reached sink or boundary
                }
                
                double distance = GeometryUtils::Distance3D(currentPoint, nextPoint);
                if (distance < m_convergenceTolerance) {
                    break; // Converged
                }
                
                flowPath.points.push_back(nextPoint);
                flowPath.totalLength += distance;
                flowPath.elevationDrop += std::max(0.0, currentPoint.z - nextPoint.z);
                
                currentPoint = nextPoint;
                currentTriangle = nextTriangle;
            }
            
            flowPath.endPoint = currentPoint;
            CalculateFlowCharacteristics(flowPath);
            
            result.flowPaths.push_back(flowPath);
            result.success = true;
            result.message = "Flow tracing completed successfully";
            
        } catch (const std::exception& e) {
            result = WaterFlowResult::Failure("Flow tracing failed: " + std::string(e.what()));
        }
        
        return result;
    }
    
private:
    void InitializeFlowNetwork() {
        m_flowDirections.clear();
        m_flowAccumulation.clear();
        
        const auto& triangles = m_dtm->GetTriangles();
        for (const auto& pair : triangles) {
            if (pair.second->IsActive()) {
                Point3D flowDirection = CalculateFlowDirection(pair.first);
                m_flowDirections[pair.first] = flowDirection;
                m_flowAccumulation[pair.first] = 0.0;
            }
        }
    }
    
    Point3D CalculateFlowDirection(DTMTriangleId triangleId) {
        const auto& triangles = m_dtm->GetTriangles();
        auto triangleIt = triangles.find(triangleId);
        
        if (triangleIt == triangles.end()) {
            return Point3D(0.0, 0.0, 0.0);
        }
        
        const auto& triangle = triangleIt->second;
        const auto& vertices = m_dtm->GetVertices();
        
        // Get triangle normal and calculate steepest descent
        Point3D normal = triangle->GetNormal(vertices);
        Point3D flowDirection(-normal.x, -normal.y, 0.0);
        
        return GeometryUtils::Normalize(flowDirection);
    }
};
```

## 5. Memory Management Comparison

### TerrainModelNET (C++/CLI)
```cpp
// Managed memory with garbage collection
DTM^ dtm = gcnew DTM();
array<BGEO::DPoint3d>^ points = gcnew array<BGEO::DPoint3d>(1000);

// Manual pinning for native interop
pin_ptr<BGEO::DPoint3d const> tPoint = &points[0];
Handle->AddPointFeature((DPoint3d*)tPoint, points->Length, userTag, &featureID);

// Explicit cleanup required
dtm->~DTM();
```

### TerrainModelCore (Pure C++)
```cpp
// RAII with smart pointers
auto dtm = DTM::Create();
std::vector<Point3D> points;
points.reserve(1000);

// Direct native operations
for (const auto& point : points) {
    dtm->AddPointFeature(point);
}

// Automatic cleanup when dtm goes out of scope
// No explicit cleanup needed
```

## Key Migration Benefits Demonstrated

1. **Simplified Memory Management**: RAII eliminates manual cleanup
2. **Better Performance**: Direct native operations without CLR overhead
3. **Enhanced Error Handling**: Rich result types with detailed error information
4. **Modern C++ Features**: Smart pointers, STL containers, lambda functions
5. **Cross-Platform Compatibility**: No Windows-specific dependencies
6. **Improved Maintainability**: Cleaner code structure and better separation of concerns

The migration to pure C++17 provides significant improvements in performance, maintainability, and platform compatibility while preserving all essential functionality.
