/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// DTM Draped Linear Element Point Code enumeration
//=======================================================================================
enum class DTMDrapedLinearElementPointCode {
    External = 0,
    Triangle = 1,
    Void = 2,
    PointOrSide = 3,
    FeaturePoint = 4,
    FeatureLine = 5
};

//=======================================================================================
// DTM Drape Point Feature structure
//=======================================================================================
struct DTMDrapePointFeature {
    DTMFeatureType featureType;
    DTMFeatureId featureId;
    DTMUserTag userTag;
    
    DTMDrapePointFeature() : featureType(DTMFeatureType::Point), featureId(0), userTag(0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static DTMDrapePointFeature FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Native DTM Draped Linear Element Point wrapper class
//=======================================================================================
class NativeDTMDrapedLinearElementPoint : public TerrainModelObjectWrap<NativeDTMDrapedLinearElementPoint>
{
private:
    Point3D m_originalPoint;
    Point3D m_drapedPoint;
    DTMDrapedLinearElementPointCode m_code;
    std::vector<DTMFeatureId> m_featureIds;
    std::vector<DTMDrapePointFeature> m_features;
    double m_distanceFromStart;
    double m_elevation;

public:
    // Constructor
    NativeDTMDrapedLinearElementPoint(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMDrapedLinearElementPoint() = default;

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetOriginalPoint(const Napi::CallbackInfo& info);
    Napi::Value GetDrapedPoint(const Napi::CallbackInfo& info);
    Napi::Value GetCode(const Napi::CallbackInfo& info);
    Napi::Value GetFeatureIds(const Napi::CallbackInfo& info);
    Napi::Value GetFeatures(const Napi::CallbackInfo& info);
    Napi::Value GetDistanceFromStart(const Napi::CallbackInfo& info);
    Napi::Value GetElevation(const Napi::CallbackInfo& info);

    // Instance methods
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value IsValid(const Napi::CallbackInfo& info);

    // Factory method for creating from native point
    static Napi::Object CreateFromNative(Napi::Env env, BcDTMDrapedLinePoint* nativePoint, double distanceFromStart);

private:
    // Helper methods
    void InitializeFromNative(BcDTMDrapedLinePoint* nativePoint, double distanceFromStart);
    static std::string GetCodeName(DTMDrapedLinearElementPointCode code);
};

//=======================================================================================
// Native DTM Draped Linear Element wrapper class
//=======================================================================================
class NativeDTMDrapedLinearElement : public TerrainModelObjectWrap<NativeDTMDrapedLinearElement>
{
private:
    BcDTMDrapedLine* m_nativeDrapedLine;
    std::vector<Point3D> m_originalPoints;
    std::vector<NativeDTMDrapedLinearElementPoint*> m_drapedPoints;
    double m_totalLength;
    bool m_isValid;

public:
    // Constructor
    NativeDTMDrapedLinearElement(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMDrapedLinearElement();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetOriginalPoints(const Napi::CallbackInfo& info);
    Napi::Value GetDrapedPoints(const Napi::CallbackInfo& info);
    Napi::Value GetPointCount(const Napi::CallbackInfo& info);
    Napi::Value GetTotalLength(const Napi::CallbackInfo& info);
    Napi::Value GetIsValid(const Napi::CallbackInfo& info);

    // Instance methods - Point access
    Napi::Value GetPoint(const Napi::CallbackInfo& info);
    Napi::Value GetPointAt(const Napi::CallbackInfo& info);
    Napi::Value GetElevationAt(const Napi::CallbackInfo& info);
    Napi::Value GetSlopeAt(const Napi::CallbackInfo& info);

    // Instance methods - Analysis
    Napi::Value GetMinElevation(const Napi::CallbackInfo& info);
    Napi::Value GetMaxElevation(const Napi::CallbackInfo& info);
    Napi::Value GetAverageElevation(const Napi::CallbackInfo& info);
    Napi::Value GetAverageSlope(const Napi::CallbackInfo& info);
    Napi::Value GetTotalRise(const Napi::CallbackInfo& info);
    Napi::Value GetTotalFall(const Napi::CallbackInfo& info);

    // Instance methods - Segmentation
    Napi::Value GetSegments(const Napi::CallbackInfo& info);
    Napi::Value GetSegmentAt(const Napi::CallbackInfo& info);
    Napi::Value InterpolateAt(const Napi::CallbackInfo& info);

    // Instance methods - Export
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value ToPointArray(const Napi::CallbackInfo& info);
    Napi::Value ToElevationProfile(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    BcDTMDrapedLine* GetHandle() const { return m_nativeDrapedLine; }
    bool IsValidElement() const { return m_nativeDrapedLine != nullptr && m_isValid && !m_disposed; }

    // Factory method for creating from native draped line
    static Napi::Object CreateFromNative(Napi::Env env, BcDTMDrapedLine* nativeDrapedLine);

private:
    // Helper methods
    void InitializeFromNative(BcDTMDrapedLine* nativeDrapedLine);
    void PopulateDrapedPoints();
    void CalculateTotalLength();
    double CalculateDistanceFromStart(int pointIndex);
    Point3D InterpolatePosition(double distance);
    double InterpolateElevation(double distance);
};

//=======================================================================================
// Linear Element Segment structure
//=======================================================================================
struct LinearElementSegment {
    int startIndex;
    int endIndex;
    Point3D startPoint;
    Point3D endPoint;
    double length;
    double slope;
    double bearing;
    
    LinearElementSegment() : startIndex(-1), endIndex(-1), length(0.0), slope(0.0), bearing(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static LinearElementSegment FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Elevation Profile structure
//=======================================================================================
struct ElevationProfile {
    std::vector<double> distances;
    std::vector<double> elevations;
    std::vector<double> slopes;
    double totalLength;
    double minElevation;
    double maxElevation;
    double totalRise;
    double totalFall;
    
    ElevationProfile() : totalLength(0.0), minElevation(0.0), maxElevation(0.0), totalRise(0.0), totalFall(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static ElevationProfile FromJavaScript(Napi::Object obj);
};

} // namespace TerrainModelNodeAddon
