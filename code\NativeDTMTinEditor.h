/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Native DTM Tin Editor wrapper class for advanced DTM editing operations
//=======================================================================================
class NativeDTMTinEditor : public TerrainModelObjectWrap<NativeDTMTinEditor>
{
private:
    BcDTMPtr m_nativeDtm;
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;
    std::vector<Point3D> m_featurePoints;

public:
    // Constructor
    NativeDTMTinEditor(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMTinEditor();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Instance methods - Feature editing
    Napi::Value SetFeaturePoints(const Napi::CallbackInfo& info);
    Napi::Value GetFeaturePoints(const Napi::CallbackInfo& info);
    Napi::Value AddFeaturePoint(const Napi::CallbackInfo& info);
    Napi::Value InsertFeaturePoint(const Napi::CallbackInfo& info);
    Napi::Value RemoveFeaturePoint(const Napi::CallbackInfo& info);
    Napi::Value ClearFeaturePoints(const Napi::CallbackInfo& info);

    // Instance methods - TIN editing operations
    Napi::Value StartEdit(const Napi::CallbackInfo& info);
    Napi::Value EndEdit(const Napi::CallbackInfo& info);
    Napi::Value CancelEdit(const Napi::CallbackInfo& info);
    Napi::Value IsEditing(const Napi::CallbackInfo& info);

    // Instance methods - Triangle operations
    Napi::Value FlipEdge(const Napi::CallbackInfo& info);
    Napi::Value SplitTriangle(const Napi::CallbackInfo& info);
    Napi::Value MergeTriangles(const Napi::CallbackInfo& info);
    Napi::Value DeleteTriangle(const Napi::CallbackInfo& info);

    // Instance methods - Vertex operations
    Napi::Value MoveVertex(const Napi::CallbackInfo& info);
    Napi::Value DeleteVertex(const Napi::CallbackInfo& info);
    Napi::Value InsertVertex(const Napi::CallbackInfo& info);

    // Instance methods - Edge operations
    Napi::Value SwapEdge(const Napi::CallbackInfo& info);
    Napi::Value ConstrainEdge(const Napi::CallbackInfo& info);
    Napi::Value UnconstrainEdge(const Napi::CallbackInfo& info);

    // Instance methods - Validation and repair
    Napi::Value ValidateTriangulation(const Napi::CallbackInfo& info);
    Napi::Value RepairTriangulation(const Napi::CallbackInfo& info);
    Napi::Value OptimizeTriangulation(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    BcDTMPtr GetHandle() const { return m_nativeDtm; }
    bool IsValidEditor() const { return m_nativeDtm.IsValid() && !m_disposed; }

    // Factory method for creating from DTM
    static Napi::Object CreateFromDTM(Napi::Env env, NativeDTM* dtm);

private:
    // Helper methods
    void InitializeFromDTM(NativeDTM* dtm);
    bool ValidateEditingState(Napi::Env env, const std::string& operation);
    void UpdateFeaturePointsFromNative();
    void UpdateNativeFromFeaturePoints();
};

//=======================================================================================
// DTM Edit Operation Result structure
//=======================================================================================
struct DTMEditResult {
    bool success;
    std::string message;
    int affectedTriangles;
    int affectedVertices;
    
    DTMEditResult() : success(false), affectedTriangles(0), affectedVertices(0) {}
    DTMEditResult(bool s, const std::string& msg = "") 
        : success(s), message(msg), affectedTriangles(0), affectedVertices(0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static DTMEditResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Triangle information structure
//=======================================================================================
struct TriangleInfo {
    int triangleId;
    std::array<int, 3> vertexIndices;
    std::array<Point3D, 3> vertices;
    Point3D centroid;
    double area;
    bool isValid;
    
    TriangleInfo() : triangleId(-1), area(0.0), isValid(false) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static TriangleInfo FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Edge information structure
//=======================================================================================
struct EdgeInfo {
    int edgeId;
    std::array<int, 2> vertexIndices;
    std::array<Point3D, 2> vertices;
    double length;
    bool isConstrained;
    bool isBoundary;
    std::array<int, 2> adjacentTriangles; // -1 if no adjacent triangle
    
    EdgeInfo() : edgeId(-1), length(0.0), isConstrained(false), isBoundary(false) {
        adjacentTriangles[0] = adjacentTriangles[1] = -1;
    }
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static EdgeInfo FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Vertex information structure
//=======================================================================================
struct VertexInfo {
    int vertexId;
    Point3D position;
    std::vector<int> adjacentTriangles;
    std::vector<int> adjacentEdges;
    bool isBoundary;
    bool isConstrained;
    
    VertexInfo() : vertexId(-1), isBoundary(false), isConstrained(false) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static VertexInfo FromJavaScript(Napi::Object obj);
};

} // namespace TerrainModelNodeAddon
