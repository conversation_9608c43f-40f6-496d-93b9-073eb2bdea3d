/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

const assert = require('assert');
const path = require('path');
const fs = require('fs');

// Import the TerrainModel addon
let TerrainModel;
try {
  TerrainModel = require('../index');
} catch (error) {
  console.error('Failed to load TerrainModel addon:', error.message);
  console.error('Make sure the addon is built properly');
  process.exit(1);
}

/**
 * Integration test suite for TerrainModel C++ core functionality
 */
class IntegrationTestSuite {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
    this.startTime = Date.now();
  }
  
  async runAllTests() {
    console.log('🚀 TerrainModel Integration Test Suite');
    console.log('=====================================');
    console.log(`Node.js Version: ${process.version}`);
    console.log(`Platform: ${process.platform} ${process.arch}`);
    
    if (TerrainModel.version) {
      console.log(`TerrainModel Version: ${TerrainModel.version}`);
    }
    
    console.log('');
    
    try {
      // Core functionality tests
      await this.testCoreFramework();
      await this.testGeometryUtils();
      await this.testSpatialIndexing();
      await this.testTriangulation();
      await this.testAdvancedFeatures();
      await this.testPerformanceScenarios();
      await this.testMemoryManagement();
      await this.testErrorRecovery();
      
      this.printFinalSummary();
      
    } catch (error) {
      console.error('❌ Test suite crashed:', error.message);
      console.error(error.stack);
      process.exit(1);
    }
  }
  
  async testCoreFramework() {
    console.log('🏗️ Testing Core Framework...');
    
    await this.runTest('Module Exports', () => {
      assert(TerrainModel, 'TerrainModel module should be loaded');
      assert(typeof TerrainModel === 'object', 'TerrainModel should be an object');
      
      // Check essential exports
      assert(typeof TerrainModel.createDTM === 'function', 'Should export createDTM function');
      assert(typeof TerrainModel.createPoint3D === 'function', 'Should export createPoint3D function');
      
      // Check class constructors
      if (TerrainModel.DTMFeatureEnumerator) {
        assert(typeof TerrainModel.DTMFeatureEnumerator === 'function', 'DTMFeatureEnumerator should be a constructor');
      }
    });
    
    await this.runTest('DTM Lifecycle', () => {
      const dtm = TerrainModel.createDTM();
      assert(dtm, 'DTM should be created');
      assert(!dtm.disposed, 'DTM should not be disposed initially');
      
      // Test basic properties
      assert(typeof dtm.getVerticesCount === 'function', 'Should have getVerticesCount method');
      assert.strictEqual(dtm.getVerticesCount(), 0, 'Initial vertex count should be 0');
      
      // Test disposal
      dtm.dispose();
      assert(dtm.disposed, 'DTM should be marked as disposed');
    });
    
    await this.runTest('Point3D Creation', () => {
      const point = TerrainModel.createPoint3D(10.5, 20.3, 30.7);
      assert(point, 'Point3D should be created');
      assert.strictEqual(point.x, 10.5, 'X coordinate should match');
      assert.strictEqual(point.y, 20.3, 'Y coordinate should match');
      assert.strictEqual(point.z, 30.7, 'Z coordinate should match');
    });
  }
  
  async testGeometryUtils() {
    console.log('📐 Testing Geometry Utilities...');
    
    await this.runTest('Distance Calculations', () => {
      const p1 = TerrainModel.createPoint3D(0, 0, 0);
      const p2 = TerrainModel.createPoint3D(3, 4, 0);
      
      // Test if distance calculation is available (might be internal)
      // This test validates that points can be created and used
      assert(p1.x === 0 && p1.y === 0 && p1.z === 0, 'Point 1 should be at origin');
      assert(p2.x === 3 && p2.y === 4 && p2.z === 0, 'Point 2 should be at (3,4,0)');
    });
    
    await this.runTest('Point Validation', () => {
      // Test valid points
      const validPoint = TerrainModel.createPoint3D(100, 200, 300);
      assert(validPoint, 'Valid point should be created');
      
      // Test edge cases
      const zeroPoint = TerrainModel.createPoint3D(0, 0, 0);
      assert(zeroPoint, 'Zero point should be created');
      
      const negativePoint = TerrainModel.createPoint3D(-100, -200, -300);
      assert(negativePoint, 'Negative coordinates should be allowed');
    });
  }
  
  async testSpatialIndexing() {
    console.log('🗂️ Testing Spatial Indexing...');
    
    await this.runTest('Point Insertion and Retrieval', () => {
      const dtm = TerrainModel.createDTM();
      
      // Add points in a pattern
      const points = [];
      for (let i = 0; i < 100; i++) {
        const x = (i % 10) * 10;
        const y = Math.floor(i / 10) * 10;
        const z = 100 + Math.random() * 20;
        points.push(TerrainModel.createPoint3D(x, y, z));
      }
      
      dtm.addPoints(points);
      assert(dtm.getVerticesCount() >= points.length, 'All points should be added');
      
      dtm.dispose();
    });
    
    await this.runTest('Large Dataset Handling', () => {
      const dtm = TerrainModel.createDTM(5000, 1000); // Pre-allocate capacity
      
      // Generate a large dataset
      const points = [];
      for (let i = 0; i < 1000; i++) {
        points.push(TerrainModel.createPoint3D(
          Math.random() * 1000,
          Math.random() * 1000,
          100 + Math.random() * 50
        ));
      }
      
      const startTime = Date.now();
      dtm.addPoints(points);
      const endTime = Date.now();
      
      assert(dtm.getVerticesCount() >= points.length, 'All points should be added');
      console.log(`    ⏱️  Added 1000 points in ${endTime - startTime}ms`);
      
      dtm.dispose();
    });
  }
  
  async testTriangulation() {
    console.log('🔺 Testing Triangulation Engine...');
    
    await this.runTest('Basic Triangulation', () => {
      const dtm = TerrainModel.createDTM();
      
      // Add minimum points for triangulation
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 105),
        TerrainModel.createPoint3D(50, 100, 110),
        TerrainModel.createPoint3D(100, 100, 115)
      ];
      
      dtm.addPoints(points);
      const result = dtm.triangulate();
      
      assert(result, 'Triangulation result should be returned');
      assert(result.success, `Triangulation should succeed: ${result.message || 'No message'}`);
      assert(result.trianglesCount > 0, 'Should have triangles after triangulation');
      assert(dtm.isTriangulated, 'DTM should be marked as triangulated');
      
      console.log(`    📊 Created ${result.trianglesCount} triangles from ${result.verticesCount} vertices`);
      
      dtm.dispose();
    });
    
    await this.runTest('Complex Terrain Triangulation', () => {
      const dtm = TerrainModel.createDTM();
      
      // Create a more complex terrain with hills and valleys
      const points = [];
      for (let x = 0; x <= 200; x += 20) {
        for (let y = 0; y <= 200; y += 20) {
          const z = 100 + 
                   Math.sin(x / 50) * 15 + 
                   Math.cos(y / 50) * 10 + 
                   Math.sin((x + y) / 30) * 5;
          points.push(TerrainModel.createPoint3D(x, y, z));
        }
      }
      
      dtm.addPoints(points);
      
      const startTime = Date.now();
      const result = dtm.triangulate();
      const endTime = Date.now();
      
      assert(result.success, 'Complex triangulation should succeed');
      console.log(`    ⏱️  Triangulated ${points.length} points in ${endTime - startTime}ms`);
      console.log(`    📊 Result: ${result.trianglesCount} triangles, ${result.verticesCount} vertices`);
      
      dtm.dispose();
    });
    
    await this.runTest('Point Draping', () => {
      const dtm = TerrainModel.createDTM();
      
      // Setup a simple triangulated surface
      const points = [
        TerrainModel.createPoint3D(0, 0, 100),
        TerrainModel.createPoint3D(100, 0, 100),
        TerrainModel.createPoint3D(50, 100, 120),
        TerrainModel.createPoint3D(100, 100, 100)
      ];
      
      dtm.addPoints(points);
      const triangulationResult = dtm.triangulate();
      assert(triangulationResult.success, 'Triangulation should succeed for draping test');
      
      // Test point draping
      const testPoint = TerrainModel.createPoint3D(50, 50, 0);
      const drapedPoint = dtm.drapePoint(testPoint);
      
      assert(drapedPoint, 'Draped point should be returned');
      assert(drapedPoint.isValid, 'Draped point should be valid');
      assert(drapedPoint.drapedPoint, 'Should have draped point coordinates');
      assert(drapedPoint.drapedPoint.z > 0, 'Draped elevation should be positive');
      
      console.log(`    📍 Draped point: (${testPoint.x}, ${testPoint.y}) -> elevation ${drapedPoint.drapedPoint.z.toFixed(2)}`);
      
      dtm.dispose();
    });
  }
  
  async testAdvancedFeatures() {
    console.log('🔬 Testing Advanced Features...');
    
    await this.runTest('Feature Management', () => {
      const dtm = TerrainModel.createDTM();
      
      // Add point features
      const pointId1 = dtm.addPointFeature(TerrainModel.createPoint3D(10, 10, 100), 1);
      const pointId2 = dtm.addPointFeature(TerrainModel.createPoint3D(20, 20, 105), 2);
      
      assert(typeof pointId1 === 'number' && pointId1 > 0, 'Point feature 1 should have valid ID');
      assert(typeof pointId2 === 'number' && pointId2 > 0, 'Point feature 2 should have valid ID');
      assert(pointId1 !== pointId2, 'Feature IDs should be unique');
      
      // Add linear features if supported
      if (dtm.addLinearFeature && TerrainModel.DTMFeatureType) {
        const breaklinePoints = [
          TerrainModel.createPoint3D(0, 50, 102),
          TerrainModel.createPoint3D(50, 50, 107),
          TerrainModel.createPoint3D(100, 50, 112)
        ];
        
        const breaklineId = dtm.addLinearFeature(breaklinePoints, TerrainModel.DTMFeatureType.BreakLine, 10);
        assert(typeof breaklineId === 'number' && breaklineId > 0, 'Breakline should have valid ID');
      }
      
      dtm.dispose();
    });
    
    await this.runTest('Statistics and Analysis', () => {
      const dtm = TerrainModel.createDTM();
      
      // Create a dataset with known properties
      const points = [];
      for (let i = 0; i < 50; i++) {
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const z = 100 + Math.random() * 20; // Elevation between 100-120
        points.push(TerrainModel.createPoint3D(x, y, z));
      }
      
      dtm.addPoints(points);
      dtm.triangulate();
      
      // Test volume calculation if available
      if (dtm.calculateVolume) {
        const volume = dtm.calculateVolume(110); // Reference elevation
        assert(typeof volume === 'number', 'Volume should be a number');
        console.log(`    📊 Volume above elevation 110: ${volume.toFixed(2)}`);
      }
      
      // Test range calculation if available
      if (dtm.getRange3d) {
        const range = dtm.getRange3d();
        assert(range, 'Range should be returned');
        assert(range.low && range.high, 'Range should have low and high points');
        console.log(`    📏 Elevation range: ${range.low.z.toFixed(2)} - ${range.high.z.toFixed(2)}`);
      }
      
      dtm.dispose();
    });
  }
  
  async testPerformanceScenarios() {
    console.log('⚡ Testing Performance Scenarios...');
    
    await this.runTest('Large Dataset Performance', () => {
      const dtm = TerrainModel.createDTM(10000, 2000);
      
      // Generate a large regular grid
      const points = [];
      const gridSize = 50; // 50x50 = 2500 points
      
      for (let x = 0; x < gridSize; x++) {
        for (let y = 0; y < gridSize; y++) {
          const z = 100 + 
                   Math.sin(x / 10) * 10 + 
                   Math.cos(y / 10) * 5 + 
                   Math.random() * 2;
          points.push(TerrainModel.createPoint3D(x * 2, y * 2, z));
        }
      }
      
      console.log(`    📊 Testing with ${points.length} points...`);
      
      const addStartTime = Date.now();
      dtm.addPoints(points);
      const addEndTime = Date.now();
      
      const triangulateStartTime = Date.now();
      const result = dtm.triangulate();
      const triangulateEndTime = Date.now();
      
      assert(result.success, 'Large dataset triangulation should succeed');
      
      console.log(`    ⏱️  Point addition: ${addEndTime - addStartTime}ms`);
      console.log(`    ⏱️  Triangulation: ${triangulateEndTime - triangulateStartTime}ms`);
      console.log(`    📊 Result: ${result.trianglesCount} triangles`);
      
      // Test draping performance
      const drapingStartTime = Date.now();
      const testPoints = [];
      for (let i = 0; i < 100; i++) {
        testPoints.push(TerrainModel.createPoint3D(
          Math.random() * (gridSize * 2),
          Math.random() * (gridSize * 2),
          0
        ));
      }
      
      let successfulDrapes = 0;
      for (const testPoint of testPoints) {
        const draped = dtm.drapePoint(testPoint);
        if (draped && draped.isValid) {
          successfulDrapes++;
        }
      }
      const drapingEndTime = Date.now();
      
      console.log(`    ⏱️  Draping 100 points: ${drapingEndTime - drapingStartTime}ms`);
      console.log(`    📊 Successful drapes: ${successfulDrapes}/100`);
      
      dtm.dispose();
    });
  }
  
  async testMemoryManagement() {
    console.log('🧠 Testing Memory Management...');
    
    await this.runTest('Object Disposal', () => {
      const dtms = [];
      
      // Create multiple DTMs
      for (let i = 0; i < 10; i++) {
        const dtm = TerrainModel.createDTM();
        dtm.addPointFeature(TerrainModel.createPoint3D(i, i, 100 + i), i);
        dtms.push(dtm);
      }
      
      // Dispose all DTMs
      for (const dtm of dtms) {
        assert(!dtm.disposed, 'DTM should not be disposed before disposal');
        dtm.dispose();
        assert(dtm.disposed, 'DTM should be disposed after disposal');
      }
      
      console.log(`    ✅ Successfully created and disposed ${dtms.length} DTMs`);
    });
    
    await this.runTest('Memory Pressure Handling', () => {
      // Test behavior under memory pressure
      const dtm = TerrainModel.createDTM();
      
      // Add a large number of points in batches
      const batchSize = 500;
      const numBatches = 5;
      
      for (let batch = 0; batch < numBatches; batch++) {
        const points = [];
        for (let i = 0; i < batchSize; i++) {
          points.push(TerrainModel.createPoint3D(
            Math.random() * 1000,
            Math.random() * 1000,
            100 + Math.random() * 50
          ));
        }
        
        dtm.addPoints(points);
        console.log(`    📦 Added batch ${batch + 1}/${numBatches} (${dtm.getVerticesCount()} total vertices)`);
      }
      
      // Force triangulation
      const result = dtm.triangulate();
      assert(result.success, 'Triangulation should succeed under memory pressure');
      
      dtm.dispose();
    });
  }
  
  async testErrorRecovery() {
    console.log('🚨 Testing Error Recovery...');
    
    await this.runTest('Invalid Input Handling', () => {
      const dtm = TerrainModel.createDTM();
      
      // Test null/undefined inputs
      assert.throws(() => {
        dtm.addPointFeature(null);
      }, 'Should throw error for null point');
      
      // Test invalid coordinates (if validation is implemented)
      try {
        const invalidPoint = TerrainModel.createPoint3D(NaN, 0, 0);
        // If this doesn't throw, the implementation might handle it gracefully
        console.log('    ℹ️  NaN coordinates handled gracefully');
      } catch (error) {
        console.log('    ✅ NaN coordinates properly rejected');
      }
      
      dtm.dispose();
    });
    
    await this.runTest('Insufficient Data Handling', () => {
      const dtm = TerrainModel.createDTM();
      
      // Try to triangulate with no points
      const result1 = dtm.triangulate();
      assert(!result1.success, 'Triangulation should fail with no points');
      
      // Try with insufficient points
      dtm.addPointFeature(TerrainModel.createPoint3D(0, 0, 100), 1);
      dtm.addPointFeature(TerrainModel.createPoint3D(10, 0, 100), 2);
      
      const result2 = dtm.triangulate();
      assert(!result2.success, 'Triangulation should fail with insufficient points');
      
      dtm.dispose();
    });
  }
  
  // Helper methods
  async runTest(testName, testFunction) {
    try {
      await testFunction();
      console.log(`  ✅ ${testName}`);
      this.testResults.passed++;
    } catch (error) {
      if (error.message.includes('not implemented') || error.message.includes('not available')) {
        console.log(`  ⏭️  ${testName} (skipped - not implemented)`);
        this.testResults.skipped++;
      } else {
        console.log(`  ❌ ${testName}: ${error.message}`);
        this.testResults.failed++;
        this.testResults.errors.push({ test: testName, error: error.message });
      }
    }
  }
  
  printFinalSummary() {
    const endTime = Date.now();
    const totalTime = endTime - this.startTime;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 Integration Test Summary');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⏭️  Skipped: ${this.testResults.skipped}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    
    const total = this.testResults.passed + this.testResults.failed;
    if (total > 0) {
      const successRate = (this.testResults.passed / total * 100).toFixed(1);
      console.log(`📈 Success Rate: ${successRate}%`);
    }
    
    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 Failed Tests:');
      this.testResults.errors.forEach(error => {
        console.log(`  • ${error.test}: ${error.error}`);
      });
    }
    
    console.log('\n🎉 Integration test suite completed!');
    
    // Exit with appropriate code
    process.exit(this.testResults.failed > 0 ? 1 : 0);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new IntegrationTestSuite();
  testSuite.runAllTests().catch(console.error);
}

module.exports = IntegrationTestSuite;
