/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "NativeDTMTinEditor.h"
#include "TerrainModelUtils.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
NativeDTMTinEditor::NativeDTMTinEditor(const Napi::CallbackInfo& info) 
    : TerrainModelObjectWrap<NativeDTMTinEditor>(info)
    , m_tinEditor(nullptr)
    , m_dtm(nullptr)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsObject()) {
        ThrowTerrainModelError(env, "Expected DTM object as first argument");
        return;
    }

    // Get the DTM object
    Napi::Object dtmObj = info[0].As<Napi::Object>();
    if (!NativeDTM::constructor.InstanceOf(dtmObj)) {
        ThrowTerrainModelError(env, "Expected DTM object");
        return;
    }

    m_dtm = NativeDTM::Unwrap(dtmObj);
    if (!m_dtm || !m_dtm->IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM object");
        return;
    }

    // Keep a reference to the DTM object
    m_dtmRef = Napi::Persistent(dtmObj);

    try {
        // Create the TIN editor with the DTM
        m_tinEditor = std::make_unique<DTMTinEditor>(m_dtm->GetHandle());
        
        if (!m_tinEditor) {
            ThrowTerrainModelError(env, "Failed to create TIN editor");
            return;
        }
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Error creating TIN editor: " + std::string(e.what()));
        return;
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
NativeDTMTinEditor::~NativeDTMTinEditor()
{
    if (m_tinEditor) {
        m_tinEditor.reset();
    }
    
    if (!m_dtmRef.IsEmpty()) {
        m_dtmRef.Reset();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
void NativeDTMTinEditor::Init(Napi::Env env, Napi::Object exports)
{
    Napi::HandleScope scope(env);
    
    Napi::Function func = DefineClass(env, "DTMTinEditor", {
        // Edit session management
        InstanceMethod("startEdit", &NativeDTMTinEditor::StartEdit),
        InstanceMethod("endEdit", &NativeDTMTinEditor::EndEdit),
        InstanceMethod("cancelEdit", &NativeDTMTinEditor::CancelEdit),
        InstanceMethod("isEditing", &NativeDTMTinEditor::IsEditing),
        
        // Feature point management
        InstanceMethod("setFeaturePoints", &NativeDTMTinEditor::SetFeaturePoints),
        InstanceMethod("getFeaturePoints", &NativeDTMTinEditor::GetFeaturePoints),
        InstanceMethod("addFeaturePoint", &NativeDTMTinEditor::AddFeaturePoint),
        InstanceMethod("insertFeaturePoint", &NativeDTMTinEditor::InsertFeaturePoint),
        InstanceMethod("removeFeaturePoint", &NativeDTMTinEditor::RemoveFeaturePoint),
        InstanceMethod("clearFeaturePoints", &NativeDTMTinEditor::ClearFeaturePoints),
        
        // Triangle operations
        InstanceMethod("flipEdge", &NativeDTMTinEditor::FlipEdge),
        InstanceMethod("splitTriangle", &NativeDTMTinEditor::SplitTriangle),
        InstanceMethod("mergeTriangles", &NativeDTMTinEditor::MergeTriangles),
        InstanceMethod("deleteTriangle", &NativeDTMTinEditor::DeleteTriangle),
        
        // Vertex operations
        InstanceMethod("moveVertex", &NativeDTMTinEditor::MoveVertex),
        InstanceMethod("deleteVertex", &NativeDTMTinEditor::DeleteVertex),
        InstanceMethod("insertVertex", &NativeDTMTinEditor::InsertVertex),
        
        // Edge operations
        InstanceMethod("swapEdge", &NativeDTMTinEditor::SwapEdge),
        InstanceMethod("constrainEdge", &NativeDTMTinEditor::ConstrainEdge),
        InstanceMethod("unconstrainEdge", &NativeDTMTinEditor::UnconstrainEdge),
        
        // Validation and repair
        InstanceMethod("validateTriangulation", &NativeDTMTinEditor::ValidateTriangulation),
        InstanceMethod("repairTriangulation", &NativeDTMTinEditor::RepairTriangulation),
        InstanceMethod("optimizeTriangulation", &NativeDTMTinEditor::OptimizeTriangulation),
        
        // Undo/Redo operations
        InstanceMethod("canUndo", &NativeDTMTinEditor::CanUndo),
        InstanceMethod("canRedo", &NativeDTMTinEditor::CanRedo),
        InstanceMethod("undo", &NativeDTMTinEditor::Undo),
        InstanceMethod("redo", &NativeDTMTinEditor::Redo),
        InstanceMethod("clearUndoHistory", &NativeDTMTinEditor::ClearUndoHistory),
        
        // Utility
        InstanceMethod("dispose", &NativeDTMTinEditor::Dispose),
    });

    exports.Set("DTMTinEditor", func);
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::StartEdit(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "startEdit");
    
    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        bool success = m_tinEditor->StartEdit();
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Start edit failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::EndEdit(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "endEdit");
    
    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        bool success = m_tinEditor->EndEdit();
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "End edit failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::FlipEdge(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "flipEdge");
    
    if (info.Length() < 1 || !info[0].IsNumber()) {
        Napi::TypeError::New(env, "Expected edge ID as number").ThrowAsJavaScriptException();
        return env.Null();
    }

    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        DTMEdgeId edgeId = static_cast<DTMEdgeId>(info[0].As<Napi::Number>().Int32Value());
        DTMEditResult result = m_tinEditor->FlipEdge(edgeId);
        
        return ConvertEditResultToJS(env, result);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Flip edge failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::SplitTriangle(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "splitTriangle");
    
    if (info.Length() < 2 || !info[0].IsNumber() || !info[1].IsObject()) {
        Napi::TypeError::New(env, "Expected triangle ID and point").ThrowAsJavaScriptException();
        return env.Null();
    }

    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        DTMTriangleId triangleId = static_cast<DTMTriangleId>(info[0].As<Napi::Number>().Int32Value());
        Point3D point = TerrainModelUtils::ConvertJSObjectToPoint3D(info[1].As<Napi::Object>());
        
        DTMEditResult result = m_tinEditor->SplitTriangle(triangleId, point);
        
        return ConvertEditResultToJS(env, result);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Split triangle failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::SetFeaturePoints(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "setFeaturePoints");
    
    if (info.Length() < 1 || !info[0].IsArray()) {
        Napi::TypeError::New(env, "Expected array of points").ThrowAsJavaScriptException();
        return env.Null();
    }

    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        std::vector<Point3D> points = TerrainModelUtils::ConvertJSArrayToPoint3DVector(info[0].As<Napi::Array>());
        m_tinEditor->SetFeaturePoints(points);
        
        return env.Undefined();
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Set feature points failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::GetFeaturePoints(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "getFeaturePoints");
    
    if (!IsValidEditor()) {
        ThrowTerrainModelError(env, "Invalid TIN editor");
        return env.Null();
    }

    try {
        const std::vector<Point3D>& points = m_tinEditor->GetFeaturePoints();
        return TerrainModelUtils::ConvertPoint3DVectorToJSArray(env, points);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Get feature points failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Object NativeDTMTinEditor::ConvertEditResultToJS(Napi::Env env, const DTMEditResult& result)
{
    Napi::Object jsResult = Napi::Object::New(env);
    
    jsResult.Set("success", Napi::Boolean::New(env, result.success));
    jsResult.Set("message", Napi::String::New(env, result.message));
    jsResult.Set("affectedTriangles", Napi::Number::New(env, result.affectedTriangles));
    jsResult.Set("affectedVertices", Napi::Number::New(env, result.affectedVertices));
    jsResult.Set("affectedEdges", Napi::Number::New(env, result.affectedEdges));
    
    // Convert modified triangle IDs
    Napi::Array modifiedTriangles = Napi::Array::New(env, result.modifiedTriangles.size());
    for (size_t i = 0; i < result.modifiedTriangles.size(); ++i) {
        modifiedTriangles.Set(i, Napi::Number::New(env, result.modifiedTriangles[i]));
    }
    jsResult.Set("modifiedTriangles", modifiedTriangles);
    
    // Convert modified vertex IDs
    Napi::Array modifiedVertices = Napi::Array::New(env, result.modifiedVertices.size());
    for (size_t i = 0; i < result.modifiedVertices.size(); ++i) {
        modifiedVertices.Set(i, Napi::Number::New(env, result.modifiedVertices[i]));
    }
    jsResult.Set("modifiedVertices", modifiedVertices);
    
    // Convert modified edge IDs
    Napi::Array modifiedEdges = Napi::Array::New(env, result.modifiedEdges.size());
    for (size_t i = 0; i < result.modifiedEdges.size(); ++i) {
        modifiedEdges.Set(i, Napi::Number::New(env, result.modifiedEdges[i]));
    }
    jsResult.Set("modifiedEdges", modifiedEdges);
    
    return jsResult;
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTMTinEditor::Dispose(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    if (!m_disposed) {
        if (m_tinEditor) {
            m_tinEditor.reset();
        }
        
        if (!m_dtmRef.IsEmpty()) {
            m_dtmRef.Reset();
        }
        
        m_dtm = nullptr;
        m_disposed = true;
    }
    
    return env.Undefined();
}

} // namespace TerrainModelNodeAddon
