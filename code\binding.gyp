{"targets": [{"target_name": "TerrainModelNodeAddon", "sources": ["TerrainModelNodeAddon.cpp", "NativeDTM.cpp", "NativeDTMFeature.cpp", "NativeDTMFeatureEnumerator.cpp", "NativeDTMMesh.cpp", "NativeDTMMeshEnumerator.cpp", "TerrainModelUtils.cpp"], "include_dirs": ["<!@(node -p \"require('node-addon-api').include\")", "../TerrainModelNET", "../iModelJsNodeAddon", "$(BENTLEY_SDK_ROOT)/include", "$(BENTLEY_SDK_ROOT)/include/TerrainModel", "$(BENTLEY_SDK_ROOT)/include/Bentley"], "libraries": ["-l$(BENTLEY_SDK_ROOT)/lib/TerrainModel.lib", "-l$(BENTLEY_SDK_ROOT)/lib/BentleyGeom.lib", "-l$(BENTLEY_SDK_ROOT)/lib/BentleyAllocator.lib"], "defines": ["NAPI_DISABLE_CPP_EXCEPTIONS", "BUILDING_NODE_EXTENSION", "USING_NAMESPACE_BENTLEY_TERRAINMODEL"], "dependencies": ["<!(node -p \"require('node-addon-api').gyp\")"], "cflags!": ["-fno-exceptions"], "cflags_cc!": ["-fno-exceptions"], "xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "CLANG_CXX_LIBRARY": "libc++", "MACOSX_DEPLOYMENT_TARGET": "10.7"}, "msvs_settings": {"VCCLCompilerTool": {"ExceptionHandling": 1, "AdditionalOptions": ["/std:c++17"]}}, "conditions": [["OS=='win'", {"defines": ["WIN32_LEAN_AND_MEAN", "NOMINMAX", "_WIN32_WINNT=0x0601"], "msvs_settings": {"VCCLCompilerTool": {"RuntimeLibrary": 2, "ExceptionHandling": 1}}}], ["OS=='mac'", {"xcode_settings": {"OTHER_CPLUSPLUSFLAGS": ["-std=c++17", "-stdlib=libc++"], "OTHER_LDFLAGS": ["-stdlib=libc++"]}}], ["OS=='linux'", {"cflags_cc": ["-std=c++17"]}]]}]}