/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Native DTM Feature wrapper class
//=======================================================================================
class NativeDTMFeature : public TerrainModelObjectWrap<NativeDTMFeature>
{
private:
    BcDTMFeature* m_nativeFeature;
    DTMFeatureType m_featureType;
    DTMFeatureId m_featureId;
    DTMUserTag m_userTag;

public:
    // Constructor
    NativeDTMFeature(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMFeature();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetFeatureType(const Napi::CallbackInfo& info);
    Napi::Value GetFeatureId(const Napi::CallbackInfo& info);
    Napi::Value GetUserTag(const Napi::CallbackInfo& info);
    Napi::Value GetPoints(const Napi::CallbackInfo& info);
    Napi::Value GetPointCount(const Napi::CallbackInfo& info);

    // Instance methods
    Napi::Value GetPoint(const Napi::CallbackInfo& info);
    Napi::Value SetPoint(const Napi::CallbackInfo& info);
    Napi::Value AddPoint(const Napi::CallbackInfo& info);
    Napi::Value InsertPoint(const Napi::CallbackInfo& info);
    Napi::Value RemovePoint(const Napi::CallbackInfo& info);
    Napi::Value GetLength(const Napi::CallbackInfo& info);
    Napi::Value GetArea(const Napi::CallbackInfo& info);
    Napi::Value IsValid(const Napi::CallbackInfo& info);
    Napi::Value Clone(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    BcDTMFeature* GetHandle() const { return m_nativeFeature; }
    bool IsValidFeature() const { return m_nativeFeature != nullptr && !m_disposed; }

    // Factory method for creating from native feature
    static Napi::Object CreateFromNative(Napi::Env env, BcDTMFeature* nativeFeature);

private:
    // Helper methods
    void InitializeFromNative(BcDTMFeature* nativeFeature);
    void UpdateFeatureInfo();
};

//=======================================================================================
// DTM Feature Info wrapper class for read-only feature information
//=======================================================================================
class NativeDTMFeatureInfo : public TerrainModelObjectWrap<NativeDTMFeatureInfo>
{
private:
    DTMFeatureType m_featureType;
    DTMFeatureId m_featureId;
    DTMUserTag m_userTag;
    std::vector<Point3D> m_points;

public:
    // Constructor
    NativeDTMFeatureInfo(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMFeatureInfo() = default;

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors (read-only)
    Napi::Value GetFeatureType(const Napi::CallbackInfo& info);
    Napi::Value GetFeatureId(const Napi::CallbackInfo& info);
    Napi::Value GetUserTag(const Napi::CallbackInfo& info);
    Napi::Value GetPoints(const Napi::CallbackInfo& info);
    Napi::Value GetPointCount(const Napi::CallbackInfo& info);

    // Instance methods
    Napi::Value GetPoint(const Napi::CallbackInfo& info);
    Napi::Value ToJSON(const Napi::CallbackInfo& info);

    // Factory methods for creating from different sources
    static Napi::Object CreateFromFeature(Napi::Env env, const BcDTMFeature* feature);
    static Napi::Object CreateFromData(Napi::Env env, DTMFeatureType type, DTMFeatureId id, 
                                      DTMUserTag userTag, const std::vector<Point3D>& points);

private:
    // Helper methods
    void InitializeFromData(DTMFeatureType type, DTMFeatureId id, DTMUserTag userTag, 
                           const std::vector<Point3D>& points);
};

//=======================================================================================
// DTM Draped Point wrapper class
//=======================================================================================
class NativeDTMDrapedPoint : public TerrainModelObjectWrap<NativeDTMDrapedPoint>
{
private:
    Point3D m_originalPoint;
    Point3D m_drapedPoint;
    DTMDrapedPointCode m_drapeCode;
    bool m_isValid;

public:
    // Constructor
    NativeDTMDrapedPoint(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMDrapedPoint() = default;

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetOriginalPoint(const Napi::CallbackInfo& info);
    Napi::Value GetDrapedPoint(const Napi::CallbackInfo& info);
    Napi::Value GetDrapeCode(const Napi::CallbackInfo& info);
    Napi::Value GetIsValid(const Napi::CallbackInfo& info);
    Napi::Value GetElevation(const Napi::CallbackInfo& info);

    // Instance methods
    Napi::Value ToJSON(const Napi::CallbackInfo& info);

    // Factory method for creating from drape operation
    static Napi::Object CreateFromDrapeResult(Napi::Env env, const Point3D& originalPoint,
                                             const Point3D& drapedPoint, DTMDrapedPointCode drapeCode);

private:
    // Helper methods
    void InitializeFromDrapeResult(const Point3D& originalPoint, const Point3D& drapedPoint, 
                                  DTMDrapedPointCode drapeCode);
    static std::string GetDrapeCodeName(DTMDrapedPointCode code);
};

} // namespace TerrainModelNodeAddon
