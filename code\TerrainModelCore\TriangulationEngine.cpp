/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "TriangulationEngine.h"
#include "GeometryUtils.h"
#include "SpatialIndex.h"
#include <algorithm>
#include <chrono>
#include <random>
#include <stack>

#ifdef TERRAIN_MODEL_HAVE_OPENMP
#include <omp.h>
#endif

namespace TerrainModel {

//=======================================================================================
// TriangulationEngine implementation
//=======================================================================================
TriangulationEngine::TriangulationEngine(const Configuration& config)
    : m_config(config)
    , m_spatialIndex(SpatialIndex::Create("rtree"))
    , m_nextVertexId(1)
    , m_nextTriangleId(1)
    , m_nextEdgeId(1)
{
}

TriangulationEngine::~TriangulationEngine() = default;

void TriangulationEngine::SetConfiguration(const Configuration& config) {
    m_config = config;
}

//=======================================================================================
// Main triangulation operations
//=======================================================================================
TriangulationResult TriangulationEngine::Triangulate(const std::vector<Point3D>& points) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    m_lastResult = TriangulationResult();
    m_lastResult.status = DTMStatus::Success;
    
    try {
        // Clear existing data
        Clear();
        
        // Validate input
        if (points.size() < 3) {
            m_lastResult.status = DTMStatus::InsufficientData;
            m_lastResult.message = "Need at least 3 points for triangulation";
            return m_lastResult;
        }
        
        // Report progress
        ReportProgress(0.1, "Preprocessing points");
        
        // Remove duplicate points and validate
        std::vector<Point3D> uniquePoints;
        uniquePoints.reserve(points.size());
        
        for (const auto& point : points) {
            if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
                continue; // Skip invalid points
            }
            
            // Check for duplicates
            bool isDuplicate = false;
            for (const auto& existing : uniquePoints) {
                if (GeometryUtils::Distance2D(point, existing) < m_config.pointTolerance) {
                    isDuplicate = true;
                    break;
                }
            }
            
            if (!isDuplicate) {
                uniquePoints.push_back(point);
            }
        }
        
        if (uniquePoints.size() < 3) {
            m_lastResult.status = DTMStatus::InsufficientData;
            m_lastResult.message = "Insufficient unique points after preprocessing";
            return m_lastResult;
        }
        
        ReportProgress(0.2, "Creating vertices");
        
        // Create vertices
        for (const auto& point : uniquePoints) {
            DTMVertexId vertexId = GenerateVertexId();
            auto vertex = std::make_shared<DTMVertex>(vertexId, point);
            m_vertices[vertexId] = vertex;
        }
        
        ReportProgress(0.3, "Starting triangulation");
        
        // Choose triangulation algorithm based on point count and configuration
        TriangulationResult result;
        
        if (uniquePoints.size() < 1000) {
            result = PerformIncrementalTriangulation(uniquePoints);
        } else if (m_config.parallelProcessing && uniquePoints.size() > 5000) {
            result = PerformDivideAndConquerTriangulation(uniquePoints);
        } else {
            result = PerformDelaunayTriangulation(uniquePoints);
        }
        
        if (!result.isSuccess()) {
            m_lastResult = result;
            return m_lastResult;
        }
        
        ReportProgress(0.8, "Enforcing constraints");
        
        // Enforce constraints if any
        if (m_config.useConstraints && !m_constraintSegments.empty()) {
            EnforceConstraints();
        }
        
        ReportProgress(0.9, "Optimizing triangulation");
        
        // Optimize if requested
        if (m_config.optimizeTriangulation) {
            OptimizeTriangulation();
        }
        
        // Build spatial index
        BuildSpatialIndex();
        
        // Set final results
        m_lastResult.verticesCount = m_vertices.size();
        m_lastResult.trianglesCount = m_triangles.size();
        m_lastResult.edgesCount = m_edges.size();
        m_lastResult.message = "Triangulation completed successfully";
        
        ReportProgress(1.0, "Triangulation complete");
        
    } catch (const std::exception& e) {
        m_lastResult.status = DTMStatus::TriangulationFailed;
        m_lastResult.message = "Triangulation failed: " + std::string(e.what());
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    m_lastResult.elapsedTime = duration.count() / 1000.0;
    
    return m_lastResult;
}

TriangulationResult TriangulationEngine::TriangulateWithConstraints(
    const std::vector<Point3D>& points, 
    const std::vector<std::vector<int>>& constraints) {
    
    // Convert constraint indices to vertex IDs
    m_constraintSegments.clear();
    
    for (const auto& constraint : constraints) {
        if (constraint.size() >= 2) {
            std::vector<DTMVertexId> vertexIds;
            for (int index : constraint) {
                if (index >= 0 && index < static_cast<int>(points.size())) {
                    vertexIds.push_back(static_cast<DTMVertexId>(index + 1)); // 1-based IDs
                }
            }
            if (vertexIds.size() >= 2) {
                m_constraintSegments.push_back(vertexIds);
            }
        }
    }
    
    return Triangulate(points);
}

TriangulationResult TriangulationEngine::TriangulateIncremental(const std::vector<Point3D>& newPoints) {
    // Add new points to existing triangulation
    for (const auto& point : newPoints) {
        InsertVertex(point);
    }
    
    // Update results
    m_lastResult.verticesCount = m_vertices.size();
    m_lastResult.trianglesCount = m_triangles.size();
    m_lastResult.edgesCount = m_edges.size();
    
    return m_lastResult;
}

//=======================================================================================
// Core triangulation algorithms
//=======================================================================================
TriangulationResult TriangulationEngine::PerformDelaunayTriangulation(const std::vector<Point3D>& points) {
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    try {
        // Create super triangle to contain all points
        Range3D bounds = GeometryUtils::BoundingBox(points);
        double margin = std::max(bounds.high.x - bounds.low.x, bounds.high.y - bounds.low.y) * 0.1;
        
        Point3D p1(bounds.low.x - margin, bounds.low.y - margin, bounds.low.z);
        Point3D p2(bounds.high.x + margin, bounds.low.y - margin, bounds.low.z);
        Point3D p3((bounds.low.x + bounds.high.x) * 0.5, bounds.high.y + margin, bounds.low.z);
        
        // Create super triangle vertices
        DTMVertexId superV1 = GenerateVertexId();
        DTMVertexId superV2 = GenerateVertexId();
        DTMVertexId superV3 = GenerateVertexId();
        
        m_vertices[superV1] = std::make_shared<DTMVertex>(superV1, p1);
        m_vertices[superV2] = std::make_shared<DTMVertex>(superV2, p2);
        m_vertices[superV3] = std::make_shared<DTMVertex>(superV3, p3);
        
        // Create super triangle
        DTMTriangleId superTriangle = CreateTriangle(superV1, superV2, superV3);
        
        // Insert points one by one
        for (const auto& point : points) {
            InsertVertex(point);
        }
        
        // Remove super triangle and its vertices
        std::vector<DTMTriangleId> toRemove;
        for (const auto& pair : m_triangles) {
            const auto& triangle = pair.second;
            if (triangle->IsIncidentTo(superV1) || 
                triangle->IsIncidentTo(superV2) || 
                triangle->IsIncidentTo(superV3)) {
                toRemove.push_back(pair.first);
            }
        }
        
        for (DTMTriangleId triangleId : toRemove) {
            DeleteTriangle(triangleId);
        }
        
        m_vertices.erase(superV1);
        m_vertices.erase(superV2);
        m_vertices.erase(superV3);
        
        result.message = "Delaunay triangulation completed";
        
    } catch (const std::exception& e) {
        result.status = DTMStatus::TriangulationFailed;
        result.message = "Delaunay triangulation failed: " + std::string(e.what());
    }
    
    return result;
}

TriangulationResult TriangulationEngine::PerformIncrementalTriangulation(const std::vector<Point3D>& points) {
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    try {
        // Sort points for better performance (optional)
        std::vector<Point3D> sortedPoints = points;
        std::random_device rd;
        std::mt19937 g(rd());
        std::shuffle(sortedPoints.begin(), sortedPoints.end(), g);
        
        // Use Delaunay algorithm for incremental insertion
        result = PerformDelaunayTriangulation(sortedPoints);
        
        if (result.isSuccess()) {
            result.message = "Incremental triangulation completed";
        }
        
    } catch (const std::exception& e) {
        result.status = DTMStatus::TriangulationFailed;
        result.message = "Incremental triangulation failed: " + std::string(e.what());
    }
    
    return result;
}

TriangulationResult TriangulationEngine::PerformDivideAndConquerTriangulation(const std::vector<Point3D>& points) {
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    try {
        // For now, fall back to incremental triangulation
        // A full divide-and-conquer implementation would be more complex
        result = PerformIncrementalTriangulation(points);
        
        if (result.isSuccess()) {
            result.message = "Divide-and-conquer triangulation completed";
        }
        
    } catch (const std::exception& e) {
        result.status = DTMStatus::TriangulationFailed;
        result.message = "Divide-and-conquer triangulation failed: " + std::string(e.what());
    }
    
    return result;
}

//=======================================================================================
// Incremental insertion
//=======================================================================================
DTMVertexId TriangulationEngine::InsertVertex(const Point3D& point) {
    // Create vertex
    DTMVertexId vertexId = GenerateVertexId();
    auto vertex = std::make_shared<DTMVertex>(vertexId, point);
    m_vertices[vertexId] = vertex;
    
    // Find triangle containing the point
    DTMTriangleId containingTriangle = FindTriangleContaining(point);
    
    if (containingTriangle != Constants::INVALID_ID) {
        InsertVertexInTriangle(vertexId, containingTriangle);
    } else {
        // Point is outside convex hull - handle boundary insertion
        // For now, just add the vertex without triangulation
        // A full implementation would handle boundary cases
    }
    
    return vertexId;
}

void TriangulationEngine::InsertVertexInTriangle(DTMVertexId vertexId, DTMTriangleId triangleId) {
    auto triangleIt = m_triangles.find(triangleId);
    if (triangleIt == m_triangles.end()) {
        return;
    }
    
    const auto& triangle = triangleIt->second;
    DTMVertexId v1 = triangle->GetVertex1Id();
    DTMVertexId v2 = triangle->GetVertex2Id();
    DTMVertexId v3 = triangle->GetVertex3Id();
    
    // Remove the original triangle
    DeleteTriangle(triangleId);
    
    // Create three new triangles
    DTMTriangleId t1 = CreateTriangle(vertexId, v1, v2);
    DTMTriangleId t2 = CreateTriangle(vertexId, v2, v3);
    DTMTriangleId t3 = CreateTriangle(vertexId, v3, v1);
    
    // Legalize edges
    // This is a simplified version - a full implementation would handle all edge cases
    std::vector<DTMEdgeId> edgesToCheck;
    
    // Find edges opposite to the new vertex in each new triangle
    auto t1It = m_triangles.find(t1);
    auto t2It = m_triangles.find(t2);
    auto t3It = m_triangles.find(t3);
    
    if (t1It != m_triangles.end()) {
        for (int i = 0; i < 3; ++i) {
            DTMEdgeId edgeId = t1It->second->GetEdgeId(i);
            if (edgeId != Constants::INVALID_ID) {
                edgesToCheck.push_back(edgeId);
            }
        }
    }
    
    // Legalize edges
    for (DTMEdgeId edgeId : edgesToCheck) {
        LegalizeEdge(vertexId, edgeId);
    }
}

void TriangulationEngine::InsertVertexOnEdge(DTMVertexId vertexId, DTMEdgeId edgeId) {
    // TODO: Implement vertex insertion on edge
    // This is more complex as it involves splitting the edge and updating adjacent triangles
}

void TriangulationEngine::LegalizeEdge(DTMVertexId vertexId, DTMEdgeId edgeId) {
    auto edgeIt = m_edges.find(edgeId);
    if (edgeIt == m_edges.end() || edgeIt->second->IsBoundary()) {
        return; // Edge doesn't exist or is on boundary
    }
    
    const auto& edge = edgeIt->second;
    DTMTriangleId leftTriangle = edge->GetLeftTriangleId();
    DTMTriangleId rightTriangle = edge->GetRightTriangleId();
    
    if (leftTriangle == Constants::INVALID_ID || rightTriangle == Constants::INVALID_ID) {
        return; // Boundary edge
    }
    
    // Check if edge needs to be flipped (Delaunay criterion)
    if (!IsFlipLegal(edgeId)) {
        return;
    }
    
    // Get the fourth vertex (opposite to the edge in the adjacent triangle)
    auto leftTriangleIt = m_triangles.find(leftTriangle);
    auto rightTriangleIt = m_triangles.find(rightTriangle);
    
    if (leftTriangleIt == m_triangles.end() || rightTriangleIt == m_triangles.end()) {
        return;
    }
    
    DTMVertexId oppositeVertex = leftTriangleIt->second->GetOppositeVertex(edgeId);
    if (oppositeVertex == Constants::INVALID_ID) {
        oppositeVertex = rightTriangleIt->second->GetOppositeVertex(edgeId);
    }
    
    if (oppositeVertex == Constants::INVALID_ID || oppositeVertex == vertexId) {
        return;
    }
    
    // Check incircle test
    auto v1It = m_vertices.find(edge->GetVertex1Id());
    auto v2It = m_vertices.find(edge->GetVertex2Id());
    auto vIt = m_vertices.find(vertexId);
    auto oppIt = m_vertices.find(oppositeVertex);
    
    if (v1It == m_vertices.end() || v2It == m_vertices.end() || 
        vIt == m_vertices.end() || oppIt == m_vertices.end()) {
        return;
    }
    
    double incircle = GeometryUtils::InCircle(
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        vIt->second->GetPosition(),
        oppIt->second->GetPosition()
    );
    
    if (incircle > 0.0) {
        // Edge is illegal, flip it
        FlipEdge(edgeId);
        
        // Recursively check the new edges
        // TODO: Find the new edges created by the flip and check them
    }
}

//=======================================================================================
// Triangle operations
//=======================================================================================
DTMTriangleId TriangulationEngine::CreateTriangle(DTMVertexId v1, DTMVertexId v2, DTMVertexId v3) {
    DTMTriangleId triangleId = GenerateTriangleId();
    auto triangle = std::make_shared<DTMTriangle>(triangleId, v1, v2, v3);
    m_triangles[triangleId] = triangle;
    
    // Create edges if they don't exist
    DTMEdgeId edge1 = CreateEdge(v1, v2);
    DTMEdgeId edge2 = CreateEdge(v2, v3);
    DTMEdgeId edge3 = CreateEdge(v3, v1);
    
    triangle->SetEdgeId(0, edge1);
    triangle->SetEdgeId(1, edge2);
    triangle->SetEdgeId(2, edge3);
    
    // Update edge-triangle relationships
    auto e1It = m_edges.find(edge1);
    auto e2It = m_edges.find(edge2);
    auto e3It = m_edges.find(edge3);
    
    if (e1It != m_edges.end()) {
        if (e1It->second->GetLeftTriangleId() == Constants::INVALID_ID) {
            e1It->second->SetTriangles(triangleId, e1It->second->GetRightTriangleId());
        } else {
            e1It->second->SetTriangles(e1It->second->GetLeftTriangleId(), triangleId);
        }
    }
    
    if (e2It != m_edges.end()) {
        if (e2It->second->GetLeftTriangleId() == Constants::INVALID_ID) {
            e2It->second->SetTriangles(triangleId, e2It->second->GetRightTriangleId());
        } else {
            e2It->second->SetTriangles(e2It->second->GetLeftTriangleId(), triangleId);
        }
    }
    
    if (e3It != m_edges.end()) {
        if (e3It->second->GetLeftTriangleId() == Constants::INVALID_ID) {
            e3It->second->SetTriangles(triangleId, e3It->second->GetRightTriangleId());
        } else {
            e3It->second->SetTriangles(e3It->second->GetLeftTriangleId(), triangleId);
        }
    }
    
    return triangleId;
}

void TriangulationEngine::DeleteTriangle(DTMTriangleId triangleId) {
    auto it = m_triangles.find(triangleId);
    if (it != m_triangles.end()) {
        const auto& triangle = it->second;
        
        // Update edges to remove triangle reference
        for (int i = 0; i < 3; ++i) {
            DTMEdgeId edgeId = triangle->GetEdgeId(i);
            auto edgeIt = m_edges.find(edgeId);
            if (edgeIt != m_edges.end()) {
                edgeIt->second->ReplaceTriangle(triangleId, Constants::INVALID_ID);
            }
        }
        
        m_triangles.erase(it);
    }
}

DTMTriangleId TriangulationEngine::FindTriangleContaining(const Point3D& point) const {
    // Simple linear search - a more efficient implementation would use spatial indexing
    for (const auto& pair : m_triangles) {
        if (pair.second->ContainsPoint(point, m_vertices)) {
            return pair.first;
        }
    }
    return Constants::INVALID_ID;
}

//=======================================================================================
// Edge operations
//=======================================================================================
DTMEdgeId TriangulationEngine::CreateEdge(DTMVertexId vertex1Id, DTMVertexId vertex2Id) {
    // Check if edge already exists
    for (const auto& pair : m_edges) {
        const auto& edge = pair.second;
        if ((edge->GetVertex1Id() == vertex1Id && edge->GetVertex2Id() == vertex2Id) ||
            (edge->GetVertex1Id() == vertex2Id && edge->GetVertex2Id() == vertex1Id)) {
            return pair.first;
        }
    }
    
    // Create new edge
    DTMEdgeId edgeId = GenerateEdgeId();
    auto edge = std::make_shared<DTMEdge>(edgeId, vertex1Id, vertex2Id);
    m_edges[edgeId] = edge;
    
    return edgeId;
}

void TriangulationEngine::DeleteEdge(DTMEdgeId edgeId) {
    m_edges.erase(edgeId);
}

bool TriangulationEngine::FlipEdge(DTMEdgeId edgeId) {
    // TODO: Implement edge flipping
    // This is a complex operation that involves:
    // 1. Removing two adjacent triangles
    // 2. Creating two new triangles with the flipped edge
    // 3. Updating all adjacency relationships
    return false;
}

bool TriangulationEngine::IsFlipLegal(DTMEdgeId edgeId) const {
    auto edgeIt = m_edges.find(edgeId);
    if (edgeIt == m_edges.end() || edgeIt->second->IsBoundary()) {
        return false;
    }
    
    // TODO: Implement proper legality check
    // For now, assume all edges are legal
    return true;
}

bool TriangulationEngine::IsEdgeConstrained(DTMEdgeId edgeId) const {
    return m_constrainedEdges.find(edgeId) != m_constrainedEdges.end();
}

//=======================================================================================
// Utility operations
//=======================================================================================
void TriangulationEngine::Clear() {
    m_vertices.clear();
    m_triangles.clear();
    m_edges.clear();
    m_constraintSegments.clear();
    m_constrainedEdges.clear();
    
    m_nextVertexId = 1;
    m_nextTriangleId = 1;
    m_nextEdgeId = 1;
    
    if (m_spatialIndex) {
        m_spatialIndex->Clear();
    }
}

void TriangulationEngine::BuildSpatialIndex() {
    if (!m_spatialIndex) {
        return;
    }
    
    m_spatialIndex->Clear();
    
    // Add vertices
    for (const auto& pair : m_vertices) {
        m_spatialIndex->InsertVertex(pair.first, pair.second->GetPosition());
    }
    
    // Add triangles
    for (const auto& pair : m_triangles) {
        Range3D bounds = CalculateTriangleBounds(pair.second);
        m_spatialIndex->InsertTriangle(pair.first, bounds);
    }
}

Range3D TriangulationEngine::CalculateTriangleBounds(DTMTrianglePtr triangle) const {
    auto v1It = m_vertices.find(triangle->GetVertex1Id());
    auto v2It = m_vertices.find(triangle->GetVertex2Id());
    auto v3It = m_vertices.find(triangle->GetVertex3Id());
    
    if (v1It == m_vertices.end() || v2It == m_vertices.end() || v3It == m_vertices.end()) {
        return Range3D();
    }
    
    std::vector<Point3D> points = {
        v1It->second->GetPosition(),
        v2It->second->GetPosition(),
        v3It->second->GetPosition()
    };
    
    return GeometryUtils::BoundingBox(points);
}

bool TriangulationEngine::ReportProgress(double progress, const std::string& message) const {
    if (m_progressCallback) {
        return m_progressCallback(progress, message);
    }
    return true; // Continue processing
}

void TriangulationEngine::UpdateProgress(const std::string& phase, size_t current, size_t total) const {
    if (total > 0) {
        double progress = static_cast<double>(current) / static_cast<double>(total);
        std::string message = phase + " (" + std::to_string(current) + "/" + std::to_string(total) + ")";
        ReportProgress(progress, message);
    }
}

//=======================================================================================
// Constraint enforcement (simplified implementation)
//=======================================================================================
void TriangulationEngine::EnforceConstraints() {
    for (const auto& constraint : m_constraintSegments) {
        if (constraint.size() >= 2) {
            EnforceConstraint(constraint);
        }
    }
}

void TriangulationEngine::EnforceConstraint(const std::vector<DTMVertexId>& constraint) {
    // Simple implementation: mark edges as constrained
    for (size_t i = 0; i < constraint.size() - 1; ++i) {
        DTMEdgeId edgeId = CreateEdge(constraint[i], constraint[i + 1]);
        m_constrainedEdges.insert(edgeId);
        
        auto edgeIt = m_edges.find(edgeId);
        if (edgeIt != m_edges.end()) {
            edgeIt->second->SetConstrained(true);
        }
    }
}

//=======================================================================================
// Optimization (simplified implementation)
//=======================================================================================
TriangulationResult TriangulationEngine::OptimizeTriangulation() {
    TriangulationResult result;
    result.status = DTMStatus::Success;
    result.message = "Optimization completed";
    
    // Simple optimization: remove sliver triangles
    RemoveSliverTriangles();
    
    return result;
}

TriangulationResult TriangulationEngine::RemoveSliverTriangles() {
    TriangulationResult result;
    result.status = DTMStatus::Success;
    
    std::vector<DTMTriangleId> sliversToRemove;
    
    for (const auto& pair : m_triangles) {
        if (IsTriangleSliver(pair.first)) {
            sliversToRemove.push_back(pair.first);
        }
    }
    
    for (DTMTriangleId triangleId : sliversToRemove) {
        DeleteTriangle(triangleId);
    }
    
    result.message = "Removed " + std::to_string(sliversToRemove.size()) + " sliver triangles";
    return result;
}

bool TriangulationEngine::IsTriangleSliver(DTMTriangleId triangleId) const {
    auto triangleIt = m_triangles.find(triangleId);
    if (triangleIt == m_triangles.end()) {
        return false;
    }
    
    return triangleIt->second->GetQuality(m_vertices) < 0.1; // Quality threshold
}

} // namespace TerrainModel
