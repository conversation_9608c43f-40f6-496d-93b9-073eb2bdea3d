/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include <fstream>
#include <memory>

namespace TerrainModel {

// Forward declaration
class DTM;

//=======================================================================================
// File format enumeration
//=======================================================================================
enum class FileFormat {
    Unknown,
    TIN,           // Standard TIN format
    GeopakTIN,     // GEOPAK TIN format
    GeopakDAT,     // GEOPAK DAT format
    LandXML,       // LandXML format
    DXF,           // AutoCAD DXF format
    OBJ,           // Wavefront OBJ format
    PLY,           // Stanford PLY format
    STL,           // STL format
    Binary,        // Custom binary format
    JSON,          // JSON format
    XML            // XML format
};

//=======================================================================================
// File I/O result structure
//=======================================================================================
struct FileIOResult {
    bool success = false;
    std::string message;
    FileFormat detectedFormat = FileFormat::Unknown;
    size_t verticesRead = 0;
    size_t trianglesRead = 0;
    size_t featuresRead = 0;
    double elapsedTime = 0.0;
    std::vector<std::string> warnings;
    
    static FileIOResult Success(const std::string& msg = "Operation completed successfully") {
        FileIOResult result;
        result.success = true;
        result.message = msg;
        return result;
    }
    
    static FileIOResult Failure(const std::string& msg = "Operation failed") {
        FileIOResult result;
        result.success = false;
        result.message = msg;
        return result;
    }
};

//=======================================================================================
// File I/O options
//=======================================================================================
struct FileIOOptions {
    bool preserveFeatures = true;
    bool preserveTriangulation = true;
    bool validateData = true;
    bool compressOutput = false;
    bool includeStatistics = false;
    double precision = 1e-6;
    std::string encoding = "UTF-8";
    
    // Format-specific options
    std::map<std::string, std::string> formatOptions;
};

//=======================================================================================
// Abstract base class for file format handlers
//=======================================================================================
class FileFormatHandler {
public:
    virtual ~FileFormatHandler() = default;
    
    virtual FileFormat GetFormat() const = 0;
    virtual std::string GetFormatName() const = 0;
    virtual std::vector<std::string> GetFileExtensions() const = 0;
    virtual bool CanRead() const = 0;
    virtual bool CanWrite() const = 0;
    
    virtual FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) = 0;
    virtual FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) = 0;
    
    virtual bool ValidateFile(const std::string& fileName) const = 0;
    virtual FileFormat DetectFormat(const std::string& fileName) const = 0;
};

//=======================================================================================
// TIN format handler
//=======================================================================================
class TINFormatHandler : public FileFormatHandler {
public:
    FileFormat GetFormat() const override { return FileFormat::TIN; }
    std::string GetFormatName() const override { return "TIN"; }
    std::vector<std::string> GetFileExtensions() const override { return {".tin", ".TIN"}; }
    bool CanRead() const override { return true; }
    bool CanWrite() const override { return true; }
    
    FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    
    bool ValidateFile(const std::string& fileName) const override;
    FileFormat DetectFormat(const std::string& fileName) const override;

private:
    struct TINHeader {
        uint32_t version;
        uint32_t vertexCount;
        uint32_t triangleCount;
        uint32_t featureCount;
        double bounds[6]; // minX, minY, minZ, maxX, maxY, maxZ
    };
    
    bool ReadHeader(std::ifstream& file, TINHeader& header) const;
    bool WriteHeader(std::ofstream& file, const TINHeader& header) const;
    bool ReadVertices(std::ifstream& file, DTM& dtm, uint32_t count) const;
    bool WriteVertices(std::ofstream& file, const DTM& dtm) const;
    bool ReadTriangles(std::ifstream& file, DTM& dtm, uint32_t count) const;
    bool WriteTriangles(std::ofstream& file, const DTM& dtm) const;
    bool ReadFeatures(std::ifstream& file, DTM& dtm, uint32_t count) const;
    bool WriteFeatures(std::ofstream& file, const DTM& dtm) const;
};

//=======================================================================================
// GEOPAK TIN format handler
//=======================================================================================
class GeopakTINFormatHandler : public FileFormatHandler {
public:
    FileFormat GetFormat() const override { return FileFormat::GeopakTIN; }
    std::string GetFormatName() const override { return "GEOPAK TIN"; }
    std::vector<std::string> GetFileExtensions() const override { return {".tin", ".TIN"}; }
    bool CanRead() const override { return true; }
    bool CanWrite() const override { return true; }
    
    FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    
    bool ValidateFile(const std::string& fileName) const override;
    FileFormat DetectFormat(const std::string& fileName) const override;

private:
    bool IsGeopakFormat(const std::string& fileName) const;
    bool ReadGeopakHeader(std::ifstream& file) const;
    bool WriteGeopakHeader(std::ofstream& file, const DTM& dtm) const;
};

//=======================================================================================
// LandXML format handler
//=======================================================================================
class LandXMLFormatHandler : public FileFormatHandler {
public:
    FileFormat GetFormat() const override { return FileFormat::LandXML; }
    std::string GetFormatName() const override { return "LandXML"; }
    std::vector<std::string> GetFileExtensions() const override { return {".xml", ".landxml"}; }
    bool CanRead() const override { return true; }
    bool CanWrite() const override { return true; }
    
    FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    
    bool ValidateFile(const std::string& fileName) const override;
    FileFormat DetectFormat(const std::string& fileName) const override;

private:
    class XMLParser;
    class XMLWriter;
    
    std::unique_ptr<XMLParser> CreateParser() const;
    std::unique_ptr<XMLWriter> CreateWriter() const;
};

//=======================================================================================
// JSON format handler
//=======================================================================================
class JSONFormatHandler : public FileFormatHandler {
public:
    FileFormat GetFormat() const override { return FileFormat::JSON; }
    std::string GetFormatName() const override { return "JSON"; }
    std::vector<std::string> GetFileExtensions() const override { return {".json", ".geojson"}; }
    bool CanRead() const override { return true; }
    bool CanWrite() const override { return true; }
    
    FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    
    bool ValidateFile(const std::string& fileName) const override;
    FileFormat DetectFormat(const std::string& fileName) const override;

private:
    class JSONParser;
    class JSONWriter;
    
    std::string SerializeDTM(const DTM& dtm, const FileIOOptions& options) const;
    bool DeserializeDTM(const std::string& jsonData, DTM& dtm, const FileIOOptions& options) const;
};

//=======================================================================================
// Binary format handler (custom high-performance format)
//=======================================================================================
class BinaryFormatHandler : public FileFormatHandler {
public:
    FileFormat GetFormat() const override { return FileFormat::Binary; }
    std::string GetFormatName() const override { return "Binary"; }
    std::vector<std::string> GetFileExtensions() const override { return {".dtm", ".bin"}; }
    bool CanRead() const override { return true; }
    bool CanWrite() const override { return true; }
    
    FileIOResult Read(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    FileIOResult Write(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions()) override;
    
    bool ValidateFile(const std::string& fileName) const override;
    FileFormat DetectFormat(const std::string& fileName) const override;

private:
    static constexpr uint32_t BINARY_FORMAT_MAGIC = 0x4D544442; // "DTMB"
    static constexpr uint32_t BINARY_FORMAT_VERSION = 1;
    
    struct BinaryHeader {
        uint32_t magic;
        uint32_t version;
        uint32_t flags;
        uint32_t vertexCount;
        uint32_t triangleCount;
        uint32_t edgeCount;
        uint32_t featureCount;
        double bounds[6];
        uint64_t vertexOffset;
        uint64_t triangleOffset;
        uint64_t edgeOffset;
        uint64_t featureOffset;
        uint32_t checksum;
    };
    
    bool ReadBinaryHeader(std::ifstream& file, BinaryHeader& header) const;
    bool WriteBinaryHeader(std::ofstream& file, const BinaryHeader& header) const;
    uint32_t CalculateChecksum(const DTM& dtm) const;
    bool ValidateChecksum(const DTM& dtm, uint32_t expectedChecksum) const;
};

//=======================================================================================
// Main file I/O manager
//=======================================================================================
class FileIOManager {
private:
    std::map<FileFormat, std::unique_ptr<FileFormatHandler>> m_handlers;
    std::map<std::string, FileFormat> m_extensionMap;

public:
    FileIOManager();
    ~FileIOManager() = default;
    
    // Handler management
    void RegisterHandler(std::unique_ptr<FileFormatHandler> handler);
    void UnregisterHandler(FileFormat format);
    FileFormatHandler* GetHandler(FileFormat format) const;
    
    // File operations
    FileIOResult LoadDTM(const std::string& fileName, DTM& dtm, const FileIOOptions& options = FileIOOptions());
    FileIOResult SaveDTM(const std::string& fileName, const DTM& dtm, const FileIOOptions& options = FileIOOptions());
    FileIOResult SaveDTM(const std::string& fileName, const DTM& dtm, FileFormat format, const FileIOOptions& options = FileIOOptions());
    
    // Format detection and validation
    FileFormat DetectFileFormat(const std::string& fileName) const;
    FileFormat GetFormatFromExtension(const std::string& fileName) const;
    bool ValidateFile(const std::string& fileName) const;
    bool ValidateFile(const std::string& fileName, FileFormat expectedFormat) const;
    
    // Utility functions
    std::vector<FileFormat> GetSupportedFormats() const;
    std::vector<std::string> GetSupportedExtensions() const;
    std::string GetFormatName(FileFormat format) const;
    bool CanRead(FileFormat format) const;
    bool CanWrite(FileFormat format) const;
    
    // Batch operations
    FileIOResult ConvertFile(const std::string& inputFile, const std::string& outputFile, 
                           FileFormat outputFormat, const FileIOOptions& options = FileIOOptions());
    std::vector<FileIOResult> ConvertFiles(const std::vector<std::string>& inputFiles, 
                                          const std::string& outputDirectory, 
                                          FileFormat outputFormat, 
                                          const FileIOOptions& options = FileIOOptions());
    
    // Statistics and information
    void GetFileStatistics(const std::string& fileName, std::map<std::string, double>& stats) const;
    
    // Singleton access
    static FileIOManager& Instance();

private:
    void InitializeDefaultHandlers();
    void BuildExtensionMap();
    std::string GetFileExtension(const std::string& fileName) const;
    std::string NormalizeExtension(const std::string& extension) const;
};

//=======================================================================================
// Utility functions
//=======================================================================================
namespace FileIOUtils {
    bool FileExists(const std::string& fileName);
    bool IsReadable(const std::string& fileName);
    bool IsWritable(const std::string& fileName);
    size_t GetFileSize(const std::string& fileName);
    std::string GetFileDirectory(const std::string& fileName);
    std::string GetFileName(const std::string& filePath);
    std::string GetFileBaseName(const std::string& fileName);
    std::string GetFileExtension(const std::string& fileName);
    std::string ChangeFileExtension(const std::string& fileName, const std::string& newExtension);
    
    bool CreateDirectory(const std::string& directory);
    bool CreateDirectoryRecursive(const std::string& directory);
    
    std::string ReadTextFile(const std::string& fileName);
    bool WriteTextFile(const std::string& fileName, const std::string& content);
    
    std::vector<uint8_t> ReadBinaryFile(const std::string& fileName);
    bool WriteBinaryFile(const std::string& fileName, const std::vector<uint8_t>& data);
    
    std::string GenerateBackupFileName(const std::string& fileName);
    bool BackupFile(const std::string& fileName);
}

} // namespace TerrainModel
