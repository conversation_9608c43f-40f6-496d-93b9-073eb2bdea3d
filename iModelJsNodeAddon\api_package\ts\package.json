{"name": "imodeljs-native-typescript-api", "version": "0.0.1", "description": "TypeScript API of imodeljs-native", "private": true, "engines": {"node": ">=10.11.0 <13.0"}, "scripts": {"build": "tsc 1>&2", "clean": "<PERSON><PERSON><PERSON> lib", "copy:test-assets": "cpx \"./assets/test/**/*\" ./lib/test/assets", "lint": "tslint --project . 1>&2", "pretest": "npm run copy:test-assets", "test": "node ./lib/test/index.js"}, "//dependencies:": ["NOTE: Tests may depend only on iModel.js 'wire format', which is declared in @bentley/bentleyjs-core and @bentley/imodeljs-common", "NOTE: Tests may NOT depend on other iModel.js packages"], "dependencies": {"@bentley/bentleyjs-core": "1.14.0", "@bentley/imodeljs-common": "1.14.0"}, "devDependencies": {"@bentley/build-tools": "1.14.0", "@types/node": "10.14.1", "@types/chai": "^4.1.4", "@types/mocha": "^5.2.5", "cpx": "^1.5.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "tslint-etc": "^1.5.2", "typescript": "~3.7.4", "mocha": "^5.2.0", "chai": "^4.1.2"}}