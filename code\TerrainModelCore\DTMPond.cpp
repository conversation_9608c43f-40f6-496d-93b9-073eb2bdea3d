/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "DTMPond.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <queue>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// PondDesignResult implementation
//=======================================================================================
PondDesignResult PondDesignResult::Success(const std::string& message) {
    PondDesignResult result;
    result.success = true;
    result.calculated = true;
    result.message = message;
    return result;
}

PondDesignResult PondDesignResult::Failure(const std::string& message) {
    PondDesignResult result;
    result.success = false;
    result.calculated = false;
    result.message = message;
    return result;
}

//=======================================================================================
// DTMPond implementation
//=======================================================================================
DTMPond::DTMPond(DTMPtr dtm)
    : m_dtm(dtm)
    , m_targetVolume(0.0)
    , m_targetElevation(0.0)
    , m_maxDepth(10.0)
    , m_minDepth(0.5)
    , m_sideSlope(3.0)
    , m_tolerance(0.01)
    , m_maxIterations(100)
{
    if (!m_dtm) {
        throw std::invalid_argument("DTM cannot be null");
    }
    
    if (!m_dtm->IsTriangulated()) {
        throw std::runtime_error("DTM must be triangulated for pond design");
    }
}

DTMPond::~DTMPond() = default;

//=======================================================================================
// Pond design by volume
//=======================================================================================
PondDesignResult DTMPond::CalculatePondByVolume(double targetVolume) {
    if (targetVolume <= 0.0) {
        return PondDesignResult::Failure("Target volume must be positive");
    }
    
    m_targetVolume = targetVolume;
    
    try {
        // Find the lowest point in the DTM as starting point
        Point3D lowestPoint = FindLowestPoint();
        if (lowestPoint.z == std::numeric_limits<double>::max()) {
            return PondDesignResult::Failure("Could not find valid starting point");
        }
        
        // Use iterative approach to find the water level that gives target volume
        double waterLevel = FindWaterLevelForVolume(lowestPoint, targetVolume);
        
        if (waterLevel == std::numeric_limits<double>::max()) {
            return PondDesignResult::Failure("Could not find water level for target volume");
        }
        
        // Calculate pond properties at this water level
        PondDesignResult result = CalculatePondAtElevation(waterLevel);
        
        if (result.success) {
            result.targetVolume = targetVolume;
            result.message = "Pond designed successfully for target volume";
        }
        
        return result;
        
    } catch (const std::exception& e) {
        return PondDesignResult::Failure("Pond design failed: " + std::string(e.what()));
    }
}

//=======================================================================================
// Pond design by elevation
//=======================================================================================
PondDesignResult DTMPond::CalculatePondByElevation(double waterElevation) {
    m_targetElevation = waterElevation;
    
    try {
        return CalculatePondAtElevation(waterElevation);
        
    } catch (const std::exception& e) {
        return PondDesignResult::Failure("Pond design failed: " + std::string(e.what()));
    }
}

//=======================================================================================
// Pond design by boundary
//=======================================================================================
PondDesignResult DTMPond::CalculatePondByBoundary(const std::vector<Point3D>& boundaryPoints) {
    if (boundaryPoints.size() < 3) {
        return PondDesignResult::Failure("Boundary must have at least 3 points");
    }
    
    try {
        // Find the average elevation of boundary points
        double totalElevation = 0.0;
        for (const auto& point : boundaryPoints) {
            totalElevation += point.z;
        }
        double avgElevation = totalElevation / boundaryPoints.size();
        
        // Calculate pond properties within the boundary
        PondDesignResult result = CalculatePondWithBoundary(boundaryPoints, avgElevation);
        
        if (result.success) {
            result.message = "Pond designed successfully within boundary";
        }
        
        return result;
        
    } catch (const std::exception& e) {
        return PondDesignResult::Failure("Pond design failed: " + std::string(e.what()));
    }
}

//=======================================================================================
// Pond optimization
//=======================================================================================
PondDesignResult DTMPond::OptimizePondDesign(const PondOptimizationCriteria& criteria) {
    try {
        PondDesignResult bestResult;
        double bestScore = -std::numeric_limits<double>::max();
        
        // Try different water levels within reasonable range
        Point3D lowestPoint = FindLowestPoint();
        double minElevation = lowestPoint.z + m_minDepth;
        double maxElevation = lowestPoint.z + m_maxDepth;
        
        int numSteps = 20;
        double stepSize = (maxElevation - minElevation) / numSteps;
        
        for (int i = 0; i <= numSteps; ++i) {
            double testElevation = minElevation + i * stepSize;
            
            PondDesignResult result = CalculatePondAtElevation(testElevation);
            
            if (result.success) {
                double score = EvaluatePondDesign(result, criteria);
                
                if (score > bestScore) {
                    bestScore = score;
                    bestResult = result;
                }
            }
        }
        
        if (bestResult.success) {
            bestResult.message = "Optimized pond design found";
        } else {
            bestResult = PondDesignResult::Failure("No valid pond design found during optimization");
        }
        
        return bestResult;
        
    } catch (const std::exception& e) {
        return PondDesignResult::Failure("Pond optimization failed: " + std::string(e.what()));
    }
}

//=======================================================================================
// Internal calculation methods
//=======================================================================================
PondDesignResult DTMPond::CalculatePondAtElevation(double waterElevation) {
    PondDesignResult result;
    
    // Find all triangles that are below the water level
    std::vector<DTMTriangleId> submergedTriangles = FindSubmergedTriangles(waterElevation);
    
    if (submergedTriangles.empty()) {
        return PondDesignResult::Failure("No area below water elevation");
    }
    
    // Calculate pond boundary
    std::vector<Point3D> boundary = CalculatePondBoundary(waterElevation);
    
    if (boundary.size() < 3) {
        return PondDesignResult::Failure("Could not determine pond boundary");
    }
    
    // Calculate volume
    double volume = CalculateVolumeBelow(waterElevation, submergedTriangles);
    
    // Calculate surface area
    double surfaceArea = GeometryUtils::PolygonArea2D(boundary);
    
    // Calculate depth statistics
    DepthStatistics depthStats = CalculateDepthStatistics(waterElevation, submergedTriangles);
    
    // Set result properties
    result.success = true;
    result.calculated = true;
    result.waterElevation = waterElevation;
    result.volume = volume;
    result.surfaceArea = surfaceArea;
    result.boundary = boundary;
    result.depth = depthStats.maxDepth;
    result.averageDepth = depthStats.averageDepth;
    result.maxDepth = depthStats.maxDepth;
    result.minDepth = depthStats.minDepth;
    
    // Calculate additional properties
    if (surfaceArea > 0) {
        result.perimeter = CalculatePerimeter(boundary);
        result.shorelineLength = result.perimeter;
        
        // Shape factor (circularity)
        double circularPerimeter = 2.0 * Constants::PI * std::sqrt(surfaceArea / Constants::PI);
        result.shapeFactor = circularPerimeter / result.perimeter;
    }
    
    return result;
}

Point3D DTMPond::FindLowestPoint() const {
    Point3D lowestPoint;
    lowestPoint.z = std::numeric_limits<double>::max();
    
    const auto& vertices = m_dtm->GetVertices();
    
    for (const auto& pair : vertices) {
        const auto& vertex = pair.second;
        
        if (vertex->IsActive()) {
            Point3D pos = vertex->GetPosition();
            if (pos.z < lowestPoint.z) {
                lowestPoint = pos;
            }
        }
    }
    
    return lowestPoint;
}

double DTMPond::FindWaterLevelForVolume(const Point3D& startPoint, double targetVolume) {
    double minElevation = startPoint.z + m_minDepth;
    double maxElevation = startPoint.z + m_maxDepth;
    
    // Binary search for the water level that gives target volume
    for (int iteration = 0; iteration < m_maxIterations; ++iteration) {
        double testElevation = (minElevation + maxElevation) * 0.5;
        
        std::vector<DTMTriangleId> submergedTriangles = FindSubmergedTriangles(testElevation);
        double volume = CalculateVolumeBelow(testElevation, submergedTriangles);
        
        double volumeError = std::abs(volume - targetVolume) / targetVolume;
        
        if (volumeError < m_tolerance) {
            return testElevation;
        }
        
        if (volume < targetVolume) {
            minElevation = testElevation;
        } else {
            maxElevation = testElevation;
        }
        
        // Check for convergence
        if (std::abs(maxElevation - minElevation) < m_tolerance) {
            break;
        }
    }
    
    // Return best approximation
    return (minElevation + maxElevation) * 0.5;
}

std::vector<DTMTriangleId> DTMPond::FindSubmergedTriangles(double waterElevation) const {
    std::vector<DTMTriangleId> submergedTriangles;
    
    const auto& triangles = m_dtm->GetTriangles();
    const auto& vertices = m_dtm->GetVertices();
    
    for (const auto& pair : triangles) {
        const auto& triangle = pair.second;
        
        if (!triangle->IsActive()) {
            continue;
        }
        
        // Check if any vertex of the triangle is below water level
        bool hasSubmergedVertex = false;
        
        for (int i = 0; i < 3; ++i) {
            DTMVertexId vertexId = triangle->GetVertexId(i);
            auto vertexIt = vertices.find(vertexId);
            
            if (vertexIt != vertices.end()) {
                Point3D pos = vertexIt->second->GetPosition();
                if (pos.z < waterElevation) {
                    hasSubmergedVertex = true;
                    break;
                }
            }
        }
        
        if (hasSubmergedVertex) {
            submergedTriangles.push_back(pair.first);
        }
    }
    
    return submergedTriangles;
}

std::vector<Point3D> DTMPond::CalculatePondBoundary(double waterElevation) const {
    std::vector<Point3D> boundary;
    
    const auto& triangles = m_dtm->GetTriangles();
    const auto& vertices = m_dtm->GetVertices();
    const auto& edges = m_dtm->GetEdges();
    
    // Find edges that intersect the water level
    for (const auto& pair : edges) {
        const auto& edge = pair.second;
        
        if (!edge->IsActive()) {
            continue;
        }
        
        // Get edge vertices
        auto vertex1It = vertices.find(edge->GetVertex1Id());
        auto vertex2It = vertices.find(edge->GetVertex2Id());
        
        if (vertex1It == vertices.end() || vertex2It == vertices.end()) {
            continue;
        }
        
        Point3D p1 = vertex1It->second->GetPosition();
        Point3D p2 = vertex2It->second->GetPosition();
        
        // Check if edge crosses water level
        bool p1Below = p1.z < waterElevation;
        bool p2Below = p2.z < waterElevation;
        
        if (p1Below != p2Below) {
            // Edge crosses water level - find intersection point
            double t = (waterElevation - p1.z) / (p2.z - p1.z);
            Point3D intersection = GeometryUtils::Interpolate(p1, p2, t);
            boundary.push_back(intersection);
        }
    }
    
    // Sort boundary points to form a closed polygon
    if (boundary.size() >= 3) {
        boundary = SortBoundaryPoints(boundary);
    }
    
    return boundary;
}

std::vector<Point3D> DTMPond::SortBoundaryPoints(const std::vector<Point3D>& points) const {
    if (points.size() < 3) {
        return points;
    }
    
    // Find centroid
    Point3D centroid(0.0, 0.0, 0.0);
    for (const auto& point : points) {
        centroid.x += point.x;
        centroid.y += point.y;
        centroid.z += point.z;
    }
    centroid.x /= points.size();
    centroid.y /= points.size();
    centroid.z /= points.size();
    
    // Sort points by angle from centroid
    std::vector<std::pair<double, Point3D>> anglePoints;
    
    for (const auto& point : points) {
        double angle = std::atan2(point.y - centroid.y, point.x - centroid.x);
        anglePoints.emplace_back(angle, point);
    }
    
    std::sort(anglePoints.begin(), anglePoints.end());
    
    std::vector<Point3D> sortedPoints;
    for (const auto& pair : anglePoints) {
        sortedPoints.push_back(pair.second);
    }
    
    return sortedPoints;
}

double DTMPond::CalculateVolumeBelow(double waterElevation, const std::vector<DTMTriangleId>& submergedTriangles) const {
    double totalVolume = 0.0;
    
    const auto& triangles = m_dtm->GetTriangles();
    const auto& vertices = m_dtm->GetVertices();
    
    for (DTMTriangleId triangleId : submergedTriangles) {
        auto triangleIt = triangles.find(triangleId);
        if (triangleIt == triangles.end()) {
            continue;
        }
        
        const auto& triangle = triangleIt->second;
        
        // Get triangle vertices
        std::vector<Point3D> triangleVertices;
        for (int i = 0; i < 3; ++i) {
            DTMVertexId vertexId = triangle->GetVertexId(i);
            auto vertexIt = vertices.find(vertexId);
            if (vertexIt != vertices.end()) {
                triangleVertices.push_back(vertexIt->second->GetPosition());
            }
        }
        
        if (triangleVertices.size() == 3) {
            // Calculate volume contribution of this triangle
            double triangleVolume = CalculateTriangleVolumeBelow(triangleVertices, waterElevation);
            totalVolume += triangleVolume;
        }
    }
    
    return totalVolume;
}

double DTMPond::CalculateTriangleVolumeBelow(const std::vector<Point3D>& triangleVertices, double waterElevation) const {
    if (triangleVertices.size() != 3) {
        return 0.0;
    }
    
    // Calculate triangle area
    double area = GeometryUtils::TriangleArea2D(triangleVertices[0], triangleVertices[1], triangleVertices[2]);
    
    // Calculate average depth below water level
    double totalDepth = 0.0;
    int submergedCount = 0;
    
    for (const auto& vertex : triangleVertices) {
        if (vertex.z < waterElevation) {
            totalDepth += (waterElevation - vertex.z);
            submergedCount++;
        }
    }
    
    if (submergedCount == 0) {
        return 0.0;
    }
    
    double averageDepth = totalDepth / submergedCount;
    
    // Volume = area * average depth * submersion factor
    double submersionFactor = static_cast<double>(submergedCount) / 3.0;
    
    return area * averageDepth * submersionFactor;
}

DTMPond::DepthStatistics DTMPond::CalculateDepthStatistics(double waterElevation, const std::vector<DTMTriangleId>& submergedTriangles) const {
    DepthStatistics stats;
    stats.minDepth = std::numeric_limits<double>::max();
    stats.maxDepth = 0.0;
    stats.averageDepth = 0.0;
    
    const auto& triangles = m_dtm->GetTriangles();
    const auto& vertices = m_dtm->GetVertices();
    
    std::vector<double> depths;
    
    for (DTMTriangleId triangleId : submergedTriangles) {
        auto triangleIt = triangles.find(triangleId);
        if (triangleIt == triangles.end()) {
            continue;
        }
        
        const auto& triangle = triangleIt->second;
        
        // Check each vertex
        for (int i = 0; i < 3; ++i) {
            DTMVertexId vertexId = triangle->GetVertexId(i);
            auto vertexIt = vertices.find(vertexId);
            
            if (vertexIt != vertices.end()) {
                Point3D pos = vertexIt->second->GetPosition();
                if (pos.z < waterElevation) {
                    double depth = waterElevation - pos.z;
                    depths.push_back(depth);
                    
                    stats.minDepth = std::min(stats.minDepth, depth);
                    stats.maxDepth = std::max(stats.maxDepth, depth);
                }
            }
        }
    }
    
    if (!depths.empty()) {
        double totalDepth = 0.0;
        for (double depth : depths) {
            totalDepth += depth;
        }
        stats.averageDepth = totalDepth / depths.size();
    } else {
        stats.minDepth = 0.0;
    }
    
    return stats;
}

double DTMPond::CalculatePerimeter(const std::vector<Point3D>& boundary) const {
    if (boundary.size() < 3) {
        return 0.0;
    }
    
    double perimeter = 0.0;
    
    for (size_t i = 0; i < boundary.size(); ++i) {
        size_t nextIndex = (i + 1) % boundary.size();
        perimeter += GeometryUtils::Distance2D(boundary[i], boundary[nextIndex]);
    }
    
    return perimeter;
}

PondDesignResult DTMPond::CalculatePondWithBoundary(const std::vector<Point3D>& boundaryPoints, double waterElevation) {
    // TODO: Implement pond calculation within a specific boundary
    // This would involve:
    // 1. Finding triangles within the boundary
    // 2. Calculating volume only within the boundary
    // 3. Handling partial triangle intersections with boundary
    
    PondDesignResult result;
    result.success = false;
    result.message = "Pond calculation with boundary not yet implemented";
    
    return result;
}

double DTMPond::EvaluatePondDesign(const PondDesignResult& result, const PondOptimizationCriteria& criteria) const {
    double score = 0.0;
    
    // Volume score
    if (criteria.targetVolume > 0) {
        double volumeError = std::abs(result.volume - criteria.targetVolume) / criteria.targetVolume;
        score += (1.0 - volumeError) * criteria.volumeWeight;
    }
    
    // Depth score (prefer moderate depths)
    if (result.averageDepth > 0) {
        double idealDepth = (m_minDepth + m_maxDepth) * 0.5;
        double depthError = std::abs(result.averageDepth - idealDepth) / idealDepth;
        score += (1.0 - depthError) * criteria.depthWeight;
    }
    
    // Shape score (prefer more circular shapes)
    if (result.shapeFactor > 0) {
        score += result.shapeFactor * criteria.shapeWeight;
    }
    
    // Surface area score
    if (criteria.targetSurfaceArea > 0) {
        double areaError = std::abs(result.surfaceArea - criteria.targetSurfaceArea) / criteria.targetSurfaceArea;
        score += (1.0 - areaError) * criteria.areaWeight;
    }
    
    return score;
}

} // namespace TerrainModel
