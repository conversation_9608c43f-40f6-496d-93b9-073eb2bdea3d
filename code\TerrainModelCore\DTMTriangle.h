/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"

namespace TerrainModel {

// Forward declarations
class DTMVertex;
class DTMEdge;

//=======================================================================================
// DTM Vertex class representing a point in the triangulation
//=======================================================================================
class DTMVertex {
private:
    DTMVertexId m_vertexId;
    Point3D m_position;
    std::vector<DTMTriangleId> m_adjacentTriangles;
    std::vector<DTMEdgeId> m_adjacentEdges;
    DTMFeatureId m_sourceFeatureId;
    bool m_isBoundary;
    bool m_isConstrained;

public:
    //=======================================================================================
    // Constructors
    //=======================================================================================
    DTMVertex();
    DTMVertex(DTMVertexId id, const Point3D& position);
    DTMVertex(DTMVertexId id, const Point3D& position, DTMFeatureId sourceFeatureId);
    
    //=======================================================================================
    // Property accessors
    //=======================================================================================
    DTMVertexId GetVertexId() const { return m_vertexId; }
    const Point3D& GetPosition() const { return m_position; }
    void SetPosition(const Point3D& position) { m_position = position; }
    
    DTMFeatureId GetSourceFeatureId() const { return m_sourceFeatureId; }
    void SetSourceFeatureId(DTMFeatureId featureId) { m_sourceFeatureId = featureId; }
    
    bool IsBoundary() const { return m_isBoundary; }
    void SetBoundary(bool boundary) { m_isBoundary = boundary; }
    
    bool IsConstrained() const { return m_isConstrained; }
    void SetConstrained(bool constrained) { m_isConstrained = constrained; }
    
    //=======================================================================================
    // Adjacency management
    //=======================================================================================
    const std::vector<DTMTriangleId>& GetAdjacentTriangles() const { return m_adjacentTriangles; }
    const std::vector<DTMEdgeId>& GetAdjacentEdges() const { return m_adjacentEdges; }
    
    void AddAdjacentTriangle(DTMTriangleId triangleId);
    void RemoveAdjacentTriangle(DTMTriangleId triangleId);
    void AddAdjacentEdge(DTMEdgeId edgeId);
    void RemoveAdjacentEdge(DTMEdgeId edgeId);
    
    size_t GetDegree() const { return m_adjacentEdges.size(); }
    
    //=======================================================================================
    // Utility methods
    //=======================================================================================
    double DistanceTo(const DTMVertex& other) const;
    double DistanceTo(const Point3D& point) const;
    bool IsValid() const;
};

//=======================================================================================
// DTM Edge class representing an edge in the triangulation
//=======================================================================================
class DTMEdge {
private:
    DTMEdgeId m_edgeId;
    DTMVertexId m_vertex1Id;
    DTMVertexId m_vertex2Id;
    DTMTriangleId m_leftTriangleId;
    DTMTriangleId m_rightTriangleId;
    DTMFeatureId m_sourceFeatureId;
    bool m_isConstrained;
    bool m_isBoundary;
    double m_length;

public:
    //=======================================================================================
    // Constructors
    //=======================================================================================
    DTMEdge();
    DTMEdge(DTMEdgeId id, DTMVertexId vertex1Id, DTMVertexId vertex2Id);
    DTMEdge(DTMEdgeId id, DTMVertexId vertex1Id, DTMVertexId vertex2Id, DTMFeatureId sourceFeatureId);
    
    //=======================================================================================
    // Property accessors
    //=======================================================================================
    DTMEdgeId GetEdgeId() const { return m_edgeId; }
    
    DTMVertexId GetVertex1Id() const { return m_vertex1Id; }
    DTMVertexId GetVertex2Id() const { return m_vertex2Id; }
    std::pair<DTMVertexId, DTMVertexId> GetVertexIds() const { return {m_vertex1Id, m_vertex2Id}; }
    
    DTMTriangleId GetLeftTriangleId() const { return m_leftTriangleId; }
    DTMTriangleId GetRightTriangleId() const { return m_rightTriangleId; }
    void SetLeftTriangleId(DTMTriangleId triangleId) { m_leftTriangleId = triangleId; }
    void SetRightTriangleId(DTMTriangleId triangleId) { m_rightTriangleId = triangleId; }
    
    DTMFeatureId GetSourceFeatureId() const { return m_sourceFeatureId; }
    void SetSourceFeatureId(DTMFeatureId featureId) { m_sourceFeatureId = featureId; }
    
    bool IsConstrained() const { return m_isConstrained; }
    void SetConstrained(bool constrained) { m_isConstrained = constrained; }
    
    bool IsBoundary() const { return m_isBoundary; }
    void SetBoundary(bool boundary) { m_isBoundary = boundary; }
    
    double GetLength() const { return m_length; }
    void SetLength(double length) { m_length = length; }
    
    //=======================================================================================
    // Utility methods
    //=======================================================================================
    bool HasTriangle(DTMTriangleId triangleId) const;
    DTMTriangleId GetOtherTriangle(DTMTriangleId triangleId) const;
    bool SharesVertex(const DTMEdge& other) const;
    DTMVertexId GetSharedVertex(const DTMEdge& other) const;
    DTMVertexId GetOtherVertex(DTMVertexId vertexId) const;
    bool IsValid() const;
};

//=======================================================================================
// DTM Triangle class representing a triangle in the triangulation
//=======================================================================================
class DTMTriangle {
private:
    DTMTriangleId m_triangleId;
    std::array<DTMVertexId, 3> m_vertexIds;
    std::array<DTMEdgeId, 3> m_edgeIds;
    Point3D m_centroid;
    double m_area;
    double m_perimeter;
    Range3D m_bounds;
    bool m_isValid;
    bool m_propertiesCached;

public:
    //=======================================================================================
    // Constructors
    //=======================================================================================
    DTMTriangle();
    DTMTriangle(DTMTriangleId id, DTMVertexId v1, DTMVertexId v2, DTMVertexId v3);
    DTMTriangle(DTMTriangleId id, const std::array<DTMVertexId, 3>& vertexIds);
    
    //=======================================================================================
    // Property accessors
    //=======================================================================================
    DTMTriangleId GetTriangleId() const { return m_triangleId; }
    
    const std::array<DTMVertexId, 3>& GetVertexIds() const { return m_vertexIds; }
    DTMVertexId GetVertexId(int index) const { return m_vertexIds[index]; }
    void SetVertexIds(const std::array<DTMVertexId, 3>& vertexIds);
    
    const std::array<DTMEdgeId, 3>& GetEdgeIds() const { return m_edgeIds; }
    DTMEdgeId GetEdgeId(int index) const { return m_edgeIds[index]; }
    void SetEdgeIds(const std::array<DTMEdgeId, 3>& edgeIds) { m_edgeIds = edgeIds; }
    
    //=======================================================================================
    // Geometric properties (cached)
    //=======================================================================================
    const Point3D& GetCentroid() const;
    double GetArea() const;
    double GetPerimeter() const;
    const Range3D& GetBounds() const;
    
    //=======================================================================================
    // Geometric operations
    //=======================================================================================
    bool ContainsPoint(const Point3D& point, const std::vector<DTMVertexPtr>& vertices) const;
    Point3D InterpolateElevation(const Point3D& point, const std::vector<DTMVertexPtr>& vertices) const;
    Point3D GetNormal(const std::vector<DTMVertexPtr>& vertices) const;
    double GetSlope(const std::vector<DTMVertexPtr>& vertices) const;
    double GetAspect(const std::vector<DTMVertexPtr>& vertices) const;
    
    //=======================================================================================
    // Adjacency operations
    //=======================================================================================
    bool HasVertex(DTMVertexId vertexId) const;
    bool HasEdge(DTMEdgeId edgeId) const;
    bool SharesEdge(const DTMTriangle& other) const;
    DTMEdgeId GetSharedEdge(const DTMTriangle& other) const;
    DTMVertexId GetOppositeVertex(DTMEdgeId edgeId) const;
    
    //=======================================================================================
    // Quality metrics
    //=======================================================================================
    double GetAspectRatio(const std::vector<DTMVertexPtr>& vertices) const;
    double GetMinAngle(const std::vector<DTMVertexPtr>& vertices) const;
    double GetMaxAngle(const std::vector<DTMVertexPtr>& vertices) const;
    bool IsSliver(const std::vector<DTMVertexPtr>& vertices, double threshold = 0.1) const;
    
    //=======================================================================================
    // Validation
    //=======================================================================================
    bool IsValid() const { return m_isValid; }
    void SetValid(bool valid) { m_isValid = valid; }
    bool ValidateGeometry(const std::vector<DTMVertexPtr>& vertices) const;
    
    //=======================================================================================
    // Utility methods
    //=======================================================================================
    void InvalidateCache();
    void UpdateCache(const std::vector<DTMVertexPtr>& vertices);

private:
    //=======================================================================================
    // Internal calculation methods
    //=======================================================================================
    Point3D CalculateCentroid(const std::vector<DTMVertexPtr>& vertices) const;
    double CalculateArea(const std::vector<DTMVertexPtr>& vertices) const;
    double CalculatePerimeter(const std::vector<DTMVertexPtr>& vertices) const;
    Range3D CalculateBounds(const std::vector<DTMVertexPtr>& vertices) const;
    
    //=======================================================================================
    // Geometric helper methods
    //=======================================================================================
    static double TriangleArea(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static bool PointInTriangle(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    static Point3D BarycentricCoordinates(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    static double AngleBetweenVectors(const Point3D& v1, const Point3D& v2);
};

} // namespace TerrainModel
