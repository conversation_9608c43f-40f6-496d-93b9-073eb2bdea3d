/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include "DTM.h"

namespace TerrainModel {

//=======================================================================================
// DTM Edit Operation Result
//=======================================================================================
struct DTMEditResult {
    bool success = false;
    std::string message;
    int affectedTriangles = 0;
    int affectedVertices = 0;
    int affectedEdges = 0;
    std::vector<DTMTriangleId> modifiedTriangles;
    std::vector<DTMVertexId> modifiedVertices;
    std::vector<DTMEdgeId> modifiedEdges;
    
    DTMEditResult() = default;
    DTMEditResult(bool s, const std::string& msg = "") : success(s), message(msg) {}
    
    static DTMEditResult Success(const std::string& msg = "Operation completed successfully") {
        return DTMEditResult(true, msg);
    }
    
    static DTMEditResult Failure(const std::string& msg = "Operation failed") {
        return DTMEditResult(false, msg);
    }
};

//=======================================================================================
// DTM Tin Editor class for advanced triangulation editing
//=======================================================================================
class DTMTinEditor {
private:
    DTMPtr m_dtm;
    std::vector<Point3D> m_featurePoints;
    bool m_isEditing;
    
    // Undo/Redo support
    struct EditOperation {
        std::string operationType;
        std::vector<DTMVertexPtr> savedVertices;
        std::vector<DTMTrianglePtr> savedTriangles;
        std::vector<DTMEdgePtr> savedEdges;
        std::function<void()> undoFunction;
        std::function<void()> redoFunction;
    };
    
    std::vector<EditOperation> m_undoStack;
    std::vector<EditOperation> m_redoStack;
    size_t m_maxUndoLevels;

public:
    //=======================================================================================
    // Constructors and Destructor
    //=======================================================================================
    explicit DTMTinEditor(DTMPtr dtm);
    ~DTMTinEditor();
    
    // Non-copyable
    DTMTinEditor(const DTMTinEditor&) = delete;
    DTMTinEditor& operator=(const DTMTinEditor&) = delete;

    //=======================================================================================
    // Edit session management
    //=======================================================================================
    bool StartEdit();
    bool EndEdit();
    bool CancelEdit();
    bool IsEditing() const { return m_isEditing; }
    
    //=======================================================================================
    // Feature point management
    //=======================================================================================
    void SetFeaturePoints(const std::vector<Point3D>& points);
    const std::vector<Point3D>& GetFeaturePoints() const { return m_featurePoints; }
    void AddFeaturePoint(const Point3D& point);
    void InsertFeaturePoint(size_t index, const Point3D& point);
    void RemoveFeaturePoint(size_t index);
    void ClearFeaturePoints();
    
    //=======================================================================================
    // Triangle operations
    //=======================================================================================
    DTMEditResult FlipEdge(DTMEdgeId edgeId);
    DTMEditResult SplitTriangle(DTMTriangleId triangleId, const Point3D& point);
    DTMEditResult MergeTriangles(DTMTriangleId triangle1Id, DTMTriangleId triangle2Id);
    DTMEditResult DeleteTriangle(DTMTriangleId triangleId);
    DTMEditResult SubdivideTriangle(DTMTriangleId triangleId, int subdivisionLevel = 1);
    
    //=======================================================================================
    // Vertex operations
    //=======================================================================================
    DTMEditResult MoveVertex(DTMVertexId vertexId, const Point3D& newPosition);
    DTMEditResult DeleteVertex(DTMVertexId vertexId);
    DTMEditResult InsertVertex(const Point3D& point);
    DTMEditResult MergeVertices(DTMVertexId vertex1Id, DTMVertexId vertex2Id, const Point3D& mergedPosition);
    
    //=======================================================================================
    // Edge operations
    //=======================================================================================
    DTMEditResult SwapEdge(DTMEdgeId edgeId);
    DTMEditResult ConstrainEdge(DTMEdgeId edgeId);
    DTMEditResult UnconstrainEdge(DTMEdgeId edgeId);
    DTMEditResult SplitEdge(DTMEdgeId edgeId, const Point3D& point);
    DTMEditResult CollapseEdge(DTMEdgeId edgeId);
    
    //=======================================================================================
    // Advanced editing operations
    //=======================================================================================
    DTMEditResult RefineRegion(const std::vector<Point3D>& polygon, double maxTriangleArea);
    DTMEditResult CoarsenRegion(const std::vector<Point3D>& polygon, double minTriangleArea);
    DTMEditResult SmoothRegion(const std::vector<Point3D>& polygon, int iterations = 1);
    DTMEditResult LocalRetriangulation(const std::vector<Point3D>& polygon);
    
    //=======================================================================================
    // Validation and repair
    //=======================================================================================
    bool ValidateTriangulation();
    DTMEditResult RepairTriangulation();
    DTMEditResult OptimizeTriangulation();
    DTMEditResult RemoveSliverTriangles(double aspectRatioThreshold = 0.1);
    DTMEditResult FixInvertedTriangles();
    
    //=======================================================================================
    // Undo/Redo operations
    //=======================================================================================
    bool CanUndo() const { return !m_undoStack.empty(); }
    bool CanRedo() const { return !m_redoStack.empty(); }
    DTMEditResult Undo();
    DTMEditResult Redo();
    void ClearUndoHistory();
    void SetMaxUndoLevels(size_t maxLevels) { m_maxUndoLevels = maxLevels; }
    
    //=======================================================================================
    // Query operations
    //=======================================================================================
    std::vector<DTMTriangleId> FindTrianglesInRegion(const std::vector<Point3D>& polygon) const;
    std::vector<DTMVertexId> FindVerticesInRegion(const std::vector<Point3D>& polygon) const;
    std::vector<DTMEdgeId> FindEdgesInRegion(const std::vector<Point3D>& polygon) const;
    
    DTMTrianglePtr GetTriangleAt(const Point3D& point) const;
    std::vector<DTMTrianglePtr> GetAdjacentTriangles(DTMVertexId vertexId) const;
    std::vector<DTMEdgePtr> GetAdjacentEdges(DTMVertexId vertexId) const;
    
    //=======================================================================================
    // Geometric analysis
    //=======================================================================================
    double CalculateTriangleQuality(DTMTriangleId triangleId) const;
    std::vector<DTMTriangleId> FindPoorQualityTriangles(double qualityThreshold = 0.3) const;
    std::vector<DTMTriangleId> FindSliverTriangles(double aspectRatioThreshold = 0.1) const;
    
    //=======================================================================================
    // Utility methods
    //=======================================================================================
    DTMPtr GetDTM() const { return m_dtm; }
    void SetDTM(DTMPtr dtm);

private:
    //=======================================================================================
    // Internal helper methods
    //=======================================================================================
    void SaveEditState(const std::string& operationType);
    void RestoreEditState(const EditOperation& operation);
    void AddToUndoStack(const EditOperation& operation);
    void ClearRedoStack();
    
    // Validation helpers
    bool ValidateEditOperation(const std::string& operationType) const;
    bool IsTriangleValid(DTMTriangleId triangleId) const;
    bool IsVertexValid(DTMVertexId vertexId) const;
    bool IsEdgeValid(DTMEdgeId edgeId) const;
    
    // Geometric helpers
    bool IsFlipValid(DTMEdgeId edgeId) const;
    bool WillCreateInvalidTriangle(const std::array<DTMVertexId, 3>& vertexIds) const;
    double CalculateTriangleAspectRatio(DTMTriangleId triangleId) const;
    
    // Triangulation helpers
    std::vector<DTMTriangleId> RetriangulateRegion(const std::vector<DTMVertexId>& vertices);
    void UpdateAdjacentTriangles(DTMVertexId vertexId);
    void UpdateTriangleNeighbors(DTMTriangleId triangleId);
    
    // Memory management
    void CleanupDeletedElements();
    void OptimizeDataStructures();
};

//=======================================================================================
// Triangle Quality Metrics
//=======================================================================================
struct TriangleQualityMetrics {
    double aspectRatio = 0.0;
    double minAngle = 0.0;
    double maxAngle = 0.0;
    double area = 0.0;
    double perimeter = 0.0;
    double radiusRatio = 0.0; // circumradius / inradius
    bool isSliver = false;
    bool isInverted = false;
    
    double GetQualityScore() const {
        // Quality score from 0 (worst) to 1 (best)
        if (isInverted) return 0.0;
        if (isSliver) return 0.1;
        
        // Based on aspect ratio and angle quality
        double angleQuality = std::min(minAngle / 60.0, (180.0 - maxAngle) / 120.0);
        double shapeQuality = 1.0 / (1.0 + aspectRatio);
        
        return (angleQuality + shapeQuality) * 0.5;
    }
};

//=======================================================================================
// Edit Operation Types
//=======================================================================================
namespace EditOperations {
    constexpr const char* FLIP_EDGE = "FlipEdge";
    constexpr const char* SPLIT_TRIANGLE = "SplitTriangle";
    constexpr const char* MERGE_TRIANGLES = "MergeTriangles";
    constexpr const char* DELETE_TRIANGLE = "DeleteTriangle";
    constexpr const char* MOVE_VERTEX = "MoveVertex";
    constexpr const char* DELETE_VERTEX = "DeleteVertex";
    constexpr const char* INSERT_VERTEX = "InsertVertex";
    constexpr const char* SWAP_EDGE = "SwapEdge";
    constexpr const char* CONSTRAIN_EDGE = "ConstrainEdge";
    constexpr const char* SPLIT_EDGE = "SplitEdge";
    constexpr const char* COLLAPSE_EDGE = "CollapseEdge";
    constexpr const char* REFINE_REGION = "RefineRegion";
    constexpr const char* COARSEN_REGION = "CoarsenRegion";
    constexpr const char* SMOOTH_REGION = "SmoothRegion";
    constexpr const char* RETRIANGULATE = "Retriangulate";
}

} // namespace TerrainModel
