/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"

namespace TerrainModel {

//=======================================================================================
// DTM Feature class representing terrain features (points, lines, etc.)
//=======================================================================================
class DTMFeature {
private:
    DTMFeatureId m_featureId;
    DTMFeatureType m_featureType;
    DTMUserTag m_userTag;
    std::vector<Point3D> m_points;
    std::string m_name;
    std::string m_description;
    bool m_isActive;
    bool m_isLocked;
    
    // Cached properties
    mutable double m_length;
    mutable double m_area;
    mutable Range3D m_bounds;
    mutable bool m_propertiesCached;

public:
    //=======================================================================================
    // Constructors and Destructor
    //=======================================================================================
    DTMFeature();
    DTMFeature(DTMFeatureId id, DTMFeatureType type, DTMUserTag userTag = 0);
    DTMFeature(DTMFeatureId id, DTMFeatureType type, const std::vector<Point3D>& points, DTMUserTag userTag = 0);
    virtual ~DTMFeature() = default;
    
    // Copy and move semantics
    DTMFeature(const DTMFeature& other);
    DTMFeature& operator=(const DTMFeature& other);
    DTMFeature(DTMFeature&& other) noexcept;
    DTMFeature& operator=(DTMFeature&& other) noexcept;

    //=======================================================================================
    // Factory methods
    //=======================================================================================
    static DTMFeaturePtr CreatePoint(DTMFeatureId id, const Point3D& point, DTMUserTag userTag = 0);
    static DTMFeaturePtr CreateLinear(DTMFeatureId id, DTMFeatureType type, 
                                     const std::vector<Point3D>& points, DTMUserTag userTag = 0);
    static DTMFeaturePtr CreateBreakLine(DTMFeatureId id, const std::vector<Point3D>& points, DTMUserTag userTag = 0);
    static DTMFeaturePtr CreateVoidLine(DTMFeatureId id, const std::vector<Point3D>& points, DTMUserTag userTag = 0);
    static DTMFeaturePtr CreateContourLine(DTMFeatureId id, const std::vector<Point3D>& points, DTMUserTag userTag = 0);

    //=======================================================================================
    // Property accessors
    //=======================================================================================
    DTMFeatureId GetFeatureId() const { return m_featureId; }
    DTMFeatureType GetFeatureType() const { return m_featureType; }
    DTMUserTag GetUserTag() const { return m_userTag; }
    void SetUserTag(DTMUserTag userTag) { m_userTag = userTag; }
    
    const std::string& GetName() const { return m_name; }
    void SetName(const std::string& name) { m_name = name; }
    
    const std::string& GetDescription() const { return m_description; }
    void SetDescription(const std::string& description) { m_description = description; }
    
    bool IsActive() const { return m_isActive; }
    void SetActive(bool active) { m_isActive = active; InvalidateCache(); }
    
    bool IsLocked() const { return m_isLocked; }
    void SetLocked(bool locked) { m_isLocked = locked; }

    //=======================================================================================
    // Point management
    //=======================================================================================
    const std::vector<Point3D>& GetPoints() const { return m_points; }
    void SetPoints(const std::vector<Point3D>& points);
    
    size_t GetPointCount() const { return m_points.size(); }
    const Point3D& GetPoint(size_t index) const;
    void SetPoint(size_t index, const Point3D& point);
    
    void AddPoint(const Point3D& point);
    void InsertPoint(size_t index, const Point3D& point);
    void RemovePoint(size_t index);
    void ClearPoints();
    
    //=======================================================================================
    // Geometric properties
    //=======================================================================================
    double GetLength() const;
    double GetArea() const;
    const Range3D& GetBounds() const;
    Point3D GetCentroid() const;
    
    //=======================================================================================
    // Geometric operations
    //=======================================================================================
    bool IsPointFeature() const { return m_featureType == DTMFeatureType::Point; }
    bool IsLinearFeature() const { return m_featureType != DTMFeatureType::Point; }
    bool IsClosed() const;
    bool IsValid() const;
    
    // Point-in-polygon test (for closed linear features)
    bool ContainsPoint(const Point3D& point) const;
    
    // Distance calculations
    double DistanceToPoint(const Point3D& point) const;
    Point3D ClosestPointOnFeature(const Point3D& point) const;
    
    // Intersection operations
    std::vector<Point3D> IntersectWithLine(const Point3D& start, const Point3D& end) const;
    std::vector<Point3D> IntersectWithFeature(const DTMFeature& other) const;
    
    //=======================================================================================
    // Modification operations
    //=======================================================================================
    void Reverse();
    void Simplify(double tolerance);
    void Smooth(double factor = 0.5, int iterations = 1);
    void Offset(double distance);
    
    // Splitting and joining
    std::vector<DTMFeaturePtr> Split(const Point3D& splitPoint) const;
    std::vector<DTMFeaturePtr> Split(const std::vector<Point3D>& splitPoints) const;
    static DTMFeaturePtr Join(const std::vector<DTMFeaturePtr>& features, double tolerance = 1e-6);
    
    //=======================================================================================
    // Validation and repair
    //=======================================================================================
    bool ValidateGeometry() const;
    std::vector<std::string> GetValidationErrors() const;
    bool RepairGeometry();
    
    //=======================================================================================
    // Utility operations
    //=======================================================================================
    DTMFeaturePtr Clone() const;
    void Transform(const std::function<Point3D(const Point3D&)>& transformer);
    
    // Serialization
    std::string ToWKT() const;
    bool FromWKT(const std::string& wkt);
    
    //=======================================================================================
    // Comparison operators
    //=======================================================================================
    bool operator==(const DTMFeature& other) const;
    bool operator!=(const DTMFeature& other) const { return !(*this == other); }

private:
    //=======================================================================================
    // Internal helper methods
    //=======================================================================================
    void InvalidateCache() const;
    void UpdateCache() const;
    
    // Geometric calculations
    double CalculateLength() const;
    double CalculateArea() const;
    Range3D CalculateBounds() const;
    
    // Validation helpers
    bool ValidatePointFeature() const;
    bool ValidateLinearFeature() const;
    
    // Geometric helpers
    static double PointToLineDistance(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static Point3D ClosestPointOnLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static bool LineIntersection(const Point3D& p1, const Point3D& p2, 
                                const Point3D& p3, const Point3D& p4, Point3D& intersection);
    static bool IsPointInPolygon(const Point3D& point, const std::vector<Point3D>& polygon);
    
    // Simplification helpers
    void DouglasPeuckerSimplify(std::vector<Point3D>& points, double tolerance) const;
    double PerpendicularDistance(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd) const;
};

//=======================================================================================
// DTM Feature Enumerator class for iterating through features
//=======================================================================================
class DTMFeatureEnumerator {
private:
    const std::unordered_map<DTMFeatureId, DTMFeaturePtr>* m_features;
    std::vector<DTMFeaturePtr> m_filteredFeatures;
    size_t m_currentIndex;
    
    // Filter settings
    std::unordered_set<DTMFeatureType> m_includedTypes;
    std::unordered_set<DTMFeatureType> m_excludedTypes;
    DTMUserTag m_minUserTag;
    DTMUserTag m_maxUserTag;
    bool m_useUserTagFilter;
    bool m_sortByType;
    bool m_sortById;

public:
    //=======================================================================================
    // Constructor and Destructor
    //=======================================================================================
    explicit DTMFeatureEnumerator(const std::unordered_map<DTMFeatureId, DTMFeaturePtr>& features);
    ~DTMFeatureEnumerator() = default;

    //=======================================================================================
    // Filter configuration
    //=======================================================================================
    void IncludeAllFeatures();
    void ExcludeAllFeatures();
    void IncludeFeatureType(DTMFeatureType featureType);
    void ExcludeFeatureType(DTMFeatureType featureType);
    void SetUserTagRange(DTMUserTag minTag, DTMUserTag maxTag);
    void ClearUserTagFilter();
    
    //=======================================================================================
    // Sorting configuration
    //=======================================================================================
    void SetSortByType(bool sort) { m_sortByType = sort; }
    void SetSortById(bool sort) { m_sortById = sort; }
    
    //=======================================================================================
    // Enumeration operations
    //=======================================================================================
    void Reset();
    bool MoveNext();
    DTMFeaturePtr Current() const;
    size_t GetCount() const { return m_filteredFeatures.size(); }
    bool IsAtEnd() const { return m_currentIndex >= m_filteredFeatures.size(); }

private:
    //=======================================================================================
    // Internal helper methods
    //=======================================================================================
    void ApplyFilters();
    void ApplySorting();
    bool PassesFilter(DTMFeaturePtr feature) const;
};

} // namespace TerrainModel
