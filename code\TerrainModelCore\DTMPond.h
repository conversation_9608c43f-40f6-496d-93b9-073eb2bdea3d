/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include "DTM.h"

namespace TerrainModel {

//=======================================================================================
// Pond Calculation Result
//=======================================================================================
struct PondCalculationResult {
    bool calculated = false;
    double elevation = 0.0;
    double depth = 0.0;
    double area = 0.0;
    double volume = 0.0;
    std::vector<Point3D> pondFeatures;
    std::vector<Point3D> boundary;
    std::string errorMessage;
    
    bool IsSuccess() const { return calculated && errorMessage.empty(); }
    
    static PondCalculationResult Success(double elev, double d, double a, double v) {
        PondCalculationResult result;
        result.calculated = true;
        result.elevation = elev;
        result.depth = d;
        result.area = a;
        result.volume = v;
        return result;
    }
    
    static PondCalculationResult Failure(const std::string& error) {
        PondCalculationResult result;
        result.calculated = false;
        result.errorMessage = error;
        return result;
    }
};

//=======================================================================================
// Stock Pile Result
//=======================================================================================
struct StockPileResult {
    double stockPileVolume = 0.0;
    DTMPtr stockPileDTM;
    DTMPtr mergedDTM;
    std::vector<Point3D> stockPileBoundary;
    double stockPileHeight = 0.0;
    bool success = false;
    std::string errorMessage;
    
    bool IsSuccess() const { return success && errorMessage.empty(); }
};

//=======================================================================================
// Pond Statistics
//=======================================================================================
struct PondStatistics {
    double surfaceArea = 0.0;
    double volume = 0.0;
    double maxDepth = 0.0;
    double averageDepth = 0.0;
    double perimeter = 0.0;
    Point3D centroid;
    double waterLevel = 0.0;
    std::vector<double> depthDistribution;
    Range3D bounds;
    
    // Additional statistics
    double shorelineLength = 0.0;
    double capacityAtSpillway = 0.0;
    std::vector<Point3D> deepestPoints;
    std::vector<Point3D> shallowAreas;
};

//=======================================================================================
// Pond Volume Table Entry
//=======================================================================================
struct PondVolumeTableEntry {
    double elevation = 0.0;
    double area = 0.0;
    double volume = 0.0;
    double incrementalVolume = 0.0;
    double incrementalArea = 0.0;
    
    PondVolumeTableEntry() = default;
    PondVolumeTableEntry(double elev, double a, double vol) 
        : elevation(elev), area(a), volume(vol) {}
};

//=======================================================================================
// Pond Cross Section
//=======================================================================================
struct PondCrossSection {
    Point3D startPoint;
    Point3D endPoint;
    std::vector<Point3D> profilePoints;
    std::vector<double> distances;
    std::vector<double> elevations;
    double totalLength = 0.0;
    double waterLevel = 0.0;
    double maxDepth = 0.0;
    std::vector<Point3D> waterIntersections;
};

//=======================================================================================
// Pond Visualization Data
//=======================================================================================
struct PondVisualizationData {
    std::vector<Point3D> boundaryPoints;
    std::vector<Point3D> waterSurfacePoints;
    std::vector<std::vector<Point3D>> contourLines;
    std::vector<std::array<int, 3>> triangles;
    std::vector<double> depthValues;
    Range3D bounds;
    std::vector<Point3D> spillwayPoints;
    std::vector<Point3D> inletPoints;
    std::vector<Point3D> outletPoints;
};

//=======================================================================================
// DTM Pond class for pond design and analysis
//=======================================================================================
class DTMPond {
private:
    DTMPtr m_dtm;
    std::vector<Point3D> m_pondBoundary;
    double m_targetVolume;
    double m_targetElevation;
    double m_targetDepth;
    PondCalculationResult m_lastCalculation;
    
    // Design parameters
    double m_sideSlope;
    double m_bottomWidth;
    double m_topWidth;
    bool m_useNaturalBoundary;
    
    // Analysis cache
    mutable std::map<double, PondVolumeTableEntry> m_volumeTable;
    mutable bool m_volumeTableValid;

public:
    //=======================================================================================
    // Constructors and Destructor
    //=======================================================================================
    explicit DTMPond(DTMPtr dtm);
    ~DTMPond() = default;
    
    // Non-copyable
    DTMPond(const DTMPond&) = delete;
    DTMPond& operator=(const DTMPond&) = delete;

    //=======================================================================================
    // Property accessors
    //=======================================================================================
    const std::vector<Point3D>& GetPondBoundary() const { return m_pondBoundary; }
    void SetPondBoundary(const std::vector<Point3D>& boundary);
    
    double GetTargetVolume() const { return m_targetVolume; }
    void SetTargetVolume(double volume) { m_targetVolume = volume; InvalidateCache(); }
    
    double GetTargetElevation() const { return m_targetElevation; }
    void SetTargetElevation(double elevation) { m_targetElevation = elevation; InvalidateCache(); }
    
    double GetTargetDepth() const { return m_targetDepth; }
    void SetTargetDepth(double depth) { m_targetDepth = depth; InvalidateCache(); }
    
    double GetSideSlope() const { return m_sideSlope; }
    void SetSideSlope(double slope) { m_sideSlope = slope; InvalidateCache(); }

    //=======================================================================================
    // Pond design operations
    //=======================================================================================
    PondCalculationResult CalculatePondByVolume(double volume);
    PondCalculationResult CalculatePondByElevation(double elevation);
    PondCalculationResult CalculatePondByDepth(double depth);
    PondCalculationResult CalculatePondByBoundary(const std::vector<Point3D>& boundary);
    
    //=======================================================================================
    // Pond analysis operations
    //=======================================================================================
    PondStatistics GetPondStatistics() const;
    std::vector<std::vector<Point3D>> GetPondContours(double interval = 1.0) const;
    PondCrossSection GetPondCrossSection(const Point3D& start, const Point3D& end) const;
    std::vector<PondVolumeTableEntry> GetPondVolumeTable(double startElevation, 
                                                         double endElevation, 
                                                         double interval = 0.5) const;
    
    //=======================================================================================
    // Stock pile operations
    //=======================================================================================
    StockPileResult CalculateStockPile(double height);
    DTMPtr CreateStockPileDTM(double height);
    DTMPtr MergeWithStockPile(DTMPtr stockPileDTM);
    
    //=======================================================================================
    // Pond boundary modification
    //=======================================================================================
    void AddPondBoundaryPoint(const Point3D& point);
    void RemovePondBoundaryPoint(size_t index);
    void InsertPondBoundaryPoint(size_t index, const Point3D& point);
    void ClearPondBoundary();
    
    //=======================================================================================
    // Validation and optimization
    //=======================================================================================
    bool ValidatePondDesign() const;
    bool OptimizePondShape();
    bool CheckPondFeasibility() const;
    std::vector<std::string> GetDesignWarnings() const;
    
    //=======================================================================================
    // Export and visualization
    //=======================================================================================
    std::map<std::string, double> ExportPondData() const;
    DTMMeshPtr GeneratePondMesh() const;
    PondVisualizationData GetPondVisualizationData() const;
    
    //=======================================================================================
    // Advanced analysis
    //=======================================================================================
    double CalculateSpillwayElevation() const;
    std::vector<Point3D> FindOptimalInletLocations(int count = 1) const;
    std::vector<Point3D> FindOptimalOutletLocations(int count = 1) const;
    double CalculateRetentionTime(double inflow) const;
    
    //=======================================================================================
    // Utility operations
    //=======================================================================================
    const PondCalculationResult& GetLastCalculation() const { return m_lastCalculation; }
    void Reset();
    std::shared_ptr<DTMPond> Clone() const;

private:
    //=======================================================================================
    // Internal calculation methods
    //=======================================================================================
    PondCalculationResult PerformPondCalculation(const std::string& method, double parameter);
    double CalculateVolumeAtElevation(double elevation) const;
    double CalculateAreaAtElevation(double elevation) const;
    std::vector<Point3D> GeneratePondContour(double elevation) const;
    
    //=======================================================================================
    // Boundary and geometry helpers
    //=======================================================================================
    bool ValidateBoundary() const;
    std::vector<Point3D> GenerateNaturalBoundary(double elevation) const;
    std::vector<Point3D> InterpolateBoundaryElevations(const std::vector<Point3D>& boundary, double waterLevel) const;
    
    //=======================================================================================
    // Volume calculation helpers
    //=======================================================================================
    void BuildVolumeTable() const;
    double InterpolateVolume(double elevation) const;
    double CalculateIncrementalVolume(double elevation1, double elevation2) const;
    
    //=======================================================================================
    // Cross section helpers
    //=======================================================================================
    std::vector<Point3D> GetCrossSectionPoints(const Point3D& start, const Point3D& end) const;
    std::vector<Point3D> FindWaterIntersections(const std::vector<Point3D>& profile, double waterLevel) const;
    
    //=======================================================================================
    // Optimization helpers
    //=======================================================================================
    double CalculateOptimalDepth(double targetVolume) const;
    std::vector<Point3D> OptimizeBoundaryShape() const;
    double CalculateEarthworkVolume() const;
    
    //=======================================================================================
    // Validation helpers
    //=======================================================================================
    bool CheckMinimumArea() const;
    bool CheckMaximumDepth() const;
    bool CheckSideSlopes() const;
    bool CheckDrainageRequirements() const;
    
    //=======================================================================================
    // Cache management
    //=======================================================================================
    void InvalidateCache() const;
    bool IsCacheValid() const { return m_volumeTableValid; }
};

//=======================================================================================
// Pond Design Parameters
//=======================================================================================
struct PondDesignParameters {
    double minimumVolume = 100.0;
    double maximumVolume = 100000.0;
    double minimumDepth = 1.0;
    double maximumDepth = 10.0;
    double defaultSideSlope = 3.0; // 3:1 (horizontal:vertical)
    double minimumFreeboard = 0.5;
    double spillwayWidth = 2.0;
    bool requireEmergencySpillway = true;
    bool requireSedimentStorage = true;
    double sedimentStorageRatio = 0.1; // 10% of total volume
};

} // namespace TerrainModel
