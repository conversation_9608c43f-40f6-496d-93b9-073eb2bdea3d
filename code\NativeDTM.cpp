/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "NativeDTM.h"
#include "TerrainModelUtils.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
NativeDTM::NativeDTM(const Napi::CallbackInfo& info) 
    : TerrainModelObjectWrap<NativeDTM>(info)
    , m_autoUpdateMemoryPressure(true)
    , m_memoryUsed(0)
{
    Napi::Env env = info.Env();
    
    if (info.Length() == 0) {
        // Default constructor
        m_nativeDtm = BcDTM::Create();
    } else if (info.Length() == 2 && info[0].IsNumber() && info[1].IsNumber()) {
        // Constructor with initial and increment point counts
        int iniPoint = info[0].As<Napi::Number>().Int32Value();
        int incPoint = info[1].As<Napi::Number>().Int32Value();
        m_nativeDtm = BcDTM::Create(iniPoint, incPoint);
    } else {
        ThrowTerrainModelError(env, "Invalid constructor arguments");
        return;
    }

    if (m_nativeDtm.IsNull()) {
        ThrowTerrainModelError(env, "Failed to create DTM instance");
        return;
    }

    CheckMemoryPressure();
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
NativeDTM::~NativeDTM()
{
    if (m_nativeDtm.IsValid()) {
        m_nativeDtm = nullptr;
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
void NativeDTM::Init(Napi::Env env, Napi::Object exports)
{
    Napi::HandleScope scope(env);
    
    Napi::Function func = DefineClass(env, "DTM", {
        // Static methods
        StaticMethod("createEmpty", &NativeDTM::CreateEmpty),
        StaticMethod("createFromFile", &NativeDTM::CreateFromFile),
        StaticMethod("createFromGeopakTinFile", &NativeDTM::CreateFromGeopakTinFile),
        StaticMethod("createFromGeopakDatFile", &NativeDTM::CreateFromGeopakDatFile),
        
        // Instance methods - DTM Creation and Management
        InstanceMethod("triangulate", &NativeDTM::Triangulate),
        InstanceMethod("checkTriangulation", &NativeDTM::CheckTriangulation),
        InstanceMethod("setTriangulationParameters", &NativeDTM::SetTriangulationParameters),
        InstanceMethod("getTriangulationParameters", &NativeDTM::GetTriangulationParameters),
        
        // Instance methods - File Operations
        InstanceMethod("save", &NativeDTM::Save),
        InstanceMethod("saveAsGeopakTinFile", &NativeDTM::SaveAsGeopakTinFile),
        InstanceMethod("populateFromGeopakTinFile", &NativeDTM::PopulateFromGeopakTinFile),
        
        // Instance methods - DTM Properties
        InstanceMethod("getRange3d", &NativeDTM::GetRange3d),
        InstanceMethod("getVerticesCount", &NativeDTM::GetVerticesCount),
        InstanceMethod("getTrianglesCount", &NativeDTM::GetTrianglesCount),
        InstanceMethod("getBoundary", &NativeDTM::GetBoundary),
        InstanceMethod("calculateFeatureStatistics", &NativeDTM::CalculateFeatureStatistics),
        
        // Instance methods - Feature Management
        InstanceMethod("addPointFeature", &NativeDTM::AddPointFeature),
        InstanceMethod("addLinearFeature", &NativeDTM::AddLinearFeature),
        InstanceMethod("addPoint", &NativeDTM::AddPoint),
        InstanceMethod("addPoints", &NativeDTM::AddPoints),
        InstanceMethod("getFeatureById", &NativeDTM::GetFeatureById),
        InstanceMethod("deleteFeatureById", &NativeDTM::DeleteFeatureById),
        InstanceMethod("deleteFeaturesByType", &NativeDTM::DeleteFeaturesByType),
        InstanceMethod("deleteFeaturesByUserTag", &NativeDTM::DeleteFeaturesByUserTag),
        
        // Instance methods - Draping
        InstanceMethod("drapePoint", &NativeDTM::DrapePoint),
        InstanceMethod("drapeLinearPoints", &NativeDTM::DrapeLinearPoints),
        
        // Instance methods - Analysis
        InstanceMethod("calculateSlopeArea", &NativeDTM::CalculateSlopeArea),
        InstanceMethod("calculateCutFill", &NativeDTM::CalculateCutFill),
        InstanceMethod("calculateVolume", &NativeDTM::CalculateVolume),
        
        // Instance methods - Utilities
        InstanceMethod("joinFeatures", &NativeDTM::JoinFeatures),
        InstanceMethod("removeHull", &NativeDTM::RemoveHull),
        InstanceMethod("dispose", &NativeDTM::Dispose),
        
        // Property accessors
        InstanceAccessor("externalHandle", &NativeDTM::GetExternalHandle, nullptr),
        InstanceAccessor("isTriangulated", &NativeDTM::GetIsTriangulated, nullptr),
    });

    exports.Set("DTM", func);
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTM::CreateEmpty(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    try {
        return NativeDTM::constructor.New({});
    } catch (const std::exception& e) {
        Napi::Error::New(env, e.what()).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTM::CreateFromFile(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string filename").ThrowAsJavaScriptException();
        return env.Null();
    }

    std::string fileName = info[0].As<Napi::String>().Utf8Value();
    
    try {
        BcDTMPtr bcDtmPtr = BcDTM::CreateFromTinFile(fileName.c_str());
        
        if (bcDtmPtr.IsNull()) {
            Napi::Error::New(env, "Failed to load DTM from file: " + fileName).ThrowAsJavaScriptException();
            return env.Null();
        }

        // Create a new NativeDTM instance and set its native DTM
        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->m_nativeDtm = bcDtmPtr;
        nativeDtm->CheckMemoryPressure();
        
        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error loading DTM from file: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
Napi::Value NativeDTM::Triangulate(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "triangulate");
    
    if (!IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM instance");
        return env.Null();
    }

    try {
        DTMStatusInt status = m_nativeDtm->Triangulate();
        
        // Create triangulation report object
        Napi::Object report = Napi::Object::New(env);
        report.Set("status", Napi::Number::New(env, static_cast<int>(status)));
        report.Set("success", Napi::Boolean::New(env, status == DTMStatus::Success));
        
        if (status == DTMStatus::Success) {
            report.Set("verticesCount", Napi::Number::New(env, static_cast<double>(m_nativeDtm->GetVerticesCount())));
            report.Set("trianglesCount", Napi::Number::New(env, m_nativeDtm->GetTrianglesCount()));
        }
        
        return report;
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Triangulation failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
void NativeDTM::CheckMemoryPressure()
{
    if (m_autoUpdateMemoryPressure && m_nativeDtm.IsValid()) {
        // Estimate memory usage based on vertices and triangles count
        size_t newMemoryUsed = 0;
        if (m_nativeDtm->IsTriangulated()) {
            newMemoryUsed = m_nativeDtm->GetVerticesCount() * sizeof(DPoint3d) + 
                           m_nativeDtm->GetTrianglesCount() * sizeof(int) * 3;
        }
        UpdateMemoryPressure(newMemoryUsed);
    }
}

//=======================================================================================
// @bsimethod                                                    
//=======================================================================================
void NativeDTM::UpdateMemoryPressure(size_t newMemoryUsed)
{
    if (newMemoryUsed > m_memoryUsed) {
        // Memory increased
        size_t increase = newMemoryUsed - m_memoryUsed;
        // Note: In a real implementation, you might want to call Node.js memory pressure APIs
        // napi_adjust_external_memory(env, static_cast<int64_t>(increase));
    } else if (newMemoryUsed < m_memoryUsed) {
        // Memory decreased
        size_t decrease = m_memoryUsed - newMemoryUsed;
        // napi_adjust_external_memory(env, -static_cast<int64_t>(decrease));
    }
    m_memoryUsed = newMemoryUsed;
}

//=======================================================================================
// @bsimethod
//=======================================================================================
Napi::Value NativeDTM::CreateFromGeopakTinFile(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string filename").ThrowAsJavaScriptException();
        return env.Null();
    }

    std::string fileName = info[0].As<Napi::String>().Utf8Value();

    try {
        BcDTMPtr bcDtmPtr = BcDTM::CreateFromTinFile(fileName.c_str());

        if (bcDtmPtr.IsNull()) {
            Napi::Error::New(env, "Failed to load DTM from GEOPAK file: " + fileName).ThrowAsJavaScriptException();
            return env.Null();
        }

        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->m_nativeDtm = bcDtmPtr;
        nativeDtm->CheckMemoryPressure();

        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error loading DTM from GEOPAK file: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod
//=======================================================================================
Napi::Value NativeDTM::CreateFromGeopakDatFile(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "Expected string filename").ThrowAsJavaScriptException();
        return env.Null();
    }

    std::string fileName = info[0].As<Napi::String>().Utf8Value();

    try {
        BcDTMPtr bcDtmPtr = BcDTM::CreateFromGeopakDatFile(fileName.c_str());

        if (bcDtmPtr.IsNull()) {
            Napi::Error::New(env, "Failed to load DTM from GEOPAK DAT file: " + fileName).ThrowAsJavaScriptException();
            return env.Null();
        }

        auto dtmInstance = NativeDTM::constructor.New({});
        NativeDTM* nativeDtm = NativeDTM::Unwrap(dtmInstance);
        nativeDtm->m_nativeDtm = bcDtmPtr;
        nativeDtm->CheckMemoryPressure();

        return dtmInstance;
    } catch (const std::exception& e) {
        Napi::Error::New(env, "Error loading DTM from GEOPAK DAT file: " + std::string(e.what())).ThrowAsJavaScriptException();
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod
//=======================================================================================
Napi::Value NativeDTM::CheckTriangulation(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "checkTriangulation");

    if (!IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM instance");
        return env.Null();
    }

    try {
        bool isValid = m_nativeDtm->CheckTriangulation();
        return Napi::Boolean::New(env, isValid);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Check triangulation failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod
//=======================================================================================
Napi::Value NativeDTM::SetTriangulationParameters(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "setTriangulationParameters");

    if (info.Length() < 4 || !info[0].IsNumber() || !info[1].IsNumber() ||
        !info[2].IsNumber() || !info[3].IsNumber()) {
        Napi::TypeError::New(env, "Expected (pointTol, lineTol, edgeOption, maxSide)").ThrowAsJavaScriptException();
        return env.Null();
    }

    if (!IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM instance");
        return env.Null();
    }

    try {
        double pointTol = info[0].As<Napi::Number>().DoubleValue();
        double lineTol = info[1].As<Napi::Number>().DoubleValue();
        int edgeOption = info[2].As<Napi::Number>().Int32Value();
        double maxSide = info[3].As<Napi::Number>().DoubleValue();

        bool success = m_nativeDtm->SetTriangulationParameters(pointTol, lineTol,
                                                              static_cast<DTMEdgeOption>(edgeOption), maxSide);
        return Napi::Boolean::New(env, success);
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Set triangulation parameters failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod
//=======================================================================================
Napi::Value NativeDTM::GetTriangulationParameters(const Napi::CallbackInfo& info)
{
    Napi::Env env = info.Env();
    CheckDisposed(env, "getTriangulationParameters");

    if (!IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM instance");
        return env.Null();
    }

    try {
        double pointTol, lineTol, maxSide;
        DTMEdgeOption edgeOption;

        m_nativeDtm->GetTriangulationParameters(pointTol, lineTol, edgeOption, maxSide);

        Napi::Object params = Napi::Object::New(env);
        params.Set("pointTolerance", Napi::Number::New(env, pointTol));
        params.Set("lineTolerance", Napi::Number::New(env, lineTol));
        params.Set("edgeOption", Napi::Number::New(env, static_cast<int>(edgeOption)));
        params.Set("maxSide", Napi::Number::New(env, maxSide));

        return params;
    } catch (const std::exception& e) {
        ThrowTerrainModelError(env, "Get triangulation parameters failed: " + std::string(e.what()));
        return env.Null();
    }
}

//=======================================================================================
// @bsimethod
//=======================================================================================
void NativeDTM::CheckIsTriangulated(Napi::Env env)
{
    if (!IsValid()) {
        ThrowTerrainModelError(env, "Invalid DTM instance");
        return;
    }

    if (!m_nativeDtm->IsTriangulated()) {
        ThrowTerrainModelError(env, "DTM must be triangulated before this operation");
    }
}

} // namespace TerrainModelNodeAddon
