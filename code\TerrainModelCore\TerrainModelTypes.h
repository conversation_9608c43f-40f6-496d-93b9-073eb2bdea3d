/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include <memory>
#include <vector>
#include <string>
#include <map>
#include <functional>
#include <cmath>
#include <limits>

namespace TerrainModel {

//=======================================================================================
// Forward declarations
//=======================================================================================
class DTM;
class DTMFeature;
class DTMTriangle;
class DTMVertex;
class DTMEdge;
class DTMMesh;
class DTMTinEditor;
class WaterAnalysis;
class DTMPond;

//=======================================================================================
// Basic geometric types
//=======================================================================================
struct Point3D {
    double x, y, z;
    
    Point3D() : x(0.0), y(0.0), z(0.0) {}
    Point3D(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Point3D operator+(const Point3D& other) const {
        return Point3D(x + other.x, y + other.y, z + other.z);
    }
    
    Point3D operator-(const Point3D& other) const {
        return Point3D(x - other.x, y - other.y, z - other.z);
    }
    
    Point3D operator*(double scalar) const {
        return Point3D(x * scalar, y * scalar, z * scalar);
    }
    
    double distance(const Point3D& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        return std::sqrt(dx*dx + dy*dy + dz*dz);
    }
    
    double distance2D(const Point3D& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        return std::sqrt(dx*dx + dy*dy);
    }
    
    bool isValid() const {
        return !std::isnan(x) && !std::isnan(y) && !std::isnan(z) &&
               !std::isinf(x) && !std::isinf(y) && !std::isinf(z);
    }
};

struct Range3D {
    Point3D low, high;
    
    Range3D() : low(Point3D()), high(Point3D()) {}
    Range3D(const Point3D& low_, const Point3D& high_) : low(low_), high(high_) {}
    
    void extend(const Point3D& point) {
        if (point.x < low.x) low.x = point.x;
        if (point.y < low.y) low.y = point.y;
        if (point.z < low.z) low.z = point.z;
        if (point.x > high.x) high.x = point.x;
        if (point.y > high.y) high.y = point.y;
        if (point.z > high.z) high.z = point.z;
    }
    
    bool contains(const Point3D& point) const {
        return point.x >= low.x && point.x <= high.x &&
               point.y >= low.y && point.y <= high.y &&
               point.z >= low.z && point.z <= high.z;
    }
    
    bool contains2D(const Point3D& point) const {
        return point.x >= low.x && point.x <= high.x &&
               point.y >= low.y && point.y <= high.y;
    }
    
    double width() const { return high.x - low.x; }
    double height() const { return high.y - low.y; }
    double depth() const { return high.z - low.z; }
    
    Point3D center() const {
        return Point3D((low.x + high.x) * 0.5, (low.y + high.y) * 0.5, (low.z + high.z) * 0.5);
    }
};

//=======================================================================================
// Enumerations
//=======================================================================================
enum class DTMFeatureType {
    Point = 0,
    BreakLine = 1,
    VoidLine = 2,
    HoleLine = 3,
    IslandLine = 4,
    ContourLine = 5
};

enum class DTMStatus {
    Success = 0,
    Error = 1,
    InvalidInput = 2,
    InsufficientData = 3,
    TriangulationFailed = 4,
    FileError = 5,
    MemoryError = 6
};

enum class DTMEdgeOption {
    NoRemove = 1,
    RemoveSliver = 2,
    RemoveMaxSide = 3
};

enum class DTMDrapedPointCode {
    External = 0,
    Triangle = 1,
    Void = 2,
    PointOrSide = 3
};

enum class DTMSideSlopeDirection {
    Left = 0,
    Right = 1,
    Both = 2
};

enum class DTMSideSlopeOption {
    ToSurface = 0,
    ToElevation = 1,
    ToHorizontalDistance = 2,
    ToDeltaElevation = 3
};

enum class WaterAnalysisResultType {
    Point = 0,
    Stream = 1,
    Pond = 2
};

enum class ZeroSlopeTraceOption {
    None = 0,
    TraceLastAngle = 1,
    Pond = 2
};

//=======================================================================================
// Type aliases
//=======================================================================================
using DTMFeatureId = int32_t;
using DTMUserTag = int32_t;
using DTMVertexId = int32_t;
using DTMTriangleId = int32_t;
using DTMEdgeId = int32_t;

//=======================================================================================
// Smart pointer types
//=======================================================================================
using DTMPtr = std::shared_ptr<DTM>;
using DTMFeaturePtr = std::shared_ptr<DTMFeature>;
using DTMTrianglePtr = std::shared_ptr<DTMTriangle>;
using DTMVertexPtr = std::shared_ptr<DTMVertex>;
using DTMEdgePtr = std::shared_ptr<DTMEdge>;
using DTMMeshPtr = std::shared_ptr<DTMMesh>;

//=======================================================================================
// Callback function types
//=======================================================================================
using ProgressCallback = std::function<bool(double progress, const std::string& message)>;
using FeatureBrowsingCallback = std::function<bool(DTMFeaturePtr feature)>;
using TriangleBrowsingCallback = std::function<bool(DTMTrianglePtr triangle)>;

//=======================================================================================
// Configuration structures
//=======================================================================================
struct TriangulationParameters {
    double pointTolerance = 1e-6;
    double lineTolerance = 1e-6;
    DTMEdgeOption edgeOption = DTMEdgeOption::RemoveSliver;
    double maxSide = 1000.0;
    bool useConstraints = true;
    bool optimizeTriangulation = true;
    
    TriangulationParameters() = default;
    TriangulationParameters(double pointTol, double lineTol, DTMEdgeOption edge, double maxS)
        : pointTolerance(pointTol), lineTolerance(lineTol), edgeOption(edge), maxSide(maxS) {}
};

struct DTMStatistics {
    size_t verticesCount = 0;
    size_t trianglesCount = 0;
    size_t edgesCount = 0;
    size_t featuresCount = 0;
    size_t pointFeaturesCount = 0;
    size_t linearFeaturesCount = 0;
    Range3D bounds;
    double totalArea = 0.0;
    double averageTriangleArea = 0.0;
    double minTriangleArea = 0.0;
    double maxTriangleArea = 0.0;
    bool isTriangulated = false;
    bool hasHull = false;
};

//=======================================================================================
// Result structures
//=======================================================================================
struct TriangulationResult {
    DTMStatus status = DTMStatus::Error;
    std::string message;
    size_t verticesCount = 0;
    size_t trianglesCount = 0;
    double elapsedTime = 0.0;
    std::vector<std::string> warnings;
    
    bool isSuccess() const { return status == DTMStatus::Success; }
};

struct DrapedPoint {
    Point3D originalPoint;
    Point3D drapedPoint;
    DTMDrapedPointCode code = DTMDrapedPointCode::External;
    DTMTriangleId triangleId = -1;
    std::vector<DTMFeatureId> intersectedFeatures;
    
    bool isValid() const { return code != DTMDrapedPointCode::External; }
    double elevation() const { return drapedPoint.z; }
};

struct SlopeAreaResult {
    double area = 0.0;
    double slopeArea = 0.0;
    double averageSlope = 0.0;
    double minimumSlope = 0.0;
    double maximumSlope = 0.0;
    std::vector<Point3D> polygon;
};

struct CutFillResult {
    double cutVolume = 0.0;
    double fillVolume = 0.0;
    double cutArea = 0.0;
    double fillArea = 0.0;
    double totalArea = 0.0;
    double balanceVolume = 0.0;
    std::vector<Point3D> cutRegions;
    std::vector<Point3D> fillRegions;
};

//=======================================================================================
// Constants
//=======================================================================================
namespace Constants {
    constexpr double DEFAULT_POINT_TOLERANCE = 1e-6;
    constexpr double DEFAULT_LINE_TOLERANCE = 1e-6;
    constexpr double DEFAULT_MAX_SIDE = 1000.0;
    constexpr double PI = 3.14159265358979323846;
    constexpr double DEGREES_TO_RADIANS = PI / 180.0;
    constexpr double RADIANS_TO_DEGREES = 180.0 / PI;
    constexpr int INVALID_ID = -1;
    constexpr double EPSILON = 1e-12;
}

} // namespace TerrainModel
