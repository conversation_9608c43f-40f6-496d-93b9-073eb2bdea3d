/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Side Slope Direction enumeration
//=======================================================================================
enum class DTMSideSlopeDirection {
    Left = 0,
    Right = 1,
    Both = 2
};

//=======================================================================================
// Side Slope Corner Option enumeration
//=======================================================================================
enum class DTMSideSlopeCornerOption {
    None = 0,
    Round = 1,
    Square = 2,
    Chamfer = 3
};

//=======================================================================================
// Side Slope Stroke Corner Option enumeration
//=======================================================================================
enum class DTMSideSlopeStrokeCornerOption {
    None = 0,
    Extend = 1,
    Trim = 2
};

//=======================================================================================
// Side Slope Radial Option enumeration
//=======================================================================================
enum class DTMSideSlopeRadialOption {
    ToSurface = 0,
    ToElevation = 1,
    ToDistance = 2,
    ToDelta = 3
};

//=======================================================================================
// Side Slope Cut Fill Option enumeration
//=======================================================================================
enum class DTMSideSlopeCutFillOption {
    Cut = 0,
    Fill = 1,
    Both = 2,
    Auto = 3
};

//=======================================================================================
// Side Slope Option enumeration
//=======================================================================================
enum class DTMSideSlopeOption {
    ToSurface = 0,
    ToElevation = 1,
    ToHorizontalDistance = 2,
    ToDeltaElevation = 3
};

//=======================================================================================
// DTM Slope Table Entry structure
//=======================================================================================
struct DTMSlopeTableEntry {
    double fromElevation;
    double toElevation;
    double cutSlope;
    double fillSlope;
    
    DTMSlopeTableEntry() 
        : fromElevation(0.0), toElevation(0.0), cutSlope(0.0), fillSlope(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static DTMSlopeTableEntry FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Native DTM Slope Table wrapper class
//=======================================================================================
class NativeDTMSlopeTable : public TerrainModelObjectWrap<NativeDTMSlopeTable>
{
private:
    std::vector<DTMSlopeTableEntry> m_entries;
    std::string m_name;
    std::string m_description;

public:
    // Constructor
    NativeDTMSlopeTable(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMSlopeTable() = default;

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetName(const Napi::CallbackInfo& info);
    void SetName(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetDescription(const Napi::CallbackInfo& info);
    void SetDescription(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetEntries(const Napi::CallbackInfo& info);

    // Instance methods - Table management
    Napi::Value AddEntry(const Napi::CallbackInfo& info);
    Napi::Value RemoveEntry(const Napi::CallbackInfo& info);
    Napi::Value InsertEntry(const Napi::CallbackInfo& info);
    Napi::Value ClearEntries(const Napi::CallbackInfo& info);
    Napi::Value GetEntryCount(const Napi::CallbackInfo& info);

    // Instance methods - Slope calculation
    Napi::Value GetSlopeAt(const Napi::CallbackInfo& info);
    Napi::Value GetCutSlopeAt(const Napi::CallbackInfo& info);
    Napi::Value GetFillSlopeAt(const Napi::CallbackInfo& info);
    Napi::Value InterpolateSlope(const Napi::CallbackInfo& info);

    // Instance methods - Validation and utility
    Napi::Value Validate(const Napi::CallbackInfo& info);
    Napi::Value Sort(const Napi::CallbackInfo& info);
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value Clone(const Napi::CallbackInfo& info);

private:
    // Helper methods
    double InterpolateSlopeValue(double elevation, bool useCutSlope);
    void SortEntriesByElevation();
    bool ValidateEntry(const DTMSlopeTableEntry& entry);
};

//=======================================================================================
// Native DTM Side Slope Input Point wrapper class
//=======================================================================================
class NativeDTMSideSlopeInputPoint : public TerrainModelObjectWrap<NativeDTMSideSlopeInputPoint>
{
private:
    Point3D m_startPoint;
    NativeDTM* m_slopeToDTM;
    Napi::ObjectReference m_slopeToDTMRef;
    double m_cutSlope;
    double m_fillSlope;
    DTMSideSlopeOption m_sideSlopeOption;
    double m_toElevation;
    double m_toDeltaElevation;
    double m_toHorizontalDistance;
    DTMSideSlopeCutFillOption m_cutFillSlopeOption;
    bool m_isSlopeForced;
    double m_forcedSlope;

public:
    // Constructor
    NativeDTMSideSlopeInputPoint(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMSideSlopeInputPoint();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetStartPoint(const Napi::CallbackInfo& info);
    void SetStartPoint(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetSlopeToDTM(const Napi::CallbackInfo& info);
    void SetSlopeToDTM(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetCutSlope(const Napi::CallbackInfo& info);
    Napi::Value GetFillSlope(const Napi::CallbackInfo& info);
    Napi::Value GetSideSlopeOption(const Napi::CallbackInfo& info);
    void SetSideSlopeOption(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetToElevation(const Napi::CallbackInfo& info);
    void SetToElevation(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetToDeltaElevation(const Napi::CallbackInfo& info);
    void SetToDeltaElevation(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetToHorizontalDistance(const Napi::CallbackInfo& info);
    void SetToHorizontalDistance(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetCutFillSlopeOption(const Napi::CallbackInfo& info);
    void SetCutFillSlopeOption(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetIsSlopeForced(const Napi::CallbackInfo& info);
    void SetIsSlopeForced(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetForcedSlope(const Napi::CallbackInfo& info);
    void SetForcedSlope(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods
    Napi::Value CalculateEndPoint(const Napi::CallbackInfo& info);
    Napi::Value GetSlopePoints(const Napi::CallbackInfo& info);
    Napi::Value Validate(const Napi::CallbackInfo& info);
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value Clone(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    bool IsValidPoint() const { return m_slopeToDTM != nullptr && !m_disposed; }

private:
    // Helper methods
    Point3D CalculateEndPointInternal();
    std::vector<Point3D> GenerateSlopePoints();
    bool ValidateConfiguration();
};

//=======================================================================================
// Native DTM Side Slope Input wrapper class
//=======================================================================================
class NativeDTMSideSlopeInput : public TerrainModelObjectWrap<NativeDTMSideSlopeInput>
{
private:
    NativeDTMSlopeTable* m_slopeTable;
    Napi::ObjectReference m_slopeTableRef;
    DTMSideSlopeDirection m_direction;
    DTMSideSlopeCornerOption m_cornerOption;
    DTMSideSlopeStrokeCornerOption m_strokeCornerOption;
    double m_cornerStrokeTolerance;
    double m_pointToPointTolerance;
    DTMUserTag m_breakTag;
    DTMUserTag m_sideSlopeTag;
    std::vector<NativeDTMSideSlopeInputPoint*> m_points;

public:
    // Constructor
    NativeDTMSideSlopeInput(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMSideSlopeInput();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetSlopeTable(const Napi::CallbackInfo& info);
    void SetSlopeTable(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetDirection(const Napi::CallbackInfo& info);
    void SetDirection(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetCornerOption(const Napi::CallbackInfo& info);
    void SetCornerOption(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetStrokeCornerOption(const Napi::CallbackInfo& info);
    void SetStrokeCornerOption(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetCornerStrokeTolerance(const Napi::CallbackInfo& info);
    void SetCornerStrokeTolerance(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetPointToPointTolerance(const Napi::CallbackInfo& info);
    void SetPointToPointTolerance(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetBreakTag(const Napi::CallbackInfo& info);
    void SetBreakTag(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetSideSlopeTag(const Napi::CallbackInfo& info);
    void SetSideSlopeTag(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods - Point management
    Napi::Value AddPoint(const Napi::CallbackInfo& info);
    Napi::Value InsertPoint(const Napi::CallbackInfo& info);
    Napi::Value RemovePoint(const Napi::CallbackInfo& info);
    Napi::Value GetPoint(const Napi::CallbackInfo& info);
    Napi::Value GetPointCount(const Napi::CallbackInfo& info);
    Napi::Value ClearPoints(const Napi::CallbackInfo& info);

    // Instance methods - Radial operations
    Napi::Value AddRadialToSurface(const Napi::CallbackInfo& info);
    Napi::Value AddRadialToSurfaceWithElevationLimit(const Napi::CallbackInfo& info);
    Napi::Value AddRadialOutHorizontalDistance(const Napi::CallbackInfo& info);

    // Instance methods - Calculation
    Napi::Value CalculateSideSlopes(const Napi::CallbackInfo& info);
    Napi::Value PreviewSideSlopes(const Napi::CallbackInfo& info);
    Napi::Value ValidateInput(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value Clone(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    bool IsValidInput() const { return !m_disposed; }

private:
    // Helper methods
    void InitializeDefaults();
    bool ValidateConfiguration();
    std::vector<NativeDTM*> PerformSideSlopeCalculation();
    std::vector<Point3D> GeneratePreviewPoints();
};

} // namespace TerrainModelNodeAddon
