/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Zero Slope Trace Option enumeration
//=======================================================================================
enum class ZeroSlopeTraceOption {
    None = 0,
    TraceLastAngle = 1,
    Pond = 2
};

//=======================================================================================
// Water Analysis Result Item Type enumeration
//=======================================================================================
enum class WaterAnalysisResultType {
    Point = 0,
    Stream = 1,
    Pond = 2
};

//=======================================================================================
// Native Water Analysis Result Item base class
//=======================================================================================
class NativeWaterAnalysisResultItem : public TerrainModelObjectWrap<NativeWaterAnalysisResultItem>
{
protected:
    WaterAnalysisResultType m_type;
    double m_waterVolume;
    std::vector<Point3D> m_points;

public:
    // Constructor
    NativeWaterAnalysisResultItem(const Napi::CallbackInfo& info);
    
    // Destructor
    virtual ~NativeWaterAnalysisResultItem() = default;

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetType(const Napi::CallbackInfo& info);
    Napi::Value GetWaterVolume(const Napi::CallbackInfo& info);
    Napi::Value GetPoints(const Napi::CallbackInfo& info);

    // Instance methods
    Napi::Value AsPoint(const Napi::CallbackInfo& info);
    Napi::Value AsStream(const Napi::CallbackInfo& info);
    Napi::Value AsPond(const Napi::CallbackInfo& info);
    Napi::Value ToJSON(const Napi::CallbackInfo& info);

    // Internal helper methods
    WaterAnalysisResultType GetResultType() const { return m_type; }
    double GetVolume() const { return m_waterVolume; }

    // Factory method for creating from native result
    static Napi::Object CreateFromNative(Napi::Env env, WaterAnalysisResultItemPtr nativeItem);

protected:
    // Helper methods
    void InitializeFromNative(WaterAnalysisResultItemPtr nativeItem);
    static std::string GetTypeName(WaterAnalysisResultType type);
};

//=======================================================================================
// Native Water Analysis Result Point class
//=======================================================================================
class NativeWaterAnalysisResultPoint : public NativeWaterAnalysisResultItem
{
private:
    Point3D m_location;
    double m_elevation;

public:
    // Constructor
    NativeWaterAnalysisResultPoint(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeWaterAnalysisResultPoint() = default;

    // Property accessors
    Napi::Value GetLocation(const Napi::CallbackInfo& info);
    Napi::Value GetElevation(const Napi::CallbackInfo& info);

    // Factory method
    static Napi::Object CreateFromNative(Napi::Env env, WaterAnalysisResultPointPtr nativePoint);

private:
    void InitializeFromNative(WaterAnalysisResultPointPtr nativePoint);
};

//=======================================================================================
// Native Water Analysis Result Stream class
//=======================================================================================
class NativeWaterAnalysisResultStream : public NativeWaterAnalysisResultItem
{
private:
    std::vector<Point3D> m_streamPath;
    double m_length;
    double m_averageSlope;
    Point3D m_startPoint;
    Point3D m_endPoint;

public:
    // Constructor
    NativeWaterAnalysisResultStream(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeWaterAnalysisResultStream() = default;

    // Property accessors
    Napi::Value GetStreamPath(const Napi::CallbackInfo& info);
    Napi::Value GetLength(const Napi::CallbackInfo& info);
    Napi::Value GetAverageSlope(const Napi::CallbackInfo& info);
    Napi::Value GetStartPoint(const Napi::CallbackInfo& info);
    Napi::Value GetEndPoint(const Napi::CallbackInfo& info);

    // Factory method
    static Napi::Object CreateFromNative(Napi::Env env, WaterAnalysisResultStreamPtr nativeStream);

private:
    void InitializeFromNative(WaterAnalysisResultStreamPtr nativeStream);
};

//=======================================================================================
// Native Water Analysis Result Pond class
//=======================================================================================
class NativeWaterAnalysisResultPond : public NativeWaterAnalysisResultItem
{
private:
    std::vector<Point3D> m_boundary;
    double m_area;
    double m_maxDepth;
    double m_averageDepth;
    Point3D m_centroid;
    double m_waterLevel;

public:
    // Constructor
    NativeWaterAnalysisResultPond(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeWaterAnalysisResultPond() = default;

    // Property accessors
    Napi::Value GetBoundary(const Napi::CallbackInfo& info);
    Napi::Value GetArea(const Napi::CallbackInfo& info);
    Napi::Value GetMaxDepth(const Napi::CallbackInfo& info);
    Napi::Value GetAverageDepth(const Napi::CallbackInfo& info);
    Napi::Value GetCentroid(const Napi::CallbackInfo& info);
    Napi::Value GetWaterLevel(const Napi::CallbackInfo& info);

    // Factory method
    static Napi::Object CreateFromNative(Napi::Env env, WaterAnalysisResultPondPtr nativePond);

private:
    void InitializeFromNative(WaterAnalysisResultPondPtr nativePond);
};

//=======================================================================================
// Native Water Analysis Result collection class
//=======================================================================================
class NativeWaterAnalysisResult : public TerrainModelObjectWrap<NativeWaterAnalysisResult>
{
private:
    std::vector<NativeWaterAnalysisResultItem*> m_items;
    double m_totalWaterVolume;

public:
    // Constructor
    NativeWaterAnalysisResult(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeWaterAnalysisResult();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetItems(const Napi::CallbackInfo& info);
    Napi::Value GetCount(const Napi::CallbackInfo& info);
    Napi::Value GetTotalWaterVolume(const Napi::CallbackInfo& info);

    // Instance methods - Collection access
    Napi::Value GetItem(const Napi::CallbackInfo& info);
    Napi::Value GetItemsByType(const Napi::CallbackInfo& info);
    Napi::Value GetPoints(const Napi::CallbackInfo& info);
    Napi::Value GetStreams(const Napi::CallbackInfo& info);
    Napi::Value GetPonds(const Napi::CallbackInfo& info);

    // Instance methods - Analysis
    Napi::Value GetStatistics(const Napi::CallbackInfo& info);
    Napi::Value ToJSON(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Factory method
    static Napi::Object CreateFromNative(Napi::Env env, WaterAnalysisResultPtr nativeResult);

private:
    void InitializeFromNative(WaterAnalysisResultPtr nativeResult);
    void CalculateTotalVolume();
};

//=======================================================================================
// Native Water Analysis main class
//=======================================================================================
class NativeWaterAnalysis : public TerrainModelObjectWrap<NativeWaterAnalysis>
{
private:
    TerrainModel::WaterAnalysis* m_nativeAnalysis;
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;
    ZeroSlopeTraceOption m_zeroSlopeOption;

public:
    // Constructor
    NativeWaterAnalysis(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeWaterAnalysis();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetZeroSlopeTraceOption(const Napi::CallbackInfo& info);
    void SetZeroSlopeTraceOption(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods - Analysis operations
    Napi::Value DoTrace(const Napi::CallbackInfo& info);
    Napi::Value DoTraceCallback(const Napi::CallbackInfo& info);
    Napi::Value AddWaterVolume(const Napi::CallbackInfo& info);
    Napi::Value GetResult(const Napi::CallbackInfo& info);
    Napi::Value Clone(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value Reset(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    TerrainModel::WaterAnalysis* GetHandle() const { return m_nativeAnalysis; }
    bool IsValidAnalysis() const { return m_nativeAnalysis != nullptr && !m_disposed; }

    // Factory method for creating from DTM
    static Napi::Object CreateFromDTM(Napi::Env env, NativeDTM* dtm);

private:
    // Helper methods
    void InitializeFromDTM(NativeDTM* dtm);
    static ZeroSlopeTraceOption ConvertZeroSlopeOption(int option);
    static int ConvertZeroSlopeOption(ZeroSlopeTraceOption option);
};

} // namespace TerrainModelNodeAddon
