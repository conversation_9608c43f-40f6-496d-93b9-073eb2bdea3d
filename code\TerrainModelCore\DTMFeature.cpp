/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "DTMFeature.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// DTMFeature implementation
//=======================================================================================
DTMFeature::DTMFeature(DTMFeatureId id, DTMFeatureType featureType)
    : m_id(id)
    , m_featureType(featureType)
    , m_isActive(true)
    , m_userTag(0)
    , m_elevation(0.0)
    , m_length(0.0)
    , m_area(0.0)
    , m_isClosed(false)
    , m_isSmoothed(false)
{
}

DTMFeature::~DTMFeature() = default;

void DTMFeature::AddPoint(const Point3D& point) {
    m_points.push_back(point);
    UpdateGeometry();
}

void DTMFeature::InsertPoint(size_t index, const Point3D& point) {
    if (index <= m_points.size()) {
        m_points.insert(m_points.begin() + index, point);
        UpdateGeometry();
    }
}

void DTMFeature::RemovePoint(size_t index) {
    if (index < m_points.size()) {
        m_points.erase(m_points.begin() + index);
        UpdateGeometry();
    }
}

void DTMFeature::SetPoint(size_t index, const Point3D& point) {
    if (index < m_points.size()) {
        m_points[index] = point;
        UpdateGeometry();
    }
}

Point3D DTMFeature::GetPoint(size_t index) const {
    if (index < m_points.size()) {
        return m_points[index];
    }
    return Point3D(0.0, 0.0, 0.0);
}

void DTMFeature::SetPoints(const std::vector<Point3D>& points) {
    m_points = points;
    UpdateGeometry();
}

void DTMFeature::ClearPoints() {
    m_points.clear();
    UpdateGeometry();
}

Range3D DTMFeature::GetBounds() const {
    if (m_points.empty()) {
        return Range3D();
    }
    
    return GeometryUtils::BoundingBox(m_points);
}

Point3D DTMFeature::GetCentroid() const {
    if (m_points.empty()) {
        return Point3D(0.0, 0.0, 0.0);
    }
    
    if (m_featureType == DTMFeatureType::Point) {
        return m_points[0];
    }
    
    if (IsLinearFeature()) {
        // For linear features, calculate centroid along the line
        if (m_points.size() == 1) {
            return m_points[0];
        }
        
        double totalLength = GetLength();
        if (totalLength <= 0.0) {
            // Fallback to simple average
            Point3D sum(0.0, 0.0, 0.0);
            for (const auto& point : m_points) {
                sum.x += point.x;
                sum.y += point.y;
                sum.z += point.z;
            }
            double n = static_cast<double>(m_points.size());
            return Point3D(sum.x / n, sum.y / n, sum.z / n);
        }
        
        double targetDistance = totalLength * 0.5;
        double currentDistance = 0.0;
        
        for (size_t i = 0; i < m_points.size() - 1; ++i) {
            double segmentLength = GeometryUtils::Distance3D(m_points[i], m_points[i + 1]);
            
            if (currentDistance + segmentLength >= targetDistance) {
                double t = (targetDistance - currentDistance) / segmentLength;
                return GeometryUtils::Interpolate(m_points[i], m_points[i + 1], t);
            }
            
            currentDistance += segmentLength;
        }
        
        return m_points.back();
    }
    
    if (IsAreaFeature()) {
        return GeometryUtils::PolygonCentroid2D(m_points);
    }
    
    // Default: simple average
    Point3D sum(0.0, 0.0, 0.0);
    for (const auto& point : m_points) {
        sum.x += point.x;
        sum.y += point.y;
        sum.z += point.z;
    }
    double n = static_cast<double>(m_points.size());
    return Point3D(sum.x / n, sum.y / n, sum.z / n);
}

//=======================================================================================
// Geometric operations
//=======================================================================================
double DTMFeature::DistanceTo(const Point3D& point) const {
    if (m_points.empty()) {
        return std::numeric_limits<double>::infinity();
    }
    
    if (m_featureType == DTMFeatureType::Point) {
        return GeometryUtils::Distance3D(m_points[0], point);
    }
    
    if (IsLinearFeature()) {
        double minDistance = std::numeric_limits<double>::infinity();
        
        for (size_t i = 0; i < m_points.size() - 1; ++i) {
            double distance = GeometryUtils::PointToLineDistance3D(point, m_points[i], m_points[i + 1]);
            minDistance = std::min(minDistance, distance);
        }
        
        return minDistance;
    }
    
    if (IsAreaFeature()) {
        if (GeometryUtils::IsPointInPolygon2D(point, m_points)) {
            return 0.0; // Point is inside
        }
        
        // Find distance to boundary
        double minDistance = std::numeric_limits<double>::infinity();
        size_t n = m_points.size();
        
        for (size_t i = 0; i < n; ++i) {
            size_t j = (i + 1) % n;
            double distance = GeometryUtils::PointToLineDistance3D(point, m_points[i], m_points[j]);
            minDistance = std::min(minDistance, distance);
        }
        
        return minDistance;
    }
    
    // Default: distance to closest point
    double minDistance = std::numeric_limits<double>::infinity();
    for (const auto& featurePoint : m_points) {
        double distance = GeometryUtils::Distance3D(point, featurePoint);
        minDistance = std::min(minDistance, distance);
    }
    
    return minDistance;
}

double DTMFeature::DistanceTo(const DTMFeature& other) const {
    if (m_points.empty() || other.m_points.empty()) {
        return std::numeric_limits<double>::infinity();
    }
    
    double minDistance = std::numeric_limits<double>::infinity();
    
    // Simple implementation: minimum distance between any two points
    for (const auto& point1 : m_points) {
        for (const auto& point2 : other.m_points) {
            double distance = GeometryUtils::Distance3D(point1, point2);
            minDistance = std::min(minDistance, distance);
        }
    }
    
    return minDistance;
}

bool DTMFeature::Intersects(const DTMFeature& other) const {
    if (m_points.empty() || other.m_points.empty()) {
        return false;
    }
    
    // Check bounding box intersection first
    Range3D bounds1 = GetBounds();
    Range3D bounds2 = other.GetBounds();
    
    if (!GeometryUtils::BoundingBoxIntersect(bounds1, bounds2)) {
        return false;
    }
    
    // For linear features, check line segment intersections
    if (IsLinearFeature() && other.IsLinearFeature()) {
        for (size_t i = 0; i < m_points.size() - 1; ++i) {
            for (size_t j = 0; j < other.m_points.size() - 1; ++j) {
                Point3D intersection;
                if (GeometryUtils::SegmentIntersection2D(
                    m_points[i], m_points[i + 1],
                    other.m_points[j], other.m_points[j + 1],
                    intersection)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // For area features, check if any points are inside the other polygon
    if (IsAreaFeature() && other.IsAreaFeature()) {
        for (const auto& point : m_points) {
            if (GeometryUtils::IsPointInPolygon2D(point, other.m_points)) {
                return true;
            }
        }
        
        for (const auto& point : other.m_points) {
            if (GeometryUtils::IsPointInPolygon2D(point, m_points)) {
                return true;
            }
        }
        
        return false;
    }
    
    // Mixed cases: use distance threshold
    return DistanceTo(other) < Constants::DEFAULT_POINT_TOLERANCE;
}

//=======================================================================================
// Feature modification
//=======================================================================================
void DTMFeature::Simplify(double tolerance) {
    if (m_points.size() <= 2 || !IsLinearFeature()) {
        return;
    }
    
    std::vector<Point3D> simplified = GeometryUtils::SimplifyPolyline(m_points, tolerance);
    if (simplified.size() >= 2) {
        m_points = simplified;
        UpdateGeometry();
    }
}

void DTMFeature::Smooth(double factor, int iterations) {
    if (m_points.size() <= 2 || !IsLinearFeature()) {
        return;
    }
    
    std::vector<Point3D> smoothed = GeometryUtils::SmoothPolyline(m_points, factor, iterations);
    if (!smoothed.empty()) {
        m_points = smoothed;
        m_isSmoothed = true;
        UpdateGeometry();
    }
}

void DTMFeature::Resample(double interval) {
    if (m_points.size() <= 1 || !IsLinearFeature()) {
        return;
    }
    
    std::vector<Point3D> resampled = GeometryUtils::ResamplePolyline(m_points, interval);
    if (resampled.size() >= 2) {
        m_points = resampled;
        UpdateGeometry();
    }
}

void DTMFeature::Offset(double distance) {
    if (m_points.empty()) {
        return;
    }
    
    // Simple implementation: offset each point perpendicular to the line direction
    if (IsLinearFeature() && m_points.size() >= 2) {
        std::vector<Point3D> offsetPoints;
        offsetPoints.reserve(m_points.size());
        
        for (size_t i = 0; i < m_points.size(); ++i) {
            Point3D direction;
            
            if (i == 0) {
                // First point: use direction to next point
                direction = Point3D(
                    m_points[1].y - m_points[0].y,
                    -(m_points[1].x - m_points[0].x),
                    0.0
                );
            } else if (i == m_points.size() - 1) {
                // Last point: use direction from previous point
                direction = Point3D(
                    m_points[i].y - m_points[i-1].y,
                    -(m_points[i].x - m_points[i-1].x),
                    0.0
                );
            } else {
                // Middle point: average of adjacent directions
                Point3D dir1(
                    m_points[i].y - m_points[i-1].y,
                    -(m_points[i].x - m_points[i-1].x),
                    0.0
                );
                Point3D dir2(
                    m_points[i+1].y - m_points[i].y,
                    -(m_points[i+1].x - m_points[i].x),
                    0.0
                );
                direction = Point3D(
                    (dir1.x + dir2.x) * 0.5,
                    (dir1.y + dir2.y) * 0.5,
                    0.0
                );
            }
            
            // Normalize and scale
            direction = GeometryUtils::Normalize(direction);
            Point3D offsetPoint = Point3D(
                m_points[i].x + direction.x * distance,
                m_points[i].y + direction.y * distance,
                m_points[i].z
            );
            
            offsetPoints.push_back(offsetPoint);
        }
        
        m_points = offsetPoints;
        UpdateGeometry();
    }
}

void DTMFeature::Reverse() {
    if (m_points.size() > 1) {
        std::reverse(m_points.begin(), m_points.end());
    }
}

//=======================================================================================
// Validation and repair
//=======================================================================================
bool DTMFeature::IsValid() const {
    if (!m_isActive) {
        return false;
    }
    
    if (m_points.empty()) {
        return false;
    }
    
    // Check for valid coordinates
    for (const auto& point : m_points) {
        if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
            return false;
        }
    }
    
    // Type-specific validation
    switch (m_featureType) {
        case DTMFeatureType::Point:
            return m_points.size() == 1;
            
        case DTMFeatureType::BreakLine:
        case DTMFeatureType::Contour:
            return m_points.size() >= 2;
            
        case DTMFeatureType::Void:
            return m_points.size() >= 3;
            
        default:
            return true;
    }
}

std::vector<std::string> DTMFeature::GetValidationErrors() const {
    std::vector<std::string> errors;
    
    if (!m_isActive) {
        errors.push_back("Feature is not active");
    }
    
    if (m_points.empty()) {
        errors.push_back("Feature has no points");
        return errors;
    }
    
    // Check for invalid coordinates
    for (size_t i = 0; i < m_points.size(); ++i) {
        const auto& point = m_points[i];
        if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
            errors.push_back("Point " + std::to_string(i) + " has invalid coordinates");
        }
    }
    
    // Type-specific validation
    switch (m_featureType) {
        case DTMFeatureType::Point:
            if (m_points.size() != 1) {
                errors.push_back("Point feature must have exactly one point");
            }
            break;
            
        case DTMFeatureType::BreakLine:
        case DTMFeatureType::Contour:
            if (m_points.size() < 2) {
                errors.push_back("Linear feature must have at least two points");
            }
            break;
            
        case DTMFeatureType::Void:
            if (m_points.size() < 3) {
                errors.push_back("Area feature must have at least three points");
            }
            break;
    }
    
    // Check for duplicate consecutive points
    for (size_t i = 1; i < m_points.size(); ++i) {
        if (GeometryUtils::Distance3D(m_points[i-1], m_points[i]) < Constants::DEFAULT_POINT_TOLERANCE) {
            errors.push_back("Duplicate consecutive points at indices " + 
                           std::to_string(i-1) + " and " + std::to_string(i));
        }
    }
    
    return errors;
}

bool DTMFeature::Repair() {
    if (m_points.empty()) {
        return false;
    }
    
    bool repaired = false;
    
    // Remove duplicate consecutive points
    auto it = std::unique(m_points.begin(), m_points.end(), 
        [](const Point3D& a, const Point3D& b) {
            return GeometryUtils::Distance3D(a, b) < Constants::DEFAULT_POINT_TOLERANCE;
        });
    
    if (it != m_points.end()) {
        m_points.erase(it, m_points.end());
        repaired = true;
    }
    
    // Remove invalid points
    auto validIt = std::remove_if(m_points.begin(), m_points.end(),
        [](const Point3D& point) {
            return !std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z);
        });
    
    if (validIt != m_points.end()) {
        m_points.erase(validIt, m_points.end());
        repaired = true;
    }
    
    if (repaired) {
        UpdateGeometry();
    }
    
    return repaired && IsValid();
}

//=======================================================================================
// Internal helper methods
//=======================================================================================
void DTMFeature::UpdateGeometry() {
    if (m_points.empty()) {
        m_length = 0.0;
        m_area = 0.0;
        m_elevation = 0.0;
        return;
    }
    
    // Update elevation (average Z)
    double sumZ = 0.0;
    for (const auto& point : m_points) {
        sumZ += point.z;
    }
    m_elevation = sumZ / m_points.size();
    
    // Update length
    if (IsLinearFeature()) {
        m_length = 0.0;
        for (size_t i = 1; i < m_points.size(); ++i) {
            m_length += GeometryUtils::Distance3D(m_points[i-1], m_points[i]);
        }
    } else {
        m_length = 0.0;
    }
    
    // Update area
    if (IsAreaFeature()) {
        m_area = GeometryUtils::PolygonArea2D(m_points);
    } else {
        m_area = 0.0;
    }
    
    // Update closed status
    if (m_points.size() >= 3) {
        m_isClosed = GeometryUtils::Distance3D(m_points.front(), m_points.back()) < Constants::DEFAULT_POINT_TOLERANCE;
    } else {
        m_isClosed = false;
    }
}

bool DTMFeature::IsLinearFeature() const {
    return m_featureType == DTMFeatureType::BreakLine || 
           m_featureType == DTMFeatureType::Contour;
}

bool DTMFeature::IsAreaFeature() const {
    return m_featureType == DTMFeatureType::Void;
}

} // namespace TerrainModel
