/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

declare module 'terrain-model-node-addon' {
  
  // Basic geometric types
  export interface Point3D {
    x: number;
    y: number;
    z: number;
  }

  export interface Range3D {
    low: Point3D;
    high: Point3D;
  }

  // DTM Feature Types
  export enum DTMFeatureType {
    Point = 0,
    BreakLine = 1,
    VoidLine = 2,
    HoleLine = 3,
    IslandLine = 4,
    ContourLine = 5
  }

  // DTM Drape Point Codes
  export enum DTMDrapedPointCode {
    External = 0,
    Triangle = 1,
    Void = 2,
    PointOrSide = 3
  }

  // DTM Edge Options
  export enum DTMEdgeOption {
    NoRemove = 1,
    RemoveSliver = 2,
    RemoveMaxSide = 3
  }

  // DTM Side Slope Direction
  export enum DTMSideSlopeDirection {
    Left = 0,
    Right = 1,
    Both = 2
  }

  // DTM Side Slope Option
  export enum DTMSideSlopeOption {
    ToSurface = 0,
    ToElevation = 1,
    ToHorizontalDistance = 2,
    ToDeltaElevation = 3
  }

  // DTM Side Slope Cut Fill Option
  export enum DTMSideSlopeCutFillOption {
    Cut = 0,
    Fill = 1,
    Both = 2,
    Auto = 3
  }

  // Water Analysis Result Type
  export enum WaterAnalysisResultType {
    Point = 0,
    Stream = 1,
    Pond = 2
  }

  // Zero Slope Trace Option
  export enum ZeroSlopeTraceOption {
    None = 0,
    TraceLastAngle = 1,
    Pond = 2
  }

  // Triangulation Report
  export interface TriangulationReport {
    status: number;
    success: boolean;
    verticesCount?: number;
    trianglesCount?: number;
    errors?: string[];
    warnings?: string[];
  }

  // Feature Statistics
  export interface DTMFeatureStatistics {
    verticesCount: number;
    trianglesCount: number;
    triangleLinesCount: number;
    featuresCount: number;
    breakLinesCount: number;
    contourLinesCount: number;
    voidsCount: number;
    islandsCount: number;
    holesCount: number;
    pointFeaturesCount: number;
    hasHull: boolean;
  }

  // Triangulation Parameters
  export interface TriangulationParameters {
    pointTolerance: number;
    lineTolerance: number;
    edgeOption: DTMEdgeOption;
    maxSide: number;
  }

  // Slope Area Result
  export interface SlopeAreaResult {
    area: number;
    slopeArea: number;
    averageSlope: number;
    minimumSlope: number;
    maximumSlope: number;
  }

  // Cut Fill Result
  export interface CutFillResult {
    cutVolume: number;
    fillVolume: number;
    cutArea: number;
    fillArea: number;
    totalArea: number;
    balanceVolume: number;
  }

  // DTM Feature class
  export class DTMFeature {
    readonly featureType: DTMFeatureType;
    readonly featureId: number;
    readonly userTag: number;
    readonly points: Point3D[];
    readonly pointCount: number;

    getPoint(index: number): Point3D;
    setPoint(index: number, point: Point3D): void;
    addPoint(point: Point3D): void;
    insertPoint(index: number, point: Point3D): void;
    removePoint(index: number): void;
    getLength(): number;
    getArea(): number;
    isValid(): boolean;
    clone(): DTMFeature;
    dispose(): void;
  }

  // DTM Feature Info (read-only)
  export class DTMFeatureInfo {
    readonly featureType: DTMFeatureType;
    readonly featureId: number;
    readonly userTag: number;
    readonly points: Point3D[];
    readonly pointCount: number;

    getPoint(index: number): Point3D;
    toJSON(): object;
  }

  // DTM Draped Point
  export class DTMDrapedPoint {
    readonly originalPoint: Point3D;
    readonly drapedPoint: Point3D;
    readonly drapeCode: DTMDrapedPointCode;
    readonly isValid: boolean;
    readonly elevation: number;

    toJSON(): object;
  }

  // DTM Feature Enumerator
  export class DTMFeatureEnumerator {
    constructor(dtm: DTM);

    sort: boolean;
    readSourceFeatures: boolean;

    includeAllFeatures(): void;
    excludeAllFeatures(): void;
    includeFeature(type: DTMFeatureType): void;
    excludeFeature(type: DTMFeatureType): void;
    setUserTagRange(min: number, max: number): void;

    moveNext(): boolean;
    current(): DTMFeatureInfo | null;
    reset(): void;
    dispose(): void;
  }

  // DTM Tin Editor
  export class DTMTinEditor {
    constructor(dtm: DTM);

    // Feature editing
    setFeaturePoints(points: Point3D[]): void;
    getFeaturePoints(): Point3D[];
    addFeaturePoint(point: Point3D): void;
    insertFeaturePoint(index: number, point: Point3D): void;
    removeFeaturePoint(index: number): void;
    clearFeaturePoints(): void;

    // TIN editing operations
    startEdit(): boolean;
    endEdit(): boolean;
    cancelEdit(): boolean;
    isEditing(): boolean;

    // Triangle operations
    flipEdge(edgeId: number): DTMEditResult;
    splitTriangle(triangleId: number, point: Point3D): DTMEditResult;
    mergeTriangles(triangle1Id: number, triangle2Id: number): DTMEditResult;
    deleteTriangle(triangleId: number): DTMEditResult;

    // Vertex operations
    moveVertex(vertexId: number, newPosition: Point3D): DTMEditResult;
    deleteVertex(vertexId: number): DTMEditResult;
    insertVertex(point: Point3D): DTMEditResult;

    // Edge operations
    swapEdge(edgeId: number): DTMEditResult;
    constrainEdge(edgeId: number): DTMEditResult;
    unconstrainEdge(edgeId: number): DTMEditResult;

    // Validation and repair
    validateTriangulation(): boolean;
    repairTriangulation(): DTMEditResult;
    optimizeTriangulation(): DTMEditResult;

    dispose(): void;
  }

  // DTM Edit Result
  export interface DTMEditResult {
    success: boolean;
    message: string;
    affectedTriangles: number;
    affectedVertices: number;
  }

  // Triangle Info
  export interface TriangleInfo {
    triangleId: number;
    vertexIndices: [number, number, number];
    vertices: [Point3D, Point3D, Point3D];
    centroid: Point3D;
    area: number;
    isValid: boolean;
  }

  // Edge Info
  export interface EdgeInfo {
    edgeId: number;
    vertexIndices: [number, number];
    vertices: [Point3D, Point3D];
    length: number;
    isConstrained: boolean;
    isBoundary: boolean;
    adjacentTriangles: [number, number];
  }

  // Vertex Info
  export interface VertexInfo {
    vertexId: number;
    position: Point3D;
    adjacentTriangles: number[];
    adjacentEdges: number[];
    isBoundary: boolean;
    isConstrained: boolean;
  }

  // Main DTM class
  export class DTM {
    constructor();
    constructor(initialPoints: number, incrementPoints: number);

    // Static factory methods
    static createEmpty(): DTM;
    static createFromFile(fileName: string): DTM;
    static createFromGeopakTinFile(fileName: string): DTM;
    static createFromGeopakDatFile(fileName: string): DTM;

    // Properties
    readonly externalHandle: number;
    readonly isTriangulated: boolean;

    // DTM Creation and Management
    triangulate(): TriangulationReport;
    checkTriangulation(): boolean;
    setTriangulationParameters(pointTol: number, lineTol: number, edgeOption: DTMEdgeOption, maxSide: number): boolean;
    getTriangulationParameters(): TriangulationParameters;

    // File Operations
    save(fileName: string): void;
    saveAsGeopakTinFile(fileName: string): void;
    populateFromGeopakTinFile(fileName: string): void;

    // DTM Properties
    getRange3d(): Range3D;
    getVerticesCount(): number;
    getTrianglesCount(): number;
    getBoundary(): Point3D[];
    calculateFeatureStatistics(): DTMFeatureStatistics;

    // Feature Management
    addPointFeature(point: Point3D, userTag?: number): number;
    addPointFeature(points: Point3D[], userTag?: number): number;
    addLinearFeature(points: Point3D[], featureType: DTMFeatureType, userTag?: number): number;
    addPoint(point: Point3D): void;
    addPoints(points: Point3D[]): void;
    getFeatureById(featureId: number): DTMFeature | null;
    deleteFeatureById(featureId: number): void;
    deleteFeaturesByType(featureType: DTMFeatureType): void;
    deleteFeaturesByUserTag(userTag: number): void;

    // Draping
    drapePoint(point: Point3D): DTMDrapedPoint;
    drapeLinearPoints(points: Point3D[]): DTMDrapedPoint[];

    // Analysis
    calculateSlopeArea(): SlopeAreaResult;
    calculateSlopeArea(polygon: Point3D[]): SlopeAreaResult;
    calculateCutFill(otherDtm: DTM): CutFillResult;
    calculateCutFill(otherDtm: DTM, polygon: Point3D[]): CutFillResult;
    calculateVolume(elevation: number): number;
    calculateVolume(elevation: number, polygon: Point3D[]): number;

    // Utilities
    joinFeatures(featureType: DTMFeatureType, tolerance: number): void;
    removeHull(): void;
    dispose(): void;
  }

  // DTM Draped Linear Element Point
  export class DTMDrapedLinearElementPoint {
    readonly originalPoint: Point3D;
    readonly drapedPoint: Point3D;
    readonly code: number;
    readonly featureIds: number[];
    readonly features: DTMDrapePointFeature[];
    readonly distanceFromStart: number;
    readonly elevation: number;

    toJSON(): object;
    isValid(): boolean;
  }

  // DTM Draped Linear Element
  export class DTMDrapedLinearElement {
    readonly originalPoints: Point3D[];
    readonly drapedPoints: DTMDrapedLinearElementPoint[];
    readonly pointCount: number;
    readonly totalLength: number;
    readonly isValid: boolean;

    getPoint(index: number): DTMDrapedLinearElementPoint;
    getPointAt(distance: number): DTMDrapedLinearElementPoint;
    getElevationAt(distance: number): number;
    getSlopeAt(distance: number): number;
    getMinElevation(): number;
    getMaxElevation(): number;
    getAverageElevation(): number;
    getAverageSlope(): number;
    getTotalRise(): number;
    getTotalFall(): number;
    getSegments(): LinearElementSegment[];
    getSegmentAt(distance: number): LinearElementSegment;
    interpolateAt(distance: number): Point3D;
    toJSON(): object;
    toPointArray(): Point3D[];
    toElevationProfile(): ElevationProfile;
    dispose(): void;
  }

  // Water Analysis
  export class WaterAnalysis {
    constructor(dtm: DTM);

    zeroSlopeTraceOption: ZeroSlopeTraceOption;

    doTrace(startPoint: Point3D): WaterAnalysisResult;
    doTraceCallback(startPoint: Point3D, callback: (result: WaterAnalysisResult) => void): void;
    addWaterVolume(volume: number): void;
    getResult(): WaterAnalysisResult;
    clone(): WaterAnalysis;
    reset(): void;
    dispose(): void;
  }

  // Water Analysis Result
  export class WaterAnalysisResult {
    readonly items: WaterAnalysisResultItem[];
    readonly count: number;
    readonly totalWaterVolume: number;

    getItem(index: number): WaterAnalysisResultItem;
    getItemsByType(type: WaterAnalysisResultType): WaterAnalysisResultItem[];
    getPoints(): WaterAnalysisResultPoint[];
    getStreams(): WaterAnalysisResultStream[];
    getPonds(): WaterAnalysisResultPond[];
    getStatistics(): object;
    toJSON(): object;
    dispose(): void;
  }

  // Water Analysis Result Item
  export class WaterAnalysisResultItem {
    readonly type: WaterAnalysisResultType;
    readonly waterVolume: number;
    readonly points: Point3D[];

    asPoint(): WaterAnalysisResultPoint | null;
    asStream(): WaterAnalysisResultStream | null;
    asPond(): WaterAnalysisResultPond | null;
    toJSON(): object;
  }

  // Water Analysis Result Point
  export class WaterAnalysisResultPoint extends WaterAnalysisResultItem {
    readonly location: Point3D;
    readonly elevation: number;
  }

  // Water Analysis Result Stream
  export class WaterAnalysisResultStream extends WaterAnalysisResultItem {
    readonly streamPath: Point3D[];
    readonly length: number;
    readonly averageSlope: number;
    readonly startPoint: Point3D;
    readonly endPoint: Point3D;
  }

  // Water Analysis Result Pond
  export class WaterAnalysisResultPond extends WaterAnalysisResultItem {
    readonly boundary: Point3D[];
    readonly area: number;
    readonly maxDepth: number;
    readonly averageDepth: number;
    readonly centroid: Point3D;
    readonly waterLevel: number;
  }

  // DTM Pond
  export class DTMPond {
    constructor(dtm: DTM);

    pondBoundary: Point3D[];
    targetVolume: number;
    targetElevation: number;
    targetDepth: number;

    calculatePondByVolume(volume: number): PondCalculationResult;
    calculatePondByElevation(elevation: number): PondCalculationResult;
    calculatePondByDepth(depth: number): PondCalculationResult;
    calculatePondByBoundary(boundary: Point3D[]): PondCalculationResult;
    getPondStatistics(): PondStatistics;
    getPondContours(interval: number): Point3D[][];
    getPondCrossSection(start: Point3D, end: Point3D): PondCrossSection;
    getPondVolumeTable(startElevation: number, endElevation: number, interval: number): PondVolumeTableEntry[];
    calculateStockPile(height: number): StockPileResult;
    createStockPileDTM(height: number): DTM;
    mergeWithStockPile(stockPileDTM: DTM): DTM;
    addPondBoundaryPoint(point: Point3D): void;
    removePondBoundaryPoint(index: number): void;
    insertPondBoundaryPoint(index: number, point: Point3D): void;
    clearPondBoundary(): void;
    validatePondDesign(): boolean;
    optimizePondShape(): boolean;
    checkPondFeasibility(): boolean;
    exportPondData(): object;
    generatePondMesh(): DTMMesh;
    getPondVisualizationData(): PondVisualizationData;
    getLastCalculation(): PondCalculationResult;
    reset(): void;
    dispose(): void;
  }

  // Module-level functions
  export function createDTM(): DTM;
  export function createDTM(initialPoints: number, incrementPoints: number): DTM;
  export function loadDTMFromFile(fileName: string): DTM;
  export function createDTMFromPoints(points: Point3D[]): DTM;
  export function createDTMFromMesh(vertices: Point3D[], triangles: number[][]): DTM;
  export function mergeDTMs(dtm1: DTM, dtm2: DTM): DTM;
  export function compareDTMs(dtm1: DTM, dtm2: DTM): object;
  export function setLogLevel(level: string): void;
  export function getVersion(): string;
  export function initializeLogging(logFile?: string): void;

  // Supporting interfaces and types
  export interface DTMDrapePointFeature {
    featureType: DTMFeatureType;
    featureId: number;
    userTag: number;
  }

  export interface LinearElementSegment {
    startIndex: number;
    endIndex: number;
    startPoint: Point3D;
    endPoint: Point3D;
    length: number;
    slope: number;
    bearing: number;
  }

  export interface ElevationProfile {
    distances: number[];
    elevations: number[];
    slopes: number[];
    totalLength: number;
    minElevation: number;
    maxElevation: number;
    totalRise: number;
    totalFall: number;
  }

  export interface PondCalculationResult {
    calculated: boolean;
    elevation: number;
    depth: number;
    area: number;
    volume: number;
    pondFeatures: Point3D[];
  }

  export interface StockPileResult {
    stockPileVolume: number;
    stockPileDTM: DTM;
    mergedDTM: DTM;
  }

  export interface PondStatistics {
    surfaceArea: number;
    volume: number;
    maxDepth: number;
    averageDepth: number;
    perimeter: number;
    centroid: Point3D;
    waterLevel: number;
    depthDistribution: number[];
  }

  export interface PondVolumeTableEntry {
    elevation: number;
    area: number;
    volume: number;
    incrementalVolume: number;
  }

  export interface PondCrossSection {
    startPoint: Point3D;
    endPoint: Point3D;
    profilePoints: Point3D[];
    distances: number[];
    elevations: number[];
    totalLength: number;
    waterLevel: number;
  }

  export interface PondVisualizationData {
    boundaryPoints: Point3D[];
    waterSurfacePoints: Point3D[];
    contourLines: Point3D[][];
    triangles: [number, number, number][];
    depthValues: number[];
    bounds: Range3D;
  }

  export interface DTMSlopeTableEntry {
    fromElevation: number;
    toElevation: number;
    cutSlope: number;
    fillSlope: number;
  }

  export interface VisibilityAnalysisResult {
    visibility: number;
    visiblePoints: Point3D[];
    hiddenPoints: Point3D[];
    partiallyVisiblePoints: Point3D[];
    visibilityPercentage: number;
  }

  export interface ViewShedAnalysisResult {
    eyePoint: Point3D;
    visibleArea: Point3D[];
    shadowArea: Point3D[];
    totalVisibleArea: number;
    totalShadowArea: number;
    visibilityRatio: number;
  }

  export interface DrainageAnalysisResult {
    featureType: DTMFeatureType;
    drainageFeatures: Point3D[];
    lowPoints: Point3D[];
    minLowPoint: number;
  }

  export interface CacheStatistics {
    featureCacheSize: number;
    visibilityCacheSize: number;
    viewShedCacheSize: number;
    drainageCacheSize: number;
    totalMemoryUsage: number;
    hitRatio: number;
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
  }

  export interface SightlineAnalysisResult {
    startPoint: Point3D;
    endPoint: Point3D;
    obstructionPoints: Point3D[];
    isVisible: boolean;
    clearanceHeight: number;
    totalDistance: number;
    profilePoints: Point3D[];
  }

  export interface IntervisibilityAnalysisResult {
    viewpoints: Point3D[];
    visibilityMatrix: boolean[][];
    visibilityScores: number[];
    optimalViewpoint: Point3D;
    maxVisibilityScore: number;
  }

  // Module version
  export const version: string;
}
