/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

declare module 'terrain-model-node-addon' {
  
  // Basic geometric types
  export interface Point3D {
    x: number;
    y: number;
    z: number;
  }

  export interface Range3D {
    low: Point3D;
    high: Point3D;
  }

  // DTM Feature Types
  export enum DTMFeatureType {
    Point = 0,
    BreakLine = 1,
    VoidLine = 2,
    HoleLine = 3,
    IslandLine = 4,
    ContourLine = 5
  }

  // DTM Drape Point Codes
  export enum DTMDrapedPointCode {
    External = 0,
    Triangle = 1,
    Void = 2,
    PointOrSide = 3
  }

  // DTM Edge Options
  export enum DTMEdgeOption {
    NoRemove = 1,
    RemoveSliver = 2,
    RemoveMaxSide = 3
  }

  // Triangulation Report
  export interface TriangulationReport {
    status: number;
    success: boolean;
    verticesCount?: number;
    trianglesCount?: number;
    errors?: string[];
    warnings?: string[];
  }

  // Feature Statistics
  export interface DTMFeatureStatistics {
    verticesCount: number;
    trianglesCount: number;
    triangleLinesCount: number;
    featuresCount: number;
    breakLinesCount: number;
    contourLinesCount: number;
    voidsCount: number;
    islandsCount: number;
    holesCount: number;
    pointFeaturesCount: number;
    hasHull: boolean;
  }

  // Triangulation Parameters
  export interface TriangulationParameters {
    pointTolerance: number;
    lineTolerance: number;
    edgeOption: DTMEdgeOption;
    maxSide: number;
  }

  // Slope Area Result
  export interface SlopeAreaResult {
    area: number;
    slopeArea: number;
    averageSlope: number;
    minimumSlope: number;
    maximumSlope: number;
  }

  // Cut Fill Result
  export interface CutFillResult {
    cutVolume: number;
    fillVolume: number;
    cutArea: number;
    fillArea: number;
    totalArea: number;
    balanceVolume: number;
  }

  // DTM Feature class
  export class DTMFeature {
    readonly featureType: DTMFeatureType;
    readonly featureId: number;
    readonly userTag: number;
    readonly points: Point3D[];
    readonly pointCount: number;

    getPoint(index: number): Point3D;
    setPoint(index: number, point: Point3D): void;
    addPoint(point: Point3D): void;
    insertPoint(index: number, point: Point3D): void;
    removePoint(index: number): void;
    getLength(): number;
    getArea(): number;
    isValid(): boolean;
    clone(): DTMFeature;
    dispose(): void;
  }

  // DTM Feature Info (read-only)
  export class DTMFeatureInfo {
    readonly featureType: DTMFeatureType;
    readonly featureId: number;
    readonly userTag: number;
    readonly points: Point3D[];
    readonly pointCount: number;

    getPoint(index: number): Point3D;
    toJSON(): object;
  }

  // DTM Draped Point
  export class DTMDrapedPoint {
    readonly originalPoint: Point3D;
    readonly drapedPoint: Point3D;
    readonly drapeCode: DTMDrapedPointCode;
    readonly isValid: boolean;
    readonly elevation: number;

    toJSON(): object;
  }

  // DTM Feature Enumerator
  export class DTMFeatureEnumerator {
    constructor(dtm: DTM);
    
    sort: boolean;
    readSourceFeatures: boolean;
    
    includeAllFeatures(): void;
    excludeAllFeatures(): void;
    includeFeature(type: DTMFeatureType): void;
    excludeFeature(type: DTMFeatureType): void;
    setUserTagRange(min: number, max: number): void;
    
    moveNext(): boolean;
    current(): DTMFeatureInfo | null;
    reset(): void;
    dispose(): void;
  }

  // Main DTM class
  export class DTM {
    constructor();
    constructor(initialPoints: number, incrementPoints: number);

    // Static factory methods
    static createEmpty(): DTM;
    static createFromFile(fileName: string): DTM;
    static createFromGeopakTinFile(fileName: string): DTM;
    static createFromGeopakDatFile(fileName: string): DTM;

    // Properties
    readonly externalHandle: number;
    readonly isTriangulated: boolean;

    // DTM Creation and Management
    triangulate(): TriangulationReport;
    checkTriangulation(): boolean;
    setTriangulationParameters(pointTol: number, lineTol: number, edgeOption: DTMEdgeOption, maxSide: number): boolean;
    getTriangulationParameters(): TriangulationParameters;

    // File Operations
    save(fileName: string): void;
    saveAsGeopakTinFile(fileName: string): void;
    populateFromGeopakTinFile(fileName: string): void;

    // DTM Properties
    getRange3d(): Range3D;
    getVerticesCount(): number;
    getTrianglesCount(): number;
    getBoundary(): Point3D[];
    calculateFeatureStatistics(): DTMFeatureStatistics;

    // Feature Management
    addPointFeature(point: Point3D, userTag?: number): number;
    addPointFeature(points: Point3D[], userTag?: number): number;
    addLinearFeature(points: Point3D[], featureType: DTMFeatureType, userTag?: number): number;
    addPoint(point: Point3D): void;
    addPoints(points: Point3D[]): void;
    getFeatureById(featureId: number): DTMFeature | null;
    deleteFeatureById(featureId: number): void;
    deleteFeaturesByType(featureType: DTMFeatureType): void;
    deleteFeaturesByUserTag(userTag: number): void;

    // Draping
    drapePoint(point: Point3D): DTMDrapedPoint;
    drapeLinearPoints(points: Point3D[]): DTMDrapedPoint[];

    // Analysis
    calculateSlopeArea(): SlopeAreaResult;
    calculateSlopeArea(polygon: Point3D[]): SlopeAreaResult;
    calculateCutFill(otherDtm: DTM): CutFillResult;
    calculateCutFill(otherDtm: DTM, polygon: Point3D[]): CutFillResult;
    calculateVolume(elevation: number): number;
    calculateVolume(elevation: number, polygon: Point3D[]): number;

    // Utilities
    joinFeatures(featureType: DTMFeatureType, tolerance: number): void;
    removeHull(): void;
    dispose(): void;
  }

  // Module-level functions
  export function createDTM(): DTM;
  export function createDTM(initialPoints: number, incrementPoints: number): DTM;
  export function loadDTMFromFile(fileName: string): DTM;
  export function setLogLevel(level: string): void;

  // Module version
  export const version: string;
}
