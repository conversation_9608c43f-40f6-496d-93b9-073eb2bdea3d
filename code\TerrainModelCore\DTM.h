/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include "DTMFeature.h"
#include "DTMTriangle.h"
#include "DTMVertex.h"
#include "DTMEdge.h"
#include <unordered_map>
#include <unordered_set>

namespace TerrainModel {

//=======================================================================================
// Main DTM (Digital Terrain Model) class
//=======================================================================================
class DTM {
private:
    // Core data structures
    std::unordered_map<DTMVertexId, DTMVertexPtr> m_vertices;
    std::unordered_map<DTMTriangleId, DTMTrianglePtr> m_triangles;
    std::unordered_map<DTMEdgeId, DTMEdgePtr> m_edges;
    std::unordered_map<DTMFeatureId, DTMFeaturePtr> m_features;
    
    // Spatial indexing
    std::unique_ptr<class SpatialIndex> m_spatialIndex;
    
    // State and configuration
    bool m_isTriangulated;
    TriangulationParameters m_triangulationParams;
    Range3D m_bounds;
    DTMStatistics m_statistics;
    
    // ID generators
    DTMVertexId m_nextVertexId;
    DTMTriangleId m_nextTriangleId;
    DTMEdgeId m_nextEdgeId;
    DTMFeatureId m_nextFeatureId;
    
    // Memory management
    size_t m_initialPointCapacity;
    size_t m_incrementPointCapacity;

public:
    //=======================================================================================
    // Constructors and Destructor
    //=======================================================================================
    DTM();
    DTM(size_t initialPoints, size_t incrementPoints);
    virtual ~DTM();
    
    // Copy and move semantics
    DTM(const DTM& other) = delete;
    DTM& operator=(const DTM& other) = delete;
    DTM(DTM&& other) noexcept;
    DTM& operator=(DTM&& other) noexcept;

    //=======================================================================================
    // Factory methods
    //=======================================================================================
    static DTMPtr Create();
    static DTMPtr Create(size_t initialPoints, size_t incrementPoints);
    static DTMPtr CreateFromFile(const std::string& fileName);
    static DTMPtr CreateFromPoints(const std::vector<Point3D>& points);
    static DTMPtr CreateFromMesh(const std::vector<Point3D>& vertices, 
                                const std::vector<std::array<int, 3>>& triangles);

    //=======================================================================================
    // Triangulation operations
    //=======================================================================================
    TriangulationResult Triangulate();
    TriangulationResult Triangulate(const TriangulationParameters& params);
    TriangulationResult TriangulateAsync(ProgressCallback callback = nullptr);
    bool CheckTriangulation() const;
    void ClearTriangulation();
    
    //=======================================================================================
    // Feature management
    //=======================================================================================
    DTMFeatureId AddPointFeature(const Point3D& point, DTMUserTag userTag = 0);
    DTMFeatureId AddPointFeatures(const std::vector<Point3D>& points, DTMUserTag userTag = 0);
    DTMFeatureId AddLinearFeature(const std::vector<Point3D>& points, 
                                 DTMFeatureType featureType, DTMUserTag userTag = 0);
    
    void AddPoint(const Point3D& point);
    void AddPoints(const std::vector<Point3D>& points);
    
    DTMFeaturePtr GetFeature(DTMFeatureId featureId) const;
    std::vector<DTMFeaturePtr> GetFeatures() const;
    std::vector<DTMFeaturePtr> GetFeaturesByType(DTMFeatureType featureType) const;
    std::vector<DTMFeaturePtr> GetFeaturesByUserTag(DTMUserTag userTag) const;
    
    bool DeleteFeature(DTMFeatureId featureId);
    size_t DeleteFeaturesByType(DTMFeatureType featureType);
    size_t DeleteFeaturesByUserTag(DTMUserTag userTag);
    
    //=======================================================================================
    // Vertex management
    //=======================================================================================
    DTMVertexId AddVertex(const Point3D& point);
    DTMVertexPtr GetVertex(DTMVertexId vertexId) const;
    std::vector<DTMVertexPtr> GetVertices() const;
    bool DeleteVertex(DTMVertexId vertexId);
    bool MoveVertex(DTMVertexId vertexId, const Point3D& newPosition);
    
    //=======================================================================================
    // Triangle management
    //=======================================================================================
    DTMTrianglePtr GetTriangle(DTMTriangleId triangleId) const;
    std::vector<DTMTrianglePtr> GetTriangles() const;
    DTMTrianglePtr FindTriangleContaining(const Point3D& point) const;
    std::vector<DTMTrianglePtr> FindTrianglesIntersecting(const Range3D& bounds) const;
    
    //=======================================================================================
    // Edge management
    //=======================================================================================
    DTMEdgePtr GetEdge(DTMEdgeId edgeId) const;
    std::vector<DTMEdgePtr> GetEdges() const;
    std::vector<DTMEdgePtr> GetBoundaryEdges() const;
    std::vector<DTMEdgePtr> GetConstrainedEdges() const;
    
    //=======================================================================================
    // Draping operations
    //=======================================================================================
    DrapedPoint DrapePoint(const Point3D& point) const;
    std::vector<DrapedPoint> DrapePoints(const std::vector<Point3D>& points) const;
    std::vector<DrapedPoint> DrapeLinearPoints(const std::vector<Point3D>& points) const;
    
    //=======================================================================================
    // Analysis operations
    //=======================================================================================
    SlopeAreaResult CalculateSlopeArea() const;
    SlopeAreaResult CalculateSlopeArea(const std::vector<Point3D>& polygon) const;
    CutFillResult CalculateCutFill(const DTM& otherDTM) const;
    CutFillResult CalculateCutFill(const DTM& otherDTM, const std::vector<Point3D>& polygon) const;
    double CalculateVolume(double elevation) const;
    double CalculateVolume(double elevation, const std::vector<Point3D>& polygon) const;
    
    //=======================================================================================
    // Boundary operations
    //=======================================================================================
    std::vector<Point3D> GetBoundary() const;
    std::vector<std::vector<Point3D>> GetBoundaries() const; // For multiple boundaries (holes)
    bool RemoveHull();
    
    //=======================================================================================
    // File I/O operations
    //=======================================================================================
    bool Save(const std::string& fileName) const;
    bool SaveAsTinFile(const std::string& fileName) const;
    bool SaveAsGeopakTinFile(const std::string& fileName) const;
    bool Load(const std::string& fileName);
    bool LoadFromTinFile(const std::string& fileName);
    bool LoadFromGeopakTinFile(const std::string& fileName);
    
    //=======================================================================================
    // Properties and statistics
    //=======================================================================================
    bool IsTriangulated() const { return m_isTriangulated; }
    size_t GetVerticesCount() const { return m_vertices.size(); }
    size_t GetTrianglesCount() const { return m_triangles.size(); }
    size_t GetEdgesCount() const { return m_edges.size(); }
    size_t GetFeaturesCount() const { return m_features.size(); }
    
    const Range3D& GetBounds() const { return m_bounds; }
    DTMStatistics GetStatistics() const;
    DTMStatistics CalculateFeatureStatistics() const;
    
    //=======================================================================================
    // Configuration
    //=======================================================================================
    void SetTriangulationParameters(const TriangulationParameters& params);
    const TriangulationParameters& GetTriangulationParameters() const { return m_triangulationParams; }
    
    //=======================================================================================
    // Utility operations
    //=======================================================================================
    void JoinFeatures(DTMFeatureType featureType, double tolerance);
    void OptimizeMemory();
    void Clear();
    DTMPtr Clone() const;
    
    //=======================================================================================
    // Validation
    //=======================================================================================
    bool Validate() const;
    std::vector<std::string> GetValidationErrors() const;

private:
    //=======================================================================================
    // Internal helper methods
    //=======================================================================================
    void Initialize();
    void UpdateBounds();
    void UpdateStatistics();
    void BuildSpatialIndex();
    void ClearSpatialIndex();
    
    // Triangulation helpers
    TriangulationResult PerformDelaunayTriangulation();
    void ConstrainEdges();
    void OptimizeTriangulation();
    void RemoveSliverTriangles();
    
    // Feature processing helpers
    void ProcessLinearFeature(DTMFeaturePtr feature);
    void ProcessPointFeature(DTMFeaturePtr feature);
    
    // Geometric helpers
    bool IsPointInTriangle(const Point3D& point, const DTMTriangle& triangle) const;
    double CalculateTriangleArea(const DTMTriangle& triangle) const;
    Point3D CalculateTriangleCentroid(const DTMTriangle& triangle) const;
    Point3D InterpolateElevation(const Point3D& point, const DTMTriangle& triangle) const;
    
    // ID management
    DTMVertexId GenerateVertexId() { return ++m_nextVertexId; }
    DTMTriangleId GenerateTriangleId() { return ++m_nextTriangleId; }
    DTMEdgeId GenerateEdgeId() { return ++m_nextEdgeId; }
    DTMFeatureId GenerateFeatureId() { return ++m_nextFeatureId; }
};

} // namespace TerrainModel
