/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include "DTMTriangle.h"
#include <unordered_set>
#include <queue>

namespace TerrainModel {

//=======================================================================================
// Triangulation engine for Delaunay triangulation with constraints
//=======================================================================================
class TriangulationEngine {
public:
    //=======================================================================================
    // Triangulation configuration
    //=======================================================================================
    struct Configuration {
        double pointTolerance = Constants::DEFAULT_POINT_TOLERANCE;
        double lineTolerance = Constants::DEFAULT_LINE_TOLERANCE;
        DTMEdgeOption edgeOption = DTMEdgeOption::RemoveSliver;
        double maxSide = Constants::DEFAULT_MAX_SIDE;
        bool useConstraints = true;
        bool optimizeTriangulation = true;
        bool removeHull = false;
        bool parallelProcessing = true;
        int maxThreads = 0; // 0 = auto-detect
        
        Configuration() = default;
        Configuration(const TriangulationParameters& params)
            : pointTolerance(params.pointTolerance)
            , lineTolerance(params.lineTolerance)
            , edgeOption(params.edgeOption)
            , maxSide(params.maxSide)
            , useConstraints(params.useConstraints)
            , optimizeTriangulation(params.optimizeTriangulation) {}
    };

private:
    Configuration m_config;
    
    // Triangulation data structures
    std::unordered_map<DTMVertexId, DTMVertexPtr> m_vertices;
    std::unordered_map<DTMTriangleId, DTMTrianglePtr> m_triangles;
    std::unordered_map<DTMEdgeId, DTMEdgePtr> m_edges;
    
    // Constraint handling
    std::vector<std::vector<DTMVertexId>> m_constraintSegments;
    std::unordered_set<DTMEdgeId> m_constrainedEdges;
    
    // Spatial indexing for performance
    std::unique_ptr<class SpatialIndex> m_spatialIndex;
    
    // ID generators
    DTMVertexId m_nextVertexId;
    DTMTriangleId m_nextTriangleId;
    DTMEdgeId m_nextEdgeId;
    
    // Statistics and progress
    mutable TriangulationResult m_lastResult;
    ProgressCallback m_progressCallback;

public:
    //=======================================================================================
    // Constructor and Destructor
    //=======================================================================================
    explicit TriangulationEngine(const Configuration& config = Configuration());
    ~TriangulationEngine();
    
    // Non-copyable
    TriangulationEngine(const TriangulationEngine&) = delete;
    TriangulationEngine& operator=(const TriangulationEngine&) = delete;

    //=======================================================================================
    // Configuration
    //=======================================================================================
    void SetConfiguration(const Configuration& config);
    const Configuration& GetConfiguration() const { return m_config; }
    void SetProgressCallback(ProgressCallback callback) { m_progressCallback = callback; }

    //=======================================================================================
    // Main triangulation operations
    //=======================================================================================
    TriangulationResult Triangulate(const std::vector<Point3D>& points);
    TriangulationResult TriangulateWithConstraints(const std::vector<Point3D>& points, 
                                                  const std::vector<std::vector<int>>& constraints);
    TriangulationResult TriangulateIncremental(const std::vector<Point3D>& newPoints);
    
    //=======================================================================================
    // Constraint management
    //=======================================================================================
    void AddConstraint(const std::vector<DTMVertexId>& vertexIds);
    void AddConstraints(const std::vector<std::vector<DTMVertexId>>& constraints);
    void ClearConstraints();
    const std::vector<std::vector<DTMVertexId>>& GetConstraints() const { return m_constraintSegments; }
    
    //=======================================================================================
    // Result access
    //=======================================================================================
    const std::unordered_map<DTMVertexId, DTMVertexPtr>& GetVertices() const { return m_vertices; }
    const std::unordered_map<DTMTriangleId, DTMTrianglePtr>& GetTriangles() const { return m_triangles; }
    const std::unordered_map<DTMEdgeId, DTMEdgePtr>& GetEdges() const { return m_edges; }
    const TriangulationResult& GetLastResult() const { return m_lastResult; }
    
    //=======================================================================================
    // Validation and optimization
    //=======================================================================================
    bool ValidateTriangulation() const;
    TriangulationResult OptimizeTriangulation();
    TriangulationResult RemoveSliverTriangles();
    std::vector<std::string> GetValidationErrors() const;
    
    //=======================================================================================
    // Utility operations
    //=======================================================================================
    void Clear();
    size_t GetMemoryUsage() const;
    void GetStatistics(std::map<std::string, double>& stats) const;

private:
    //=======================================================================================
    // Core triangulation algorithms
    //=======================================================================================
    TriangulationResult PerformDelaunayTriangulation(const std::vector<Point3D>& points);
    TriangulationResult PerformIncrementalTriangulation(const std::vector<Point3D>& points);
    TriangulationResult PerformDivideAndConquerTriangulation(const std::vector<Point3D>& points);
    
    //=======================================================================================
    // Incremental insertion
    //=======================================================================================
    DTMVertexId InsertVertex(const Point3D& point);
    void InsertVertexInTriangle(DTMVertexId vertexId, DTMTriangleId triangleId);
    void InsertVertexOnEdge(DTMVertexId vertexId, DTMEdgeId edgeId);
    void LegalizeEdge(DTMVertexId vertexId, DTMEdgeId edgeId);
    
    //=======================================================================================
    // Constraint enforcement
    //=======================================================================================
    void EnforceConstraints();
    void EnforceConstraint(const std::vector<DTMVertexId>& constraint);
    std::vector<DTMEdgeId> FindIntersectingEdges(DTMVertexId startVertex, DTMVertexId endVertex);
    void RecoverConstraintEdge(DTMVertexId startVertex, DTMVertexId endVertex);
    
    //=======================================================================================
    // Edge operations
    //=======================================================================================
    DTMEdgeId CreateEdge(DTMVertexId vertex1Id, DTMVertexId vertex2Id);
    void DeleteEdge(DTMEdgeId edgeId);
    bool FlipEdge(DTMEdgeId edgeId);
    bool IsFlipLegal(DTMEdgeId edgeId) const;
    bool IsEdgeConstrained(DTMEdgeId edgeId) const;
    
    //=======================================================================================
    // Triangle operations
    //=======================================================================================
    DTMTriangleId CreateTriangle(DTMVertexId v1, DTMVertexId v2, DTMVertexId v3);
    void DeleteTriangle(DTMTriangleId triangleId);
    DTMTriangleId FindTriangleContaining(const Point3D& point) const;
    std::vector<DTMTriangleId> FindTrianglesContaining(const Point3D& point) const;
    
    //=======================================================================================
    // Geometric predicates and utilities
    //=======================================================================================
    bool IsPointInCircumcircle(const Point3D& point, DTMTriangleId triangleId) const;
    bool IsTriangleValid(DTMTriangleId triangleId) const;
    bool IsTriangleSliver(DTMTriangleId triangleId) const;
    double CalculateTriangleQuality(DTMTriangleId triangleId) const;
    
    //=======================================================================================
    // Hull management
    //=======================================================================================
    std::vector<DTMTriangleId> FindHullTriangles() const;
    void RemoveHullTriangles();
    std::vector<DTMEdgeId> FindBoundaryEdges() const;
    
    //=======================================================================================
    // Optimization algorithms
    //=======================================================================================
    void OptimizeDelaunayProperty();
    void OptimizeTriangleQuality();
    void SmoothVertexPositions();
    void RemoveDegenerateTriangles();
    
    //=======================================================================================
    // Parallel processing support
    //=======================================================================================
    void ParallelTriangulate(const std::vector<Point3D>& points);
    void MergeTriangulations(const std::vector<TriangulationEngine>& subTriangulations);
    
    //=======================================================================================
    // Memory management
    //=======================================================================================
    void OptimizeMemoryLayout();
    void CompactDataStructures();
    
    //=======================================================================================
    // Progress reporting
    //=======================================================================================
    bool ReportProgress(double progress, const std::string& message) const;
    void UpdateProgress(const std::string& phase, size_t current, size_t total) const;
    
    //=======================================================================================
    // ID management
    //=======================================================================================
    DTMVertexId GenerateVertexId() { return ++m_nextVertexId; }
    DTMTriangleId GenerateTriangleId() { return ++m_nextTriangleId; }
    DTMEdgeId GenerateEdgeId() { return ++m_nextEdgeId; }
    
    //=======================================================================================
    // Spatial indexing
    //=======================================================================================
    void BuildSpatialIndex();
    void UpdateSpatialIndex();
    void ClearSpatialIndex();
};

//=======================================================================================
// Triangulation quality analyzer
//=======================================================================================
class TriangulationQualityAnalyzer {
public:
    struct QualityMetrics {
        double averageAspectRatio = 0.0;
        double worstAspectRatio = 0.0;
        double averageMinAngle = 0.0;
        double worstMinAngle = 0.0;
        double averageMaxAngle = 0.0;
        double worstMaxAngle = 0.0;
        size_t sliverTriangles = 0;
        size_t degenerateTriangles = 0;
        double qualityScore = 0.0; // 0-1, higher is better
        
        std::vector<DTMTriangleId> poorQualityTriangles;
        std::vector<DTMTriangleId> sliverTriangleIds;
        std::vector<DTMTriangleId> degenerateTriangleIds;
    };

    static QualityMetrics AnalyzeTriangulation(const std::unordered_map<DTMTriangleId, DTMTrianglePtr>& triangles,
                                              const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices);
    
    static double CalculateTriangleQuality(const DTMTriangle& triangle, 
                                          const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices);
    
    static bool IsTriangleSliver(const DTMTriangle& triangle, 
                                const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices, 
                                double threshold = 0.1);
    
    static std::vector<DTMTriangleId> FindPoorQualityTriangles(const std::unordered_map<DTMTriangleId, DTMTrianglePtr>& triangles,
                                                              const std::unordered_map<DTMVertexId, DTMVertexPtr>& vertices,
                                                              double qualityThreshold = 0.3);
};

//=======================================================================================
// Triangulation algorithm selector
//=======================================================================================
class TriangulationAlgorithmSelector {
public:
    enum class Algorithm {
        Incremental,
        DivideAndConquer,
        Sweepline,
        Adaptive
    };
    
    static Algorithm SelectOptimalAlgorithm(size_t pointCount, bool hasConstraints, const Range3D& bounds);
    static std::string GetAlgorithmName(Algorithm algorithm);
    static Algorithm ParseAlgorithmName(const std::string& name);
};

} // namespace TerrainModel
