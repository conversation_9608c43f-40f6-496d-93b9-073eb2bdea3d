/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Native DTM Mesh wrapper class
//=======================================================================================
class NativeDTMMesh : public TerrainModelObjectWrap<NativeDTMMesh>
{
private:
    DTMMeshPtr m_nativeMesh;
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;

public:
    // Constructor
    NativeDTMMesh(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMMesh();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetVertexCount(const Napi::CallbackInfo& info);
    Napi::Value GetTriangleCount(const Napi::CallbackInfo& info);
    Napi::Value GetRange(const Napi::CallbackInfo& info);

    // Instance methods - Mesh data access
    Napi::Value GetVertices(const Napi::CallbackInfo& info);
    Napi::Value GetTriangles(const Napi::CallbackInfo& info);
    Napi::Value GetVertex(const Napi::CallbackInfo& info);
    Napi::Value GetTriangle(const Napi::CallbackInfo& info);

    // Instance methods - Mesh operations
    Napi::Value GenerateMesh(const Napi::CallbackInfo& info);
    Napi::Value SimplifyMesh(const Napi::CallbackInfo& info);
    Napi::Value ExportToPolyface(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    DTMMeshPtr GetHandle() const { return m_nativeMesh; }
    bool IsValidMesh() const { return m_nativeMesh.IsValid() && !m_disposed; }

    // Factory method for creating from DTM
    static Napi::Object CreateFromDTM(Napi::Env env, NativeDTM* dtm);

private:
    // Helper methods
    void InitializeFromDTM(NativeDTM* dtm);
    std::vector<Point3D> ConvertVerticesFromNative();
    std::vector<std::array<int, 3>> ConvertTrianglesFromNative();
};

//=======================================================================================
// DTM Mesh Enumerator wrapper class
//=======================================================================================
class NativeDTMMeshEnumerator : public TerrainModelObjectWrap<NativeDTMMeshEnumerator>
{
private:
    DTMMeshEnumeratorPtr m_nativeEnumerator;
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;

public:
    // Constructor
    NativeDTMMeshEnumerator(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMMeshEnumerator();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetUsePolyfaceHeader(const Napi::CallbackInfo& info);
    void SetUsePolyfaceHeader(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods - Enumeration
    Napi::Value MoveNext(const Napi::CallbackInfo& info);
    Napi::Value Current(const Napi::CallbackInfo& info);
    Napi::Value Reset(const Napi::CallbackInfo& info);
    Napi::Value GetCurrentTriangle(const Napi::CallbackInfo& info);
    Napi::Value GetCurrentVertices(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    DTMMeshEnumeratorPtr GetHandle() const { return m_nativeEnumerator; }
    bool IsValidEnumerator() const { return m_nativeEnumerator.IsValid() && !m_disposed; }

private:
    // Helper methods
    void InitializeEnumerator(NativeDTM* dtm);
    Napi::Object CreateTriangleFromCurrent(Napi::Env env);
};

} // namespace TerrainModelNodeAddon
