/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// DTM Fence Parameters structure
//=======================================================================================
struct DTMFenceParams {
    std::vector<Point3D> points;
    DTMFenceType fenceType;
    DTMFenceOption fenceOption;
    
    DTMFenceParams() : fenceType(DTMFenceType::Shape), fenceOption(DTMFenceOption::Internal) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static DTMFenceParams FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Visibility Analysis Result structure
//=======================================================================================
struct VisibilityAnalysisResult {
    VisibilityType visibility;
    std::vector<Point3D> visiblePoints;
    std::vector<Point3D> hiddenPoints;
    std::vector<Point3D> partiallyVisiblePoints;
    double visibilityPercentage;
    
    VisibilityAnalysisResult() : visibility(VisibilityType::Invisible), visibilityPercentage(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static VisibilityAnalysisResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// View Shed Analysis Result structure
//=======================================================================================
struct ViewShedAnalysisResult {
    Point3D eyePoint;
    std::vector<Point3D> visibleArea;
    std::vector<Point3D> shadowArea;
    double totalVisibleArea;
    double totalShadowArea;
    double visibilityRatio;
    
    ViewShedAnalysisResult() : totalVisibleArea(0.0), totalShadowArea(0.0), visibilityRatio(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static ViewShedAnalysisResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Drainage Analysis Result structure
//=======================================================================================
struct DrainageAnalysisResult {
    DTMFeatureType featureType;
    std::vector<Point3D> drainageFeatures;
    std::vector<Point3D> lowPoints;
    double minLowPoint;
    DTMFenceParams fence;
    
    DrainageAnalysisResult() : featureType(DTMFeatureType::Point), minLowPoint(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static DrainageAnalysisResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Native DTM Caching wrapper class for advanced analysis and caching operations
//=======================================================================================
class NativeDTMCaching : public TerrainModelObjectWrap<NativeDTMCaching>
{
private:
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;
    std::map<std::string, std::vector<Point3D>> m_featureCache;
    std::map<std::string, VisibilityAnalysisResult> m_visibilityCache;
    std::map<std::string, ViewShedAnalysisResult> m_viewShedCache;
    std::map<std::string, DrainageAnalysisResult> m_drainageCache;
    bool m_cachingEnabled;

public:
    // Constructor
    NativeDTMCaching(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMCaching();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetCachingEnabled(const Napi::CallbackInfo& info);
    void SetCachingEnabled(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetCacheSize(const Napi::CallbackInfo& info);

    // Instance methods - Cache management
    Napi::Value EmptyCache(const Napi::CallbackInfo& info);
    Napi::Value ClearFeatureCache(const Napi::CallbackInfo& info);
    Napi::Value ClearVisibilityCache(const Napi::CallbackInfo& info);
    Napi::Value ClearViewShedCache(const Napi::CallbackInfo& info);
    Napi::Value ClearDrainageCache(const Napi::CallbackInfo& info);

    // Instance methods - Feature browsing and caching
    Napi::Value BrowseFeatures(const Napi::CallbackInfo& info);
    Napi::Value BrowseFeaturesWithCallback(const Napi::CallbackInfo& info);
    Napi::Value AddFeature(const Napi::CallbackInfo& info);
    Napi::Value GetCachedFeatures(const Napi::CallbackInfo& info);

    // Instance methods - Visibility analysis
    Napi::Value BrowseTinPointsVisibility(const Napi::CallbackInfo& info);
    Napi::Value BrowseTinLinesVisibility(const Napi::CallbackInfo& info);
    Napi::Value AnalyzePointVisibility(const Napi::CallbackInfo& info);
    Napi::Value AnalyzeLineVisibility(const Napi::CallbackInfo& info);
    Napi::Value GetVisibilityAnalysisResult(const Napi::CallbackInfo& info);

    // Instance methods - View shed analysis
    Napi::Value BrowseRadialViewSheds(const Napi::CallbackInfo& info);
    Napi::Value BrowseRegionViewSheds(const Napi::CallbackInfo& info);
    Napi::Value CalculateViewShed(const Napi::CallbackInfo& info);
    Napi::Value CalculateRadialViewShed(const Napi::CallbackInfo& info);
    Napi::Value GetViewShedAnalysisResult(const Napi::CallbackInfo& info);

    // Instance methods - Drainage analysis
    Napi::Value BrowseDrainageFeatures(const Napi::CallbackInfo& info);
    Napi::Value AnalyzeDrainage(const Napi::CallbackInfo& info);
    Napi::Value FindLowPoints(const Napi::CallbackInfo& info);
    Napi::Value TraceDrainagePath(const Napi::CallbackInfo& info);
    Napi::Value GetDrainageAnalysisResult(const Napi::CallbackInfo& info);

    // Instance methods - Advanced analysis
    Napi::Value PerformSightlineAnalysis(const Napi::CallbackInfo& info);
    Napi::Value CalculateIntervisibility(const Napi::CallbackInfo& info);
    Napi::Value AnalyzeTerrainProfile(const Napi::CallbackInfo& info);
    Napi::Value FindOptimalViewpoints(const Napi::CallbackInfo& info);

    // Instance methods - Callback management
    Napi::Value SetFeatureBrowsingCallback(const Napi::CallbackInfo& info);
    Napi::Value SetVisibilityAnalysisCallback(const Napi::CallbackInfo& info);
    Napi::Value SetDrainageAnalysisCallback(const Napi::CallbackInfo& info);
    Napi::Value FireCallback(const Napi::CallbackInfo& info);

    // Instance methods - Performance and optimization
    Napi::Value OptimizeCache(const Napi::CallbackInfo& info);
    Napi::Value GetCacheStatistics(const Napi::CallbackInfo& info);
    Napi::Value PreloadAnalysisData(const Napi::CallbackInfo& info);

    // Instance methods - Utility
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    bool IsValidCaching() const { return m_dtm != nullptr && !m_disposed; }

    // Factory method for creating from DTM
    static Napi::Object CreateFromDTM(Napi::Env env, NativeDTM* dtm);

private:
    // Helper methods
    void InitializeFromDTM(NativeDTM* dtm);
    std::string GenerateCacheKey(const std::string& operation, const std::vector<std::string>& parameters);
    bool IsCacheValid(const std::string& key);
    void InvalidateRelatedCache(const std::string& operation);
    
    // Visibility analysis helpers
    VisibilityAnalysisResult PerformVisibilityAnalysis(const Point3D& eyePoint, const std::vector<Point3D>& targetPoints);
    ViewShedAnalysisResult PerformViewShedAnalysis(const Point3D& eyePoint, double maxDistance, int numRadials);
    DrainageAnalysisResult PerformDrainageAnalysis(DTMFeatureType featureType, const DTMFenceParams& fence);
    
    // Cache management helpers
    void AddToFeatureCache(const std::string& key, const std::vector<Point3D>& features);
    void AddToVisibilityCache(const std::string& key, const VisibilityAnalysisResult& result);
    void AddToViewShedCache(const std::string& key, const ViewShedAnalysisResult& result);
    void AddToDrainageCache(const std::string& key, const DrainageAnalysisResult& result);
    
    // Callback management
    Napi::FunctionReference m_featureBrowsingCallback;
    Napi::FunctionReference m_visibilityAnalysisCallback;
    Napi::FunctionReference m_drainageAnalysisCallback;
};

//=======================================================================================
// Cache Statistics structure
//=======================================================================================
struct CacheStatistics {
    size_t featureCacheSize;
    size_t visibilityCacheSize;
    size_t viewShedCacheSize;
    size_t drainageCacheSize;
    size_t totalMemoryUsage;
    double hitRatio;
    int totalRequests;
    int cacheHits;
    int cacheMisses;
    
    CacheStatistics() 
        : featureCacheSize(0), visibilityCacheSize(0), viewShedCacheSize(0), drainageCacheSize(0),
          totalMemoryUsage(0), hitRatio(0.0), totalRequests(0), cacheHits(0), cacheMisses(0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static CacheStatistics FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Sightline Analysis Result structure
//=======================================================================================
struct SightlineAnalysisResult {
    Point3D startPoint;
    Point3D endPoint;
    std::vector<Point3D> obstructionPoints;
    bool isVisible;
    double clearanceHeight;
    double totalDistance;
    std::vector<Point3D> profilePoints;
    
    SightlineAnalysisResult() : isVisible(false), clearanceHeight(0.0), totalDistance(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static SightlineAnalysisResult FromJavaScript(Napi::Object obj);
};

//=======================================================================================
// Intervisibility Analysis Result structure
//=======================================================================================
struct IntervisibilityAnalysisResult {
    std::vector<Point3D> viewpoints;
    std::vector<std::vector<bool>> visibilityMatrix;
    std::vector<double> visibilityScores;
    Point3D optimalViewpoint;
    double maxVisibilityScore;
    
    IntervisibilityAnalysisResult() : maxVisibilityScore(0.0) {}
    
    Napi::Object ToJavaScript(Napi::Env env) const;
    static IntervisibilityAnalysisResult FromJavaScript(Napi::Object obj);
};

} // namespace TerrainModelNodeAddon
