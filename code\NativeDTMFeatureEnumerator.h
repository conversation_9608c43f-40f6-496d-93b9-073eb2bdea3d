/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"
#include "NativeDTM.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Native DTM Feature Enumerator wrapper class
//=======================================================================================
class NativeDTMFeatureEnumerator : public TerrainModelObjectWrap<NativeDTMFeatureEnumerator>
{
private:
    DTMFeatureEnumeratorPtr m_nativeEnumerator;
    NativeDTM* m_dtm;
    Napi::ObjectReference m_dtmRef;

public:
    // Constructor
    NativeDTMFeatureEnumerator(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTMFeatureEnumerator();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Property accessors
    Napi::Value GetSort(const Napi::CallbackInfo& info);
    void SetSort(const Napi::CallbackInfo& info, const Napi::Value& value);
    Napi::Value GetReadSourceFeatures(const Napi::CallbackInfo& info);
    void SetReadSourceFeatures(const Napi::CallbackInfo& info, const Napi::Value& value);

    // Instance methods - Feature filtering
    Napi::Value IncludeAllFeatures(const Napi::CallbackInfo& info);
    Napi::Value ExcludeAllFeatures(const Napi::CallbackInfo& info);
    Napi::Value IncludeFeature(const Napi::CallbackInfo& info);
    Napi::Value ExcludeFeature(const Napi::CallbackInfo& info);
    Napi::Value SetUserTagRange(const Napi::CallbackInfo& info);

    // Instance methods - Enumeration
    Napi::Value MoveNext(const Napi::CallbackInfo& info);
    Napi::Value Current(const Napi::CallbackInfo& info);
    Napi::Value Reset(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Internal helper methods
    DTMFeatureEnumeratorPtr GetHandle() const { return m_nativeEnumerator; }
    bool IsValidEnumerator() const { return m_nativeEnumerator.IsValid() && !m_disposed; }

private:
    // Helper methods
    void InitializeEnumerator(NativeDTM* dtm);
    Napi::Object CreateFeatureInfoFromCurrent(Napi::Env env);
};

} // namespace TerrainModelNodeAddon
