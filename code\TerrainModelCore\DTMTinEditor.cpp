/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "DTMTinEditor.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <stack>

namespace TerrainModel {

//=======================================================================================
// DTMEditResult implementation
//=======================================================================================
DTMEditResult DTMEditResult::Success(const std::string& message) {
    DTMEditResult result;
    result.success = true;
    result.message = message;
    return result;
}

DTMEditResult DTMEditResult::Failure(const std::string& message) {
    DTMEditResult result;
    result.success = false;
    result.message = message;
    return result;
}

//=======================================================================================
// DTMTinEditor implementation
//=======================================================================================
DTMTinEditor::DTMTinEditor(DTMPtr dtm)
    : m_dtm(dtm)
    , m_isEditing(false)
    , m_maxUndoLevels(50)
{
    if (!m_dtm) {
        throw std::invalid_argument("DTM cannot be null");
    }
}

DTMTinEditor::~DTMTinEditor() {
    if (m_isEditing) {
        CancelEdit();
    }
}

//=======================================================================================
// Edit session management
//=======================================================================================
bool DTMTinEditor::StartEdit() {
    if (m_isEditing) {
        return false; // Already editing
    }
    
    if (!m_dtm->IsTriangulated()) {
        return false; // DTM must be triangulated
    }
    
    // Create initial state snapshot
    CreateUndoSnapshot("Start Edit");
    
    m_isEditing = true;
    return true;
}

bool DTMTinEditor::EndEdit() {
    if (!m_isEditing) {
        return false;
    }
    
    // Validate the triangulation
    if (!ValidateTriangulation()) {
        // Attempt to repair
        RepairTriangulation();
    }
    
    // Clear undo history to save memory
    m_undoStack.clear();
    m_redoStack.clear();
    
    m_isEditing = false;
    return true;
}

bool DTMTinEditor::CancelEdit() {
    if (!m_isEditing) {
        return false;
    }
    
    // Restore to initial state
    if (!m_undoStack.empty()) {
        // Undo all changes
        while (m_undoStack.size() > 1) {
            UndoInternal();
        }
        UndoInternal(); // Undo the initial snapshot
    }
    
    m_undoStack.clear();
    m_redoStack.clear();
    m_isEditing = false;
    return true;
}

//=======================================================================================
// Feature point management
//=======================================================================================
void DTMTinEditor::SetFeaturePoints(const std::vector<Point3D>& points) {
    if (!m_isEditing) {
        throw std::runtime_error("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Set Feature Points");
    
    m_featurePoints = points;
    
    // TODO: Update triangulation to respect feature points
    // This would involve constraining edges between consecutive feature points
}

void DTMTinEditor::AddFeaturePoint(const Point3D& point) {
    if (!m_isEditing) {
        throw std::runtime_error("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Add Feature Point");
    
    m_featurePoints.push_back(point);
    
    // Insert the point into the triangulation
    // TODO: Implement point insertion with proper triangulation update
}

void DTMTinEditor::InsertFeaturePoint(size_t index, const Point3D& point) {
    if (!m_isEditing) {
        throw std::runtime_error("Must be in edit mode");
    }
    
    if (index > m_featurePoints.size()) {
        throw std::out_of_range("Index out of range");
    }
    
    CreateUndoSnapshot("Insert Feature Point");
    
    m_featurePoints.insert(m_featurePoints.begin() + index, point);
}

void DTMTinEditor::RemoveFeaturePoint(size_t index) {
    if (!m_isEditing) {
        throw std::runtime_error("Must be in edit mode");
    }
    
    if (index >= m_featurePoints.size()) {
        throw std::out_of_range("Index out of range");
    }
    
    CreateUndoSnapshot("Remove Feature Point");
    
    m_featurePoints.erase(m_featurePoints.begin() + index);
}

void DTMTinEditor::ClearFeaturePoints() {
    if (!m_isEditing) {
        throw std::runtime_error("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Clear Feature Points");
    
    m_featurePoints.clear();
}

//=======================================================================================
// Triangle operations
//=======================================================================================
DTMEditResult DTMTinEditor::FlipEdge(DTMEdgeId edgeId) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    // Get the edge
    const auto& edges = m_dtm->GetEdges();
    auto edgeIt = edges.find(edgeId);
    if (edgeIt == edges.end()) {
        return DTMEditResult::Failure("Edge not found");
    }
    
    const auto& edge = edgeIt->second;
    
    // Check if edge can be flipped
    if (edge->IsBoundary()) {
        return DTMEditResult::Failure("Cannot flip boundary edge");
    }
    
    if (edge->IsConstrained()) {
        return DTMEditResult::Failure("Cannot flip constrained edge");
    }
    
    DTMTriangleId leftTriangle = edge->GetLeftTriangleId();
    DTMTriangleId rightTriangle = edge->GetRightTriangleId();
    
    if (leftTriangle == Constants::INVALID_ID || rightTriangle == Constants::INVALID_ID) {
        return DTMEditResult::Failure("Edge is not shared by two triangles");
    }
    
    CreateUndoSnapshot("Flip Edge");
    
    // Perform the edge flip
    DTMEditResult result = PerformEdgeFlip(edgeId, leftTriangle, rightTriangle);
    
    if (result.success) {
        result.affectedEdges = 1;
        result.affectedTriangles = 2;
        result.modifiedEdges.push_back(edgeId);
        result.modifiedTriangles.push_back(leftTriangle);
        result.modifiedTriangles.push_back(rightTriangle);
    }
    
    return result;
}

DTMEditResult DTMTinEditor::SplitTriangle(DTMTriangleId triangleId, const Point3D& point) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    // Get the triangle
    const auto& triangles = m_dtm->GetTriangles();
    auto triangleIt = triangles.find(triangleId);
    if (triangleIt == triangles.end()) {
        return DTMEditResult::Failure("Triangle not found");
    }
    
    const auto& triangle = triangleIt->second;
    
    // Check if point is inside triangle
    const auto& vertices = m_dtm->GetVertices();
    if (!triangle->ContainsPoint(point, vertices)) {
        return DTMEditResult::Failure("Point is not inside triangle");
    }
    
    CreateUndoSnapshot("Split Triangle");
    
    // Perform the triangle split
    DTMEditResult result = PerformTriangleSplit(triangleId, point);
    
    return result;
}

DTMEditResult DTMTinEditor::MergeTriangles(DTMTriangleId triangle1Id, DTMTriangleId triangle2Id) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    // Check if triangles are adjacent
    DTMEdgeId sharedEdge = FindSharedEdge(triangle1Id, triangle2Id);
    if (sharedEdge == Constants::INVALID_ID) {
        return DTMEditResult::Failure("Triangles are not adjacent");
    }
    
    CreateUndoSnapshot("Merge Triangles");
    
    // Perform the merge by removing the shared edge
    DTMEditResult result = PerformTriangleMerge(triangle1Id, triangle2Id, sharedEdge);
    
    return result;
}

DTMEditResult DTMTinEditor::DeleteTriangle(DTMTriangleId triangleId) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Delete Triangle");
    
    // TODO: Implement triangle deletion with proper hole handling
    DTMEditResult result;
    result.success = false;
    result.message = "Triangle deletion not yet implemented";
    
    return result;
}

//=======================================================================================
// Vertex operations
//=======================================================================================
DTMEditResult DTMTinEditor::MoveVertex(DTMVertexId vertexId, const Point3D& newPosition) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    // Get the vertex
    const auto& vertices = m_dtm->GetVertices();
    auto vertexIt = vertices.find(vertexId);
    if (vertexIt == vertices.end()) {
        return DTMEditResult::Failure("Vertex not found");
    }
    
    CreateUndoSnapshot("Move Vertex");
    
    // Store old position for validation
    Point3D oldPosition = vertexIt->second->GetPosition();
    
    // Update vertex position
    vertexIt->second->SetPosition(newPosition);
    
    // Validate that the move doesn't create invalid triangles
    std::vector<DTMTriangleId> affectedTriangles = GetAdjacentTriangles(vertexId);
    
    for (DTMTriangleId triangleId : affectedTriangles) {
        const auto& triangles = m_dtm->GetTriangles();
        auto triangleIt = triangles.find(triangleId);
        if (triangleIt != triangles.end()) {
            if (triangleIt->second->IsDegenerate(vertices)) {
                // Revert the move
                vertexIt->second->SetPosition(oldPosition);
                return DTMEditResult::Failure("Move would create degenerate triangle");
            }
        }
    }
    
    DTMEditResult result = DTMEditResult::Success("Vertex moved successfully");
    result.affectedVertices = 1;
    result.affectedTriangles = affectedTriangles.size();
    result.modifiedVertices.push_back(vertexId);
    result.modifiedTriangles = affectedTriangles;
    
    return result;
}

DTMEditResult DTMTinEditor::DeleteVertex(DTMVertexId vertexId) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Delete Vertex");
    
    // TODO: Implement vertex deletion with proper retriangulation
    DTMEditResult result;
    result.success = false;
    result.message = "Vertex deletion not yet implemented";
    
    return result;
}

DTMEditResult DTMTinEditor::InsertVertex(const Point3D& position) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Insert Vertex");
    
    // TODO: Implement vertex insertion with proper triangulation update
    DTMEditResult result;
    result.success = false;
    result.message = "Vertex insertion not yet implemented";
    
    return result;
}

//=======================================================================================
// Edge operations
//=======================================================================================
DTMEditResult DTMTinEditor::SwapEdge(DTMEdgeId edgeId) {
    // SwapEdge is essentially the same as FlipEdge
    return FlipEdge(edgeId);
}

DTMEditResult DTMTinEditor::ConstrainEdge(DTMEdgeId edgeId) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    const auto& edges = m_dtm->GetEdges();
    auto edgeIt = edges.find(edgeId);
    if (edgeIt == edges.end()) {
        return DTMEditResult::Failure("Edge not found");
    }
    
    CreateUndoSnapshot("Constrain Edge");
    
    edgeIt->second->SetConstrained(true);
    
    DTMEditResult result = DTMEditResult::Success("Edge constrained");
    result.affectedEdges = 1;
    result.modifiedEdges.push_back(edgeId);
    
    return result;
}

DTMEditResult DTMTinEditor::UnconstrainEdge(DTMEdgeId edgeId) {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    const auto& edges = m_dtm->GetEdges();
    auto edgeIt = edges.find(edgeId);
    if (edgeIt == edges.end()) {
        return DTMEditResult::Failure("Edge not found");
    }
    
    CreateUndoSnapshot("Unconstrain Edge");
    
    edgeIt->second->SetConstrained(false);
    
    DTMEditResult result = DTMEditResult::Success("Edge unconstrained");
    result.affectedEdges = 1;
    result.modifiedEdges.push_back(edgeId);
    
    return result;
}

//=======================================================================================
// Validation and repair
//=======================================================================================
bool DTMTinEditor::ValidateTriangulation() const {
    if (!m_dtm->IsTriangulated()) {
        return false;
    }
    
    const auto& vertices = m_dtm->GetVertices();
    const auto& triangles = m_dtm->GetTriangles();
    const auto& edges = m_dtm->GetEdges();
    
    // Check vertex-triangle consistency
    for (const auto& pair : vertices) {
        const auto& vertex = pair.second;
        if (!vertex->IsActive()) continue;
        
        const auto& adjacentTriangles = vertex->GetAdjacentTriangles();
        for (DTMTriangleId triangleId : adjacentTriangles) {
            auto triangleIt = triangles.find(triangleId);
            if (triangleIt == triangles.end()) {
                return false; // Referenced triangle doesn't exist
            }
            
            if (!triangleIt->second->IsIncidentTo(pair.first)) {
                return false; // Triangle doesn't reference vertex
            }
        }
    }
    
    // Check triangle-edge consistency
    for (const auto& pair : triangles) {
        const auto& triangle = pair.second;
        if (!triangle->IsActive()) continue;
        
        for (int i = 0; i < 3; ++i) {
            DTMEdgeId edgeId = triangle->GetEdgeId(i);
            auto edgeIt = edges.find(edgeId);
            if (edgeIt == edges.end()) {
                return false; // Referenced edge doesn't exist
            }
            
            if (!edgeIt->second->IsIncidentTo(pair.first)) {
                return false; // Edge doesn't reference triangle
            }
        }
    }
    
    // Check for degenerate triangles
    for (const auto& pair : triangles) {
        if (pair.second->IsActive() && pair.second->IsDegenerate(vertices)) {
            return false;
        }
    }
    
    return true;
}

DTMEditResult DTMTinEditor::RepairTriangulation() {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Repair Triangulation");
    
    DTMEditResult result = DTMEditResult::Success("Triangulation repaired");
    
    // TODO: Implement triangulation repair algorithms
    // This could include:
    // - Removing degenerate triangles
    // - Fixing inconsistent adjacency relationships
    // - Retriangulating holes
    
    return result;
}

DTMEditResult DTMTinEditor::OptimizeTriangulation() {
    if (!m_isEditing) {
        return DTMEditResult::Failure("Must be in edit mode");
    }
    
    CreateUndoSnapshot("Optimize Triangulation");
    
    DTMEditResult result = DTMEditResult::Success("Triangulation optimized");
    
    // TODO: Implement triangulation optimization
    // This could include:
    // - Delaunay optimization through edge flipping
    // - Angle optimization
    // - Aspect ratio improvement
    
    return result;
}

//=======================================================================================
// Undo/Redo operations
//=======================================================================================
bool DTMTinEditor::CanUndo() const {
    return !m_undoStack.empty();
}

bool DTMTinEditor::CanRedo() const {
    return !m_redoStack.empty();
}

bool DTMTinEditor::Undo() {
    if (!m_isEditing || !CanUndo()) {
        return false;
    }
    
    return UndoInternal();
}

bool DTMTinEditor::Redo() {
    if (!m_isEditing || !CanRedo()) {
        return false;
    }
    
    return RedoInternal();
}

void DTMTinEditor::ClearUndoHistory() {
    m_undoStack.clear();
    m_redoStack.clear();
}

//=======================================================================================
// Internal helper methods
//=======================================================================================
void DTMTinEditor::CreateUndoSnapshot(const std::string& description) {
    if (!m_isEditing) {
        return;
    }
    
    // Create a snapshot of the current state
    DTMSnapshot snapshot;
    snapshot.description = description;
    snapshot.timestamp = std::chrono::system_clock::now();
    
    // TODO: Implement efficient state capture
    // For now, we'll just store the description
    // A full implementation would capture:
    // - All vertices, edges, and triangles
    // - Their states and relationships
    // - Feature points
    
    m_undoStack.push_back(snapshot);
    
    // Limit undo stack size
    if (m_undoStack.size() > m_maxUndoLevels) {
        m_undoStack.erase(m_undoStack.begin());
    }
    
    // Clear redo stack when new action is performed
    m_redoStack.clear();
}

bool DTMTinEditor::UndoInternal() {
    if (m_undoStack.empty()) {
        return false;
    }
    
    // Move current state to redo stack
    DTMSnapshot currentState;
    currentState.description = "Redo point";
    currentState.timestamp = std::chrono::system_clock::now();
    m_redoStack.push_back(currentState);
    
    // Restore previous state
    DTMSnapshot previousState = m_undoStack.back();
    m_undoStack.pop_back();
    
    // TODO: Implement state restoration
    
    return true;
}

bool DTMTinEditor::RedoInternal() {
    if (m_redoStack.empty()) {
        return false;
    }
    
    // Move current state to undo stack
    DTMSnapshot currentState;
    currentState.description = "Undo point";
    currentState.timestamp = std::chrono::system_clock::now();
    m_undoStack.push_back(currentState);
    
    // Restore redo state
    DTMSnapshot redoState = m_redoStack.back();
    m_redoStack.pop_back();
    
    // TODO: Implement state restoration
    
    return true;
}

DTMEditResult DTMTinEditor::PerformEdgeFlip(DTMEdgeId edgeId, DTMTriangleId leftTriangle, DTMTriangleId rightTriangle) {
    // TODO: Implement actual edge flip algorithm
    // This is a complex operation that involves:
    // 1. Getting the four vertices involved
    // 2. Removing the two old triangles
    // 3. Creating two new triangles with the flipped edge
    // 4. Updating all adjacency relationships
    
    DTMEditResult result;
    result.success = false;
    result.message = "Edge flip not yet fully implemented";
    
    return result;
}

DTMEditResult DTMTinEditor::PerformTriangleSplit(DTMTriangleId triangleId, const Point3D& point) {
    // TODO: Implement triangle split algorithm
    // This involves:
    // 1. Creating a new vertex at the point
    // 2. Removing the original triangle
    // 3. Creating three new triangles
    // 4. Updating adjacency relationships
    
    DTMEditResult result;
    result.success = false;
    result.message = "Triangle split not yet fully implemented";
    
    return result;
}

DTMEditResult DTMTinEditor::PerformTriangleMerge(DTMTriangleId triangle1Id, DTMTriangleId triangle2Id, DTMEdgeId sharedEdge) {
    // TODO: Implement triangle merge algorithm
    
    DTMEditResult result;
    result.success = false;
    result.message = "Triangle merge not yet fully implemented";
    
    return result;
}

DTMEdgeId DTMTinEditor::FindSharedEdge(DTMTriangleId triangle1Id, DTMTriangleId triangle2Id) const {
    const auto& triangles = m_dtm->GetTriangles();
    
    auto triangle1It = triangles.find(triangle1Id);
    auto triangle2It = triangles.find(triangle2Id);
    
    if (triangle1It == triangles.end() || triangle2It == triangles.end()) {
        return Constants::INVALID_ID;
    }
    
    const auto& triangle1 = triangle1It->second;
    const auto& triangle2 = triangle2It->second;
    
    // Check each edge of triangle1 to see if it's shared with triangle2
    for (int i = 0; i < 3; ++i) {
        DTMEdgeId edgeId = triangle1->GetEdgeId(i);
        if (triangle2->IsIncidentTo(edgeId)) {
            return edgeId;
        }
    }
    
    return Constants::INVALID_ID;
}

std::vector<DTMTriangleId> DTMTinEditor::GetAdjacentTriangles(DTMVertexId vertexId) const {
    const auto& vertices = m_dtm->GetVertices();
    auto vertexIt = vertices.find(vertexId);
    
    if (vertexIt == vertices.end()) {
        return {};
    }
    
    return vertexIt->second->GetAdjacentTriangles();
}

} // namespace TerrainModel
