/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelNodeAddon.h"

namespace TerrainModelNodeAddon {

//=======================================================================================
// Native DTM wrapper class that exposes DTM functionality to JavaScript
//=======================================================================================
class NativeDTM : public TerrainModelObjectWrap<NativeDTM>
{
private:
    BcDTMPtr m_nativeDtm;
    bool m_autoUpdateMemoryPressure;
    size_t m_memoryUsed;

public:
    // Constructor
    NativeDTM(const Napi::CallbackInfo& info);
    
    // Destructor
    ~NativeDTM();

    // Static initialization method for N-API
    static void Init(Napi::Env env, Napi::Object exports);

    // Static factory methods
    static Napi::Value CreateEmpty(const Napi::CallbackInfo& info);
    static Napi::Value CreateFromFile(const Napi::CallbackInfo& info);
    static Napi::Value CreateFromGeopakTinFile(const Napi::CallbackInfo& info);
    static Napi::Value CreateFromGeopakDatFile(const Napi::CallbackInfo& info);

    // Instance methods - DTM Creation and Management
    Napi::Value Triangulate(const Napi::CallbackInfo& info);
    Napi::Value CheckTriangulation(const Napi::CallbackInfo& info);
    Napi::Value SetTriangulationParameters(const Napi::CallbackInfo& info);
    Napi::Value GetTriangulationParameters(const Napi::CallbackInfo& info);
    
    // Instance methods - File Operations
    Napi::Value Save(const Napi::CallbackInfo& info);
    Napi::Value SaveAsGeopakTinFile(const Napi::CallbackInfo& info);
    Napi::Value PopulateFromGeopakTinFile(const Napi::CallbackInfo& info);

    // Instance methods - DTM Properties
    Napi::Value GetRange3d(const Napi::CallbackInfo& info);
    Napi::Value GetVerticesCount(const Napi::CallbackInfo& info);
    Napi::Value GetTrianglesCount(const Napi::CallbackInfo& info);
    Napi::Value GetBoundary(const Napi::CallbackInfo& info);
    Napi::Value CalculateFeatureStatistics(const Napi::CallbackInfo& info);

    // Instance methods - Feature Management
    Napi::Value AddPointFeature(const Napi::CallbackInfo& info);
    Napi::Value AddLinearFeature(const Napi::CallbackInfo& info);
    Napi::Value AddPoint(const Napi::CallbackInfo& info);
    Napi::Value AddPoints(const Napi::CallbackInfo& info);
    Napi::Value GetFeatureById(const Napi::CallbackInfo& info);
    Napi::Value DeleteFeatureById(const Napi::CallbackInfo& info);
    Napi::Value DeleteFeaturesByType(const Napi::CallbackInfo& info);
    Napi::Value DeleteFeaturesByUserTag(const Napi::CallbackInfo& info);

    // Instance methods - Draping
    Napi::Value DrapePoint(const Napi::CallbackInfo& info);
    Napi::Value DrapeLinearPoints(const Napi::CallbackInfo& info);

    // Instance methods - Analysis
    Napi::Value CalculateSlopeArea(const Napi::CallbackInfo& info);
    Napi::Value CalculateCutFill(const Napi::CallbackInfo& info);
    Napi::Value CalculateVolume(const Napi::CallbackInfo& info);

    // Instance methods - Utilities
    Napi::Value JoinFeatures(const Napi::CallbackInfo& info);
    Napi::Value RemoveHull(const Napi::CallbackInfo& info);
    Napi::Value Dispose(const Napi::CallbackInfo& info);

    // Property accessors
    Napi::Value GetExternalHandle(const Napi::CallbackInfo& info);
    Napi::Value GetIsTriangulated(const Napi::CallbackInfo& info);

    // Internal helper methods
    BcDTMPtr GetHandle() const { return m_nativeDtm; }
    bool IsValid() const { return m_nativeDtm.IsValid() && !m_disposed; }

private:
    // Helper methods
    void CheckMemoryPressure();
    void UpdateMemoryPressure(size_t newMemoryUsed);
    void CheckIsTriangulated(Napi::Env env);
    
    // Type conversion helpers
    static std::vector<Point3D> ConvertPointsFromJS(Napi::Array jsPoints);
    static Napi::Array ConvertPointsToJS(Napi::Env env, const std::vector<Point3D>& points);
    static DTMFeatureType ConvertFeatureTypeFromJS(Napi::Value jsFeatureType);
    static Napi::Value ConvertFeatureTypeToJS(Napi::Env env, DTMFeatureType featureType);
};

} // namespace TerrainModelNodeAddon
