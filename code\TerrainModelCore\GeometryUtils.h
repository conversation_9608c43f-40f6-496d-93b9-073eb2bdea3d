/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include <array>
#include <algorithm>

namespace TerrainModel {

//=======================================================================================
// Geometric utility functions and predicates
//=======================================================================================
class GeometryUtils {
public:
    //=======================================================================================
    // Point operations
    //=======================================================================================
    static double Distance2D(const Point3D& p1, const Point3D& p2);
    static double Distance3D(const Point3D& p1, const Point3D& p2);
    static double DistanceSquared2D(const Point3D& p1, const Point3D& p2);
    static double DistanceSquared3D(const Point3D& p1, const Point3D& p2);
    
    static Point3D Midpoint(const Point3D& p1, const Point3D& p2);
    static Point3D Interpolate(const Point3D& p1, const Point3D& p2, double t);
    static Point3D ProjectToLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static Point3D ProjectToPlane(const Point3D& point, const Point3D& planePoint, const Point3D& planeNormal);
    
    //=======================================================================================
    // Vector operations
    //=======================================================================================
    static Point3D CrossProduct(const Point3D& v1, const Point3D& v2);
    static double DotProduct(const Point3D& v1, const Point3D& v2);
    static Point3D Normalize(const Point3D& vector);
    static double Length(const Point3D& vector);
    static double LengthSquared(const Point3D& vector);
    static double Angle(const Point3D& v1, const Point3D& v2);
    
    //=======================================================================================
    // Triangle operations
    //=======================================================================================
    static double TriangleArea2D(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static double TriangleArea3D(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static Point3D TriangleCentroid(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static Point3D TriangleNormal(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static Point3D TriangleCircumcenter(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static double TriangleCircumradius(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static Point3D TriangleIncenter(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static double TriangleInradius(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    
    //=======================================================================================
    // Triangle quality metrics
    //=======================================================================================
    static double TriangleAspectRatio(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static double TriangleMinAngle(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static double TriangleMaxAngle(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    static bool IsTriangleSliver(const Point3D& p1, const Point3D& p2, const Point3D& p3, double threshold = 0.1);
    static bool IsTriangleDegenerate(const Point3D& p1, const Point3D& p2, const Point3D& p3, double tolerance = Constants::EPSILON);
    
    //=======================================================================================
    // Point-in-triangle tests
    //=======================================================================================
    static bool IsPointInTriangle2D(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    static Point3D BarycentricCoordinates(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    static Point3D InterpolateInTriangle(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    
    //=======================================================================================
    // Line operations
    //=======================================================================================
    static double PointToLineDistance2D(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static double PointToLineDistance3D(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static Point3D ClosestPointOnLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    static double ParameterOnLine(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
    
    //=======================================================================================
    // Line intersection
    //=======================================================================================
    static bool LineIntersection2D(const Point3D& p1, const Point3D& p2, const Point3D& p3, const Point3D& p4, Point3D& intersection);
    static bool SegmentIntersection2D(const Point3D& p1, const Point3D& p2, const Point3D& p3, const Point3D& p4, Point3D& intersection);
    static bool LineSegmentIntersection2D(const Point3D& lineStart, const Point3D& lineEnd, 
                                         const Point3D& segStart, const Point3D& segEnd, Point3D& intersection);
    
    //=======================================================================================
    // Polygon operations
    //=======================================================================================
    static double PolygonArea2D(const std::vector<Point3D>& polygon);
    static Point3D PolygonCentroid2D(const std::vector<Point3D>& polygon);
    static bool IsPointInPolygon2D(const Point3D& point, const std::vector<Point3D>& polygon);
    static bool IsPolygonClockwise2D(const std::vector<Point3D>& polygon);
    static std::vector<Point3D> ConvexHull2D(const std::vector<Point3D>& points);
    
    //=======================================================================================
    // Circle operations
    //=======================================================================================
    static bool IsPointInCircle(const Point3D& point, const Point3D& center, double radius);
    static bool IsPointInCircumcircle(const Point3D& point, const Point3D& v1, const Point3D& v2, const Point3D& v3);
    static Point3D CircleCenter(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    
    //=======================================================================================
    // Geometric predicates (robust)
    //=======================================================================================
    static double Orient2D(const Point3D& pa, const Point3D& pb, const Point3D& pc);
    static double InCircle(const Point3D& pa, const Point3D& pb, const Point3D& pc, const Point3D& pd);
    static bool IsCounterClockwise(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    
    //=======================================================================================
    // Bounding box operations
    //=======================================================================================
    static Range3D BoundingBox(const std::vector<Point3D>& points);
    static bool BoundingBoxIntersect(const Range3D& box1, const Range3D& box2);
    static Range3D BoundingBoxUnion(const Range3D& box1, const Range3D& box2);
    static Range3D BoundingBoxIntersection(const Range3D& box1, const Range3D& box2);
    
    //=======================================================================================
    // Transformation operations
    //=======================================================================================
    static Point3D Translate(const Point3D& point, const Point3D& offset);
    static Point3D Scale(const Point3D& point, const Point3D& center, double factor);
    static Point3D Rotate2D(const Point3D& point, const Point3D& center, double angle);
    static std::vector<Point3D> Transform(const std::vector<Point3D>& points, 
                                         const std::function<Point3D(const Point3D&)>& transformer);
    
    //=======================================================================================
    // Utility functions
    //=======================================================================================
    static bool IsNearlyEqual(double a, double b, double tolerance = Constants::EPSILON);
    static bool IsNearlyZero(double value, double tolerance = Constants::EPSILON);
    static double Clamp(double value, double min, double max);
    static double DegreesToRadians(double degrees);
    static double RadiansToDegrees(double radians);
    
    //=======================================================================================
    // Slope and aspect calculations
    //=======================================================================================
    static double CalculateSlope(const Point3D& p1, const Point3D& p2);
    static double CalculateGradient(const Point3D& p1, const Point3D& p2);
    static double CalculateAspect(const Point3D& normal);
    static Point3D CalculateSurfaceNormal(const Point3D& p1, const Point3D& p2, const Point3D& p3);
    
    //=======================================================================================
    // Smoothing and filtering
    //=======================================================================================
    static std::vector<Point3D> SmoothPolyline(const std::vector<Point3D>& points, double factor = 0.5, int iterations = 1);
    static std::vector<Point3D> SimplifyPolyline(const std::vector<Point3D>& points, double tolerance);
    static std::vector<Point3D> ResamplePolyline(const std::vector<Point3D>& points, double interval);
    
private:
    //=======================================================================================
    // Internal helper functions
    //=======================================================================================
    static double Determinant2x2(double a, double b, double c, double d);
    static double Determinant3x3(double a, double b, double c, double d, double e, double f, double g, double h, double i);
    static void DouglasPeucker(const std::vector<Point3D>& points, double tolerance, 
                              std::vector<bool>& keep, int start, int end);
    static double PerpendicularDistance(const Point3D& point, const Point3D& lineStart, const Point3D& lineEnd);
};

//=======================================================================================
// Inline implementations for performance-critical functions
//=======================================================================================
inline double GeometryUtils::Distance2D(const Point3D& p1, const Point3D& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}

inline double GeometryUtils::Distance3D(const Point3D& p1, const Point3D& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    double dz = p1.z - p2.z;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

inline double GeometryUtils::DistanceSquared2D(const Point3D& p1, const Point3D& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return dx * dx + dy * dy;
}

inline double GeometryUtils::DistanceSquared3D(const Point3D& p1, const Point3D& p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    double dz = p1.z - p2.z;
    return dx * dx + dy * dy + dz * dz;
}

inline Point3D GeometryUtils::Midpoint(const Point3D& p1, const Point3D& p2) {
    return Point3D((p1.x + p2.x) * 0.5, (p1.y + p2.y) * 0.5, (p1.z + p2.z) * 0.5);
}

inline Point3D GeometryUtils::Interpolate(const Point3D& p1, const Point3D& p2, double t) {
    return Point3D(p1.x + t * (p2.x - p1.x), p1.y + t * (p2.y - p1.y), p1.z + t * (p2.z - p1.z));
}

inline double GeometryUtils::DotProduct(const Point3D& v1, const Point3D& v2) {
    return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
}

inline Point3D GeometryUtils::CrossProduct(const Point3D& v1, const Point3D& v2) {
    return Point3D(v1.y * v2.z - v1.z * v2.y, v1.z * v2.x - v1.x * v2.z, v1.x * v2.y - v1.y * v2.x);
}

inline double GeometryUtils::Length(const Point3D& vector) {
    return std::sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
}

inline double GeometryUtils::LengthSquared(const Point3D& vector) {
    return vector.x * vector.x + vector.y * vector.y + vector.z * vector.z;
}

inline bool GeometryUtils::IsNearlyEqual(double a, double b, double tolerance) {
    return std::abs(a - b) <= tolerance;
}

inline bool GeometryUtils::IsNearlyZero(double value, double tolerance) {
    return std::abs(value) <= tolerance;
}

inline double GeometryUtils::Clamp(double value, double min, double max) {
    return std::max(min, std::min(max, value));
}

inline double GeometryUtils::DegreesToRadians(double degrees) {
    return degrees * Constants::DEGREES_TO_RADIANS;
}

inline double GeometryUtils::RadiansToDegrees(double radians) {
    return radians * Constants::RADIANS_TO_DEGREES;
}

} // namespace TerrainModel
