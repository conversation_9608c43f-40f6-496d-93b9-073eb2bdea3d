/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

#include "SpatialIndex.h"
#include "GeometryUtils.h"
#include <algorithm>
#include <queue>
#include <cmath>

namespace TerrainModel {

//=======================================================================================
// SpatialIndex factory implementation
//=======================================================================================
std::unique_ptr<SpatialIndex> SpatialIndex::Create(const std::string& type) {
    if (type == "rtree") {
        return std::make_unique<RTreeSpatialIndex>();
    } else if (type == "adaptive") {
        return std::make_unique<AdaptiveSpatialIndex>();
    } else {
        // Default to R-tree
        return std::make_unique<RTreeSpatialIndex>();
    }
}

//=======================================================================================
// R-tree spatial index implementation
//=======================================================================================

// R-tree internal structures
struct RTreeSpatialIndex::Entry {
    Range3D bounds;
    DTMTriangleId triangleId = Constants::INVALID_ID;
    DTMVertexId vertexId = Constants::INVALID_ID;
    bool isTriangle = false;
    
    Entry() = default;
    Entry(DTMTriangleId tid, const Range3D& b) : bounds(b), triangleId(tid), isTriangle(true) {}
    Entry(DTMVertexId vid, const Point3D& pos) : vertexId(vid), isTriangle(false) {
        bounds.low = bounds.high = pos;
    }
};

struct RTreeSpatialIndex::Node {
    std::vector<Entry> entries;
    std::vector<std::unique_ptr<Node>> children;
    bool isLeaf = true;
    
    Node() = default;
    ~Node() = default;
    
    // Non-copyable
    Node(const Node&) = delete;
    Node& operator=(const Node&) = delete;
    
    // Movable
    Node(Node&&) = default;
    Node& operator=(Node&&) = default;
};

RTreeSpatialIndex::RTreeSpatialIndex()
    : m_root(std::make_unique<Node>())
    , m_triangleCount(0)
    , m_vertexCount(0)
    , m_height(1)
    , m_queryCount(0)
    , m_nodeVisits(0)
{
}

RTreeSpatialIndex::~RTreeSpatialIndex() = default;

void RTreeSpatialIndex::InsertTriangle(DTMTriangleId triangleId, const Range3D& bounds) {
    Entry entry(triangleId, bounds);
    Insert(entry);
    m_triangleCount++;
}

void RTreeSpatialIndex::RemoveTriangle(DTMTriangleId triangleId) {
    Entry entry;
    entry.triangleId = triangleId;
    entry.isTriangle = true;
    
    if (Remove(entry)) {
        m_triangleCount--;
    }
}

void RTreeSpatialIndex::UpdateTriangle(DTMTriangleId triangleId, const Range3D& oldBounds, const Range3D& newBounds) {
    RemoveTriangle(triangleId);
    InsertTriangle(triangleId, newBounds);
}

void RTreeSpatialIndex::InsertVertex(DTMVertexId vertexId, const Point3D& position) {
    Entry entry(vertexId, position);
    Insert(entry);
    m_vertexCount++;
}

void RTreeSpatialIndex::RemoveVertex(DTMVertexId vertexId) {
    Entry entry;
    entry.vertexId = vertexId;
    entry.isTriangle = false;
    
    if (Remove(entry)) {
        m_vertexCount--;
    }
}

void RTreeSpatialIndex::UpdateVertex(DTMVertexId vertexId, const Point3D& oldPosition, const Point3D& newPosition) {
    RemoveVertex(vertexId);
    InsertVertex(vertexId, newPosition);
}

std::vector<DTMTriangleId> RTreeSpatialIndex::FindTrianglesInRange(const Range3D& range) const {
    std::vector<Entry> results;
    Search(range, results);
    
    std::vector<DTMTriangleId> triangles;
    for (const auto& entry : results) {
        if (entry.isTriangle) {
            triangles.push_back(entry.triangleId);
        }
    }
    
    m_queryCount++;
    return triangles;
}

std::vector<DTMTriangleId> RTreeSpatialIndex::FindTrianglesContaining(const Point3D& point) const {
    Range3D pointRange;
    pointRange.low = pointRange.high = point;
    return FindTrianglesInRange(pointRange);
}

std::vector<DTMVertexId> RTreeSpatialIndex::FindVerticesInRange(const Range3D& range) const {
    std::vector<Entry> results;
    Search(range, results);
    
    std::vector<DTMVertexId> vertices;
    for (const auto& entry : results) {
        if (!entry.isTriangle) {
            vertices.push_back(entry.vertexId);
        }
    }
    
    m_queryCount++;
    return vertices;
}

std::vector<DTMVertexId> RTreeSpatialIndex::FindNearestVertices(const Point3D& point, double radius, int maxCount) const {
    std::vector<Entry> results;
    SearchNearest(point, radius, results, maxCount);
    
    std::vector<DTMVertexId> vertices;
    for (const auto& entry : results) {
        if (!entry.isTriangle) {
            vertices.push_back(entry.vertexId);
        }
    }
    
    m_queryCount++;
    return vertices;
}

void RTreeSpatialIndex::Clear() {
    m_root = std::make_unique<Node>();
    m_triangleCount = 0;
    m_vertexCount = 0;
    m_height = 1;
    m_queryCount = 0;
    m_nodeVisits = 0;
}

void RTreeSpatialIndex::Rebuild() {
    // For now, just clear and let items be re-inserted
    // A more sophisticated implementation would collect all entries and rebuild optimally
    Clear();
}

size_t RTreeSpatialIndex::GetMemoryUsage() const {
    // Rough estimate of memory usage
    size_t nodeSize = sizeof(Node) + sizeof(Entry) * MAX_ENTRIES;
    size_t estimatedNodes = std::max(1UL, (m_triangleCount + m_vertexCount) / (MAX_ENTRIES / 2));
    return estimatedNodes * nodeSize;
}

void RTreeSpatialIndex::GetStatistics(std::map<std::string, double>& stats) const {
    stats["triangleCount"] = static_cast<double>(m_triangleCount);
    stats["vertexCount"] = static_cast<double>(m_vertexCount);
    stats["height"] = static_cast<double>(m_height);
    stats["queryCount"] = static_cast<double>(m_queryCount);
    stats["nodeVisits"] = static_cast<double>(m_nodeVisits);
    stats["memoryUsage"] = static_cast<double>(GetMemoryUsage());
    
    if (m_queryCount > 0) {
        stats["avgNodeVisitsPerQuery"] = static_cast<double>(m_nodeVisits) / static_cast<double>(m_queryCount);
    }
}

void RTreeSpatialIndex::Insert(const Entry& entry) {
    // Find the best leaf to insert the entry
    Node* leaf = ChooseLeaf(entry.bounds).get();
    
    // Add entry to leaf
    leaf->entries.push_back(entry);
    
    // If leaf overflows, split it
    std::unique_ptr<Node> newNode = nullptr;
    if (leaf->entries.size() > MAX_ENTRIES) {
        auto splitResult = SplitNode(leaf);
        newNode = std::move(splitResult.second);
    }
    
    // Adjust tree upward
    AdjustTree(leaf, std::move(newNode));
}

bool RTreeSpatialIndex::Remove(const Entry& entry) {
    // Simple implementation - find and remove entry
    // A full implementation would handle tree condensation
    std::function<bool(Node*)> removeFromNode = [&](Node* node) -> bool {
        for (auto it = node->entries.begin(); it != node->entries.end(); ++it) {
            if (entry.isTriangle && it->isTriangle && it->triangleId == entry.triangleId) {
                node->entries.erase(it);
                return true;
            } else if (!entry.isTriangle && !it->isTriangle && it->vertexId == entry.vertexId) {
                node->entries.erase(it);
                return true;
            }
        }
        
        for (auto& child : node->children) {
            if (removeFromNode(child.get())) {
                return true;
            }
        }
        
        return false;
    };
    
    return removeFromNode(m_root.get());
}

void RTreeSpatialIndex::Search(const Range3D& range, std::vector<Entry>& results) const {
    std::function<void(const Node*)> searchNode = [&](const Node* node) {
        m_nodeVisits++;
        
        for (const auto& entry : node->entries) {
            if (GeometryUtils::BoundingBoxIntersect(entry.bounds, range)) {
                if (node->isLeaf) {
                    results.push_back(entry);
                }
            }
        }
        
        if (!node->isLeaf) {
            for (const auto& child : node->children) {
                Range3D childBounds = CalculateBounds(child.get());
                if (GeometryUtils::BoundingBoxIntersect(childBounds, range)) {
                    searchNode(child.get());
                }
            }
        }
    };
    
    searchNode(m_root.get());
}

void RTreeSpatialIndex::SearchPoint(const Point3D& point, std::vector<Entry>& results) const {
    Range3D pointRange;
    pointRange.low = pointRange.high = point;
    Search(pointRange, results);
}

void RTreeSpatialIndex::SearchNearest(const Point3D& point, double radius, std::vector<Entry>& results, int maxCount) const {
    Range3D searchRange;
    searchRange.low = Point3D(point.x - radius, point.y - radius, point.z - radius);
    searchRange.high = Point3D(point.x + radius, point.y + radius, point.z + radius);
    
    std::vector<Entry> candidates;
    Search(searchRange, candidates);
    
    // Sort by distance and take the closest ones
    std::vector<std::pair<double, Entry>> distanceEntries;
    for (const auto& entry : candidates) {
        Point3D entryPoint = entry.isTriangle ? 
            Point3D((entry.bounds.low.x + entry.bounds.high.x) * 0.5,
                   (entry.bounds.low.y + entry.bounds.high.y) * 0.5,
                   (entry.bounds.low.z + entry.bounds.high.z) * 0.5) :
            entry.bounds.low;
        
        double distance = GeometryUtils::Distance3D(point, entryPoint);
        if (distance <= radius) {
            distanceEntries.emplace_back(distance, entry);
        }
    }
    
    std::sort(distanceEntries.begin(), distanceEntries.end());
    
    int count = (maxCount > 0) ? std::min(maxCount, static_cast<int>(distanceEntries.size())) : 
                                static_cast<int>(distanceEntries.size());
    
    for (int i = 0; i < count; ++i) {
        results.push_back(distanceEntries[i].second);
    }
}

std::unique_ptr<RTreeSpatialIndex::Node> RTreeSpatialIndex::ChooseLeaf(const Range3D& bounds) {
    // Simple implementation - just return the root for now
    // A full implementation would traverse the tree to find the best leaf
    return std::make_unique<Node>();
}

void RTreeSpatialIndex::AdjustTree(Node* leaf, std::unique_ptr<Node> newNode) {
    // Simple implementation - if we have a new node, make it the new root
    if (newNode) {
        auto newRoot = std::make_unique<Node>();
        newRoot->isLeaf = false;
        newRoot->children.push_back(std::move(m_root));
        newRoot->children.push_back(std::move(newNode));
        m_root = std::move(newRoot);
        m_height++;
    }
}

std::pair<std::unique_ptr<RTreeSpatialIndex::Node>, std::unique_ptr<RTreeSpatialIndex::Node>> 
RTreeSpatialIndex::SplitNode(Node* node) {
    auto node1 = std::make_unique<Node>();
    auto node2 = std::make_unique<Node>();
    
    node1->isLeaf = node->isLeaf;
    node2->isLeaf = node->isLeaf;
    
    // Simple split - divide entries in half
    size_t mid = node->entries.size() / 2;
    
    node1->entries.assign(node->entries.begin(), node->entries.begin() + mid);
    node2->entries.assign(node->entries.begin() + mid, node->entries.end());
    
    // Copy the first node's content back to the original node
    node->entries = node1->entries;
    
    return std::make_pair(std::move(node1), std::move(node2));
}

void RTreeSpatialIndex::CondenseTree(Node* leaf) {
    // TODO: Implement tree condensation
}

Range3D RTreeSpatialIndex::CalculateBounds(const Node* node) const {
    if (node->entries.empty()) {
        return Range3D();
    }
    
    Range3D bounds = node->entries[0].bounds;
    for (size_t i = 1; i < node->entries.size(); ++i) {
        bounds = GeometryUtils::BoundingBoxUnion(bounds, node->entries[i].bounds);
    }
    
    return bounds;
}

double RTreeSpatialIndex::CalculateAreaIncrease(const Range3D& existing, const Range3D& addition) const {
    Range3D combined = GeometryUtils::BoundingBoxUnion(existing, addition);
    
    double existingArea = (existing.high.x - existing.low.x) * (existing.high.y - existing.low.y);
    double combinedArea = (combined.high.x - combined.low.x) * (combined.high.y - combined.low.y);
    
    return combinedArea - existingArea;
}

double RTreeSpatialIndex::CalculateOverlap(const Range3D& bounds, const Node* node) const {
    double totalOverlap = 0.0;
    
    for (const auto& entry : node->entries) {
        Range3D intersection = GeometryUtils::BoundingBoxIntersection(bounds, entry.bounds);
        if (intersection.IsValid()) {
            totalOverlap += (intersection.high.x - intersection.low.x) * (intersection.high.y - intersection.low.y);
        }
    }
    
    return totalOverlap;
}

void RTreeSpatialIndex::UpdateBounds(Node* node) {
    // TODO: Update node bounds based on entries
}

} // namespace TerrainModel
