/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#if defined (_WIN32) && !defined(BENTLEY_WINRT)
#include <windows.h>
#endif
#include "IModelJsNative.h"
#include <Bentley/Base64Utilities.h>
#include <Bentley/Desktop/FileSystem.h>
#include <GeomSerialization/GeomSerializationApi.h>
#include <ECDb/ChangeIterator.h>
#include <DgnPlatform/FunctionalDomain.h>
#include <WebServices/iModelHub/Client/ChangeSetKind.h>
#include <chrono>

#if defined (BENTLEYCONFIG_PARASOLID)
#include <BRepCore/PSolidUtil.h>
#endif

static Utf8String s_lastECDbIssue;
static BeFileName s_addonDllDir;
static BeFileName s_tempDir;
static bool s_useTileCache = true;

using namespace ElementDependency;

USING_NAMESPACE_BENTLEY_IMODELHUB

namespace IModelJsNative {

BE_JSON_NAME(parentId)
BE_JSON_NAME(pathname)
BE_JSON_NAME(changeType)
BE_JSON_NAME(codeSpecId)
BE_JSON_NAME(codeScope)
BE_JSON_NAME(value)
BE_JSON_NAME(state)

/*=================================================================================**//**
* An implementation of IKnownLocationsAdmin that is useful for desktop applications.
* This implementation works for Windows, Linux, and MacOS.
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct KnownLocationsAdmin : DgnPlatformLib::Host::IKnownLocationsAdmin
{
    BeFileName m_tempDirectory;
    BeFileName m_assetsDirectory;

    BeFileNameCR _GetLocalTempDirectoryBaseName() override {return m_tempDirectory;}
    BeFileNameCR _GetDgnPlatformAssetsDirectory() override {return m_assetsDirectory;}

    //! Construct an instance of the KnownDesktopLocationsAdmin
    KnownLocationsAdmin()
        {
        m_tempDirectory = s_tempDir;
        m_assetsDirectory = s_addonDllDir;
        m_assetsDirectory.AppendToPath(L"Assets");
        }
};

//=======================================================================================
// @bsistruct                                   Sam.Wilson                  05/17
//=======================================================================================
struct JsDgnHost : DgnPlatformLib::Host {
private:
    BeMutex m_mutex;

    void _SupplyProductName(Utf8StringR name) override { name.assign("IModelJs"); }
    IKnownLocationsAdmin& _SupplyIKnownLocationsAdmin() override { return *new KnownLocationsAdmin(); }
    BeSQLite::L10N::SqlangFiles _SupplySqlangFiles() override
        {
        BeFileName sqlang(GetIKnownLocationsAdmin().GetDgnPlatformAssetsDirectory());
        sqlang.AppendToPath(L"sqlang/iModelJsNodeAddon_en.sqlang.db3");
        return BeSQLite::L10N::SqlangFiles(sqlang);
        }

    RepositoryAdmin& _SupplyRepositoryAdmin() override {return JsInterop::GetRepositoryAdmin();}

public:
    JsDgnHost() { BeAssertFunctions::SetBeAssertHandler(&JsInterop::HandleAssertion);}
};

//=======================================================================================
// @bsistruct                                   Sam.Wilson                  02/18
//=======================================================================================
struct NativeLoggingShim : NativeLogging::Provider::ILogProvider
{
    int STDCALL_ATTRIBUTE Initialize() override {return SUCCESS;}

    int STDCALL_ATTRIBUTE Uninitialize() override {return SUCCESS;}

    int STDCALL_ATTRIBUTE CreateLogger(WCharCP nameSpace, NativeLogging::Provider::ILogProviderContext** ppContext) override
        {
        *ppContext = reinterpret_cast<NativeLogging::Provider::ILogProviderContext*>(new WString(nameSpace));
        return SUCCESS;
        }

    int STDCALL_ATTRIBUTE DestroyLogger(NativeLogging::Provider::ILogProviderContext* pContext) override
        {
        WString* ns = reinterpret_cast<WString*>(pContext);
        if(nullptr != ns)
            delete ns;
        return SUCCESS;
        }

    int STDCALL_ATTRIBUTE SetOption(WCharCP attribName, WCharCP attribValue) override {BeAssert(false); return SUCCESS;}

    int STDCALL_ATTRIBUTE GetOption(WCharCP attribName, WCharP attribValue, uint32_t valueSize) override {return ERROR;}

    void STDCALL_ATTRIBUTE LogMessage(NativeLogging::Provider::ILogProviderContext* context, NativeLogging::SEVERITY sev, WCharCP msg) override
        {
        LogMessage(context, sev, Utf8String(msg).c_str());
        }

    int  STDCALL_ATTRIBUTE SetSeverity(WCharCP nameSpace, NativeLogging::SEVERITY severity) override
        {
        BeAssert(false && "only the app (in TypeScript) sets severities");
        return ERROR;
        }

    void STDCALL_ATTRIBUTE LogMessage(NativeLogging::Provider::ILogProviderContext* context, NativeLogging::SEVERITY sev, Utf8CP msg) override
        {
        WString* ns = reinterpret_cast<WString*>(context);
        JsInterop::LogMessage(Utf8String(*ns).c_str(), sev, msg);
        }

    bool STDCALL_ATTRIBUTE IsSeverityEnabled(NativeLogging::Provider::ILogProviderContext* context, NativeLogging::SEVERITY sev) override
        {
        WString* ns = reinterpret_cast<WString*>(context);
        return JsInterop::IsSeverityEnabled(Utf8String(*ns).c_str(), sev);
        }

};

} // namespace IModelJsNative

using namespace IModelJsNative;

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  05/17
//---------------------------------------------------------------------------------------
void JsInterop::Initialize(BeFileNameCR addonDllDir, Napi::Env env, BeFileNameCR tempDir)
    {
    Env() = env;
    MainThreadId() = BeThreadUtilities::GetCurrentThreadId();
    s_addonDllDir = addonDllDir;
    s_tempDir = tempDir;

#if defined(BENTLEYCONFIG_OS_WINDOWS_DESKTOP) // excludes WinRT
    // Include this location for delay load of pskernel...
    WString newPath;
    newPath = L"PATH=" + addonDllDir + L";";
PUSH_DISABLE_DEPRECATION_WARNINGS
    newPath.append(::_wgetenv(L"PATH"));
POP_DISABLE_DEPRECATION_WARNINGS
    _wputenv(newPath.c_str());

    // Defeat node's attempt to turn off WER
    auto errMode = GetErrorMode();
    errMode &= ~SEM_NOGPFAULTERRORBOX;
    SetErrorMode(errMode);
#endif

    static std::once_flag s_initFlag;
    std::call_once(s_initFlag, []()
        {
        auto jsHost = new JsDgnHost();
        DgnPlatformLib::Initialize(*jsHost);
        RegisterOptionalDomains();
        InitLogging();
        InitializeParasolid();
        });
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Shaun.Sewall                09/18
//---------------------------------------------------------------------------------------
void JsInterop::RegisterOptionalDomains()
    {
    DgnDomains::RegisterDomain(FunctionalDomain::GetDomain(), DgnDomain::Required::No, DgnDomain::Readonly::No);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
void JsInterop::InitLogging()
    {
    NativeLogging::LoggingConfig::ActivateProvider(new NativeLoggingShim());
    }

#if defined (BENTLEYCONFIG_PARASOLID)
static RefCountedPtr<PSolidThreadUtil::MainThreadMark> s_psolidMainThreadMark;
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   09/18
+---------------+---------------+---------------+---------------+---------------+------*/
void JsInterop::InitializeParasolid()
    {
#if defined (BENTLEYCONFIG_PARASOLID)
    PSolidKernelManager::StartSession();

    if (s_psolidMainThreadMark.IsNull())
        s_psolidMainThreadMark = new PSolidThreadUtil::MainThreadMark();
#endif
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  05/17
//---------------------------------------------------------------------------------------
NativeLogging::ILogger& JsInterop::GetLogger()
    {
    static NativeLogging::ILogger* s_logger;
    if (nullptr == s_logger)
        s_logger = NativeLogging::LoggingManager::GetLogger("imodeljs-addon"); // This is thread-safe. The assignment is atomic, and GetLogger will always return the same value for a given key anyway.
    return *s_logger;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Affan.Khan                     05/19
//---------------------------------------------------------------------------------------
Napi::Value JsInterop::ConcurrentQueryInit(ECDbCR ecdb, Napi::Env env, Napi::Object cfg)
    {
    ConcurrentQueryManager::Config config;
    Napi::Number v;
    Napi::Boolean b;
    b = cfg.Get("useSharedCache").ToBoolean();
    if (!b.IsUndefined() && !b.IsNull())
        config.SetUseSharedCache(b);

    b = cfg.Get("useUncommittedRead").ToBoolean();
    if (!v.IsUndefined() && !b.IsNull())
        config.SetUseUncommittedRead(b);

    b = cfg.Get("useImmutableDb").ToBoolean();
    if (!v.IsUndefined() && !b.IsNull())
        config.SetUseImmutableDb(b);

    v = cfg.Get("concurrent").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetConcurrent(v.Uint32Value());

    v = cfg.Get("cachedStatementsPerThread").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetCacheStatementsPerThread(v.Uint32Value());

    v = cfg.Get("maxQueueSize").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetMaxQueueSize(v.Uint32Value());

    v = cfg.Get("minMonitorInterval").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetMinMonitorInterval(std::chrono::seconds(v.Uint32Value()));

    v = cfg.Get("idleCleanupTime").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetIdleCleanupTime(std::chrono::seconds(v.Uint32Value()));

    v = cfg.Get("autoExpireTimeForCompletedQuery").ToNumber();
    if (!v.IsUndefined() && !v.IsNull())
        config.SetAutoExpireTimeForCompletedQuery(std::chrono::seconds(v.Uint32Value()));

    auto quota = cfg.Get("quota").ToObject();
    if (!quota.IsUndefined() && !quota.IsNull())
        {
        std::chrono::seconds maxTimeAllowed(0);
        unsigned int maxMemoryAllowed = 0;

        v = quota.Get("maxTimeAllowed").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            maxTimeAllowed = std::chrono::seconds(v.Uint32Value());

        v = quota.Get("maxMemoryAllowed").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            maxMemoryAllowed = v.Uint32Value();

        config.SetQuota(ConcurrentQueryManager::Quota(maxTimeAllowed, maxMemoryAllowed));
        }

    return Napi::Boolean::New(env, ecdb.GetConcurrentQueryManager().Initalize(config));
    }
//---------------------------------------------------------------------------------------
// @bsimethod                               Affan.Khan                     05/19
//---------------------------------------------------------------------------------------
Napi::Value JsInterop::PostConcurrentQuery(ECDbCR ecdb, Napi::Env env, Utf8StringCR ecsql, Utf8StringCR bindings, Napi::Object limit, Napi::Object quota, ConcurrentQueryManager::Priority priority)
    {
    TaskId taskId = 0;
    Napi::Number v;

    int maxRowAllowed = -1;
    int startRowOffset = -1;
    std::chrono::seconds maxTimeAllowed(0);
    unsigned int maxMemoryAllowed = 0;
    if (!limit.IsUndefined() && !limit.IsNull())
        {
        v = limit.Get("maxRowAllowed").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            maxRowAllowed = v.Int32Value() > 0 ? v.Int32Value() : -1;

        v = limit.Get("startRowOffset").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            startRowOffset = v.Int32Value() > 0 ? v.Int32Value() : -1;
        }

    if (!quota.IsUndefined() && !quota.IsNull())
        {
        v = quota.Get("maxTimeAllowed").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            maxTimeAllowed = std::chrono::seconds(v.Uint32Value());

        v = quota.Get("maxMemoryAllowed").ToNumber();
        if (!v.IsUndefined() && !v.IsNull())
            maxMemoryAllowed = v.Uint32Value();
        }
        JsInterop::ObjectReferenceClaimCheck jsRequestContext = JsInterop::GetCurrentClientRequestContextForMainThread();
        ConcurrentQueryManager::RequestContext context([jsRequestContext]()
            {
            if (!JsInterop::IsMainThread())
                {
                // WIP: Seems like on MacOS _only_ we somehow get here on the main thread. Needs investigation.
                JsInterop::SetCurrentClientRequestContextForWorkerThread(jsRequestContext);
                }
            });

    const auto rc = ecdb.GetConcurrentQueryManager().PostQuery(taskId, ecsql.c_str(), bindings.c_str(),
                                                       ConcurrentQueryManager::Limit(maxRowAllowed, startRowOffset),
                                                       ConcurrentQueryManager::Quota(maxTimeAllowed, maxMemoryAllowed), priority, context);
    auto result = Napi::Object::New(env);
    result.Set("status", (int) rc);
    result.Set("taskId", taskId);
    return result;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Affan.Khan                     05/19
//---------------------------------------------------------------------------------------
Napi::Value JsInterop::PollConcurrentQuery(ECDbCR ecdb, Napi::Env env, uint32_t taskId)
    {
    Utf8String json;
    int64_t rows;
    const auto rc = ecdb.GetConcurrentQueryManager().PollQuery(json, rows, taskId);
    auto result = Napi::Object::New(env);
    result.Set("status", (int) rc);
    result.Set("result", json.c_str());
    result.Set("rowCount", rows);
    return result;
    }
//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 02/18
//---------------------------------------------------------------------------------------
DgnDbPtr JsInterop::CreateDgnDb(DbResult& result, BeFileNameCR filename, JsonValueCR props, Napi::Env env)
    {
    result = BE_SQLITE_NOTFOUND;
    JsonValueCR rootSubject = props[json_rootSubject()];
    if (rootSubject.isNull() || !rootSubject.isMember(json_name())) {
        Napi::TypeError::New(env, "Root subject name is missing").ThrowAsJavaScriptException();
        return nullptr;
    }

    BeFileName path = filename.GetDirectoryName();
    if (!path.DoesPathExist()) {
        Utf8String err = Utf8String("Path [") + path.GetNameUtf8() + "] does not exist";
        Napi::TypeError::New(env, err.c_str()).ThrowAsJavaScriptException();
        return nullptr;
    }

    CreateDgnDbParams params(rootSubject[json_name()].asCString());
    if (rootSubject.isMember(json_description()))
        params.SetRootSubjectDescription(rootSubject[json_description()].asCString());
    if (props.isMember(json_globalOrigin()))
        params.m_globalOrigin = JsonUtils::ToDPoint3d(props[json_globalOrigin()]);
    if (props.isMember(json_guid()))
        params.m_guid.FromString(props[json_guid()].asCString());
    if (props.isMember(json_projectExtents()))
        params.m_projectExtents.FromJson(props[json_projectExtents()]);
    if (props.isMember(json_client()))
        params.m_client = props[json_client()].asCString();
    if (props.isMember(json_password()))
        {
        params.GetEncryptionParamsR().SetPassword(props[json_password()].asCString());
        params.SetPageSize(BeSQLite::Db::PageSize::PAGESIZE_64K); // SQLite recommends a larger page size for encrypted files
        }

    DgnDbPtr db = DgnDb::CreateDgnDb(&result, filename, params);
    if (!db.IsValid())
        return nullptr;

    // NEEDS_WORK - create GCS from ecef location

    return db;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  06/17
//---------------------------------------------------------------------------------------
DbResult JsInterop::OpenDgnDb(DgnDbPtr& db, BeFileNameCR fileOrPathname, DgnDb::OpenParams const& openParams)
    {
    BeFileName pathname = ResolveFileName(fileOrPathname);
    DbResult result;
    db = DgnDb::OpenDgnDb(&result, pathname, openParams);
    return result;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  06/17
//---------------------------------------------------------------------------------------
BeFileName JsInterop::ResolveFileName(BeFileNameCR fileOrPathname)
    {
    BeFileName pathname;
    if (fileOrPathname.DoesPathExist())
        {
        pathname = fileOrPathname;
        }
    else
        {
        // *** NEEDS WORK: To save the user the trouble of typing in a full path name, we'll let him
        //                  define an envvar that defines the directory.
        BeFileName dbDir;
#if defined(BENTLEYCONFIG_OS_WINDOWS) && !defined(BENTLEYCONFIG_OS_WINRT)
PUSH_DISABLE_DEPRECATION_WARNINGS
        Utf8CP dbdirenv = getenv("NODE_DGNDB_DIR");
POP_DISABLE_DEPRECATION_WARNINGS
#else
        auto mobileDir = DgnPlatformLib::GetHost().GetIKnownLocationsAdmin().GetLocalTempDirectoryBaseName().GetNameUtf8();
        Utf8CP dbdirenv = mobileDir.c_str();
#endif
        if (nullptr != dbdirenv)
            dbDir.SetNameUtf8(dbdirenv);
        else
            {
            Desktop::FileSystem::GetCwd(dbDir);
            dbDir.AppendToPath(L"briefcases");
            }

        pathname = dbDir;
        pathname.AppendToPath(L"../Documents/");
        pathname.AppendToPath(fileOrPathname.c_str());
        }
    return pathname;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::ReadChangeSet(DgnRevisionPtr& revision, Utf8StringCR dbGuid, JsonValueCR changeSetToken)
    {
    PRECONDITION(changeSetToken.isMember("id") && changeSetToken.isMember("pathname"), RevisionStatus::FileNotFound);

    Utf8String id = changeSetToken["id"].asString();
    Utf8String parentId = changeSetToken["parentId"].asString();

    RevisionStatus revStatus;
    revision = DgnRevision::Create(&revStatus, id, parentId, dbGuid);
    PRECONDITION(revStatus == RevisionStatus::Success, revStatus);
    BeAssert(revision.IsValid());

    BeFileName changeSetPathname(changeSetToken["pathname"].asCString(), true);
    PRECONDITION(changeSetPathname.DoesPathExist(), RevisionStatus::FileNotFound);

    if (!changeSetToken["pushDate"].isNull())
        {
        Utf8String pushDate = changeSetToken["pushDate"].asString();
        DateTime date;
        DateTime::FromString(date, pushDate.c_str());
        revision->SetDateTime(date);
        }

    revision->SetRevisionChangesFile(changeSetPathname);
    return RevisionStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::DumpChangeSet(DgnDbR dgndb, JsonValueCR changeSetToken)
    {
    DgnRevisionPtr revision;
    RevisionStatus status = ReadChangeSet(revision, dgndb.GetDbGuid().ToString(), changeSetToken);
    if (RevisionStatus::Success != status)
        return status;
    revision->Dump(dgndb);
    return RevisionStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Shaun.Sewall                    12/19
//---------------------------------------------------------------------------------------
DgnDbStatus JsInterop::ExtractChangedInstanceIdsFromChangeSet(JsonValueR json, DgnDbR db, BeFileNameCR changeSetFile)
    {
    ECClassCP elementClass = db.Schemas().GetClass(BIS_ECSCHEMA_NAME, BIS_CLASS_Element);
    Json::Value elementJson(Json::ValueType::objectValue);
    Json::Value elementInsertIds(Json::ValueType::arrayValue);
    Json::Value elementUpdateIds(Json::ValueType::arrayValue);
    Json::Value elementDeleteIds(Json::ValueType::arrayValue);

    ECClassCP aspectClass = db.Schemas().GetClass(BIS_ECSCHEMA_NAME, BIS_CLASS_ElementAspect);
    Json::Value aspectJson(Json::ValueType::objectValue);
    Json::Value aspectInsertIds(Json::ValueType::arrayValue);
    Json::Value aspectUpdateIds(Json::ValueType::arrayValue);
    Json::Value aspectDeleteIds(Json::ValueType::arrayValue);

    ECClassCP modelClass = db.Schemas().GetClass(BIS_ECSCHEMA_NAME, BIS_CLASS_Model);
    Json::Value modelJson(Json::ValueType::objectValue);
    Json::Value modelInsertIds(Json::ValueType::arrayValue);
    Json::Value modelUpdateIds(Json::ValueType::arrayValue);
    Json::Value modelDeleteIds(Json::ValueType::arrayValue);

    ECClassCP relationshipClass = db.Schemas().GetClass(BIS_ECSCHEMA_NAME, BIS_REL_ElementRefersToElements);
    // WIP: also consider ElementDrivesElement
    Json::Value relationshipJson(Json::ValueType::objectValue);
    Json::Value relationshipInsertIds(Json::ValueType::arrayValue);
    Json::Value relationshipUpdateIds(Json::ValueType::arrayValue);
    Json::Value relationshipDeleteIds(Json::ValueType::arrayValue);

    ECClassCP codeSpecClass = db.Schemas().GetClass(BIS_ECSCHEMA_NAME, BIS_CLASS_CodeSpec);
    Json::Value codeSpecJson(Json::ValueType::objectValue);
    Json::Value codeSpecInsertIds(Json::ValueType::arrayValue);
    Json::Value codeSpecUpdateIds(Json::ValueType::arrayValue);
    Json::Value codeSpecDeleteIds(Json::ValueType::arrayValue);

    Json::Value fontJson(Json::ValueType::objectValue);
    Json::Value fontInsertIds(Json::ValueType::arrayValue);
    Json::Value fontUpdateIds(Json::ValueType::arrayValue);
    Json::Value fontDeleteIds(Json::ValueType::arrayValue);

    RevisionChangesFileReader changeSetReader(changeSetFile, db);
    // changeSetReader.Dump("ExtractChangedInstanceIdsFromChangeSet", db);
    ChangeIterator changeIter(db, changeSetReader);
    for (ChangeIterator::RowEntry const& changeEntry : changeIter)
        {
        if (!changeEntry.IsMapped() || !changeEntry.IsPrimaryTable())
            {
            if (0 == strcmp(changeEntry.GetTableName().c_str(), "dgn_Font"))
                {
                Changes::Change const& change = changeEntry.GetChange();
                Byte* pColumns = nullptr;
                int numColumns = 0;
                if (BE_SQLITE_OK != change.GetPrimaryKeyColumns(&pColumns, &numColumns) || (0 == numColumns) || (0 == pColumns[0]))
                    {
                    BeAssert(false && "Expect column 0 to be the primary key of the dgn_Font table");
                    continue;
                    }

                switch (changeEntry.GetDbOpcode())
                    {
                    case DbOpcode::Insert: fontInsertIds.append(change.GetNewValue(0).GetValueId<DgnFontId>().ToHexStr()); break;
                    case DbOpcode::Update: fontUpdateIds.append(change.GetOldValue(0).GetValueId<DgnFontId>().ToHexStr()); break;
                    case DbOpcode::Delete: fontDeleteIds.append(change.GetOldValue(0).GetValueId<DgnFontId>().ToHexStr()); break;
                    default: break;
                    }
                }
            continue;
            }

        ECClassCP primaryClass = changeEntry.GetPrimaryClass();
        if (primaryClass->Is(elementClass))
            {
            switch (changeEntry.GetDbOpcode())
                {
                case DbOpcode::Insert: elementInsertIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Update: elementUpdateIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Delete: elementDeleteIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                default: break;
                }
            }
        else if (primaryClass->Is(aspectClass))
            {
            switch (changeEntry.GetDbOpcode())
                {
                case DbOpcode::Insert: aspectInsertIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Update: aspectUpdateIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Delete: aspectDeleteIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                default: break;
                }
            }
        else if (primaryClass->Is(modelClass))
            {
            switch (changeEntry.GetDbOpcode())
                {
                case DbOpcode::Insert: modelInsertIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Update: modelUpdateIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Delete: modelDeleteIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                default: break;
                }
            }
        else if (primaryClass->Is(relationshipClass)) // WIP: also consider ElementDrivesElement
            {
            switch (changeEntry.GetDbOpcode())
                {
                case DbOpcode::Insert: relationshipInsertIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Update: relationshipUpdateIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Delete: relationshipDeleteIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                default: break;
                }
            }
        else if (primaryClass->Is(codeSpecClass))
            {
            switch (changeEntry.GetDbOpcode())
                {
                case DbOpcode::Insert: codeSpecInsertIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Update: codeSpecUpdateIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                case DbOpcode::Delete: codeSpecDeleteIds.append(changeEntry.GetPrimaryInstanceId().ToHexStr()); break;
                default: break;
                }
            }
        }

    if (elementInsertIds.size() > 0) elementJson["insert"] = elementInsertIds;
    if (elementUpdateIds.size() > 0) elementJson["update"] = elementUpdateIds;
    if (elementDeleteIds.size() > 0) elementJson["delete"] = elementDeleteIds;
    if (!elementJson.empty()) json["element"] = elementJson;

    if (aspectInsertIds.size() > 0) aspectJson["insert"] = aspectInsertIds;
    if (aspectUpdateIds.size() > 0) aspectJson["update"] = aspectUpdateIds;
    if (aspectDeleteIds.size() > 0) aspectJson["delete"] = aspectDeleteIds;
    if (!aspectJson.empty()) json["aspect"] = aspectJson;

    if (modelInsertIds.size() > 0) modelJson["insert"] = modelInsertIds;
    if (modelUpdateIds.size() > 0) modelJson["update"] = modelUpdateIds;
    if (modelDeleteIds.size() > 0) modelJson["delete"] = modelDeleteIds;
    if (!modelJson.empty()) json["model"] = modelJson;

    if (relationshipInsertIds.size() > 0) relationshipJson["insert"] = relationshipInsertIds;
    if (relationshipUpdateIds.size() > 0) relationshipJson["update"] = relationshipUpdateIds;
    if (relationshipDeleteIds.size() > 0) relationshipJson["delete"] = relationshipDeleteIds;
    if (!relationshipJson.empty()) json["relationship"] = relationshipJson;

    if (codeSpecInsertIds.size() > 0) codeSpecJson["insert"] = codeSpecInsertIds;
    if (codeSpecUpdateIds.size() > 0) codeSpecJson["update"] = codeSpecUpdateIds;
    if (codeSpecDeleteIds.size() > 0) codeSpecJson["delete"] = codeSpecDeleteIds;
    if (!codeSpecJson.empty()) json["codeSpec"] = codeSpecJson;

    if (fontInsertIds.size() > 0) fontJson["insert"] = fontInsertIds;
    if (fontUpdateIds.size() > 0) fontJson["update"] = fontUpdateIds;
    if (fontDeleteIds.size() > 0) fontJson["delete"] = fontDeleteIds;
    if (!fontJson.empty()) json["font"] = fontJson;

    return DgnDbStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::ReadChangeSets(bvector<DgnRevisionPtr>& revisionPtrs, bool& containsSchemaChanges, Utf8StringCR dbGuid, JsonValueCR changeSetTokens)
    {
    revisionPtrs.clear();
    containsSchemaChanges = false;
    PRECONDITION(!changeSetTokens.isNull() && changeSetTokens.isArray(), RevisionStatus::FileNotFound);

    for (uint32_t ii = 0; ii < changeSetTokens.size(); ii++)
        {
        JsonValueCR changeSetToken = changeSetTokens[ii];

        DgnRevisionPtr revision;
        RevisionStatus status = ReadChangeSet(revision, dbGuid, changeSetToken);
        if (RevisionStatus::Success != status)
            return status;

        if (!containsSchemaChanges)
            containsSchemaChanges = changeSetToken.isMember("changeType") && (BentleyApi::iModel::Hub::ChangeSetKind) changeSetToken["changeType"].asInt() == BentleyApi::iModel::Hub::ChangeSetKind::Schema;

        revisionPtrs.push_back(revision);
        }

    return RevisionStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::ApplySchemaChangeSets(BeFileNameCR dbFileName, bvector<DgnRevisionCP> const& revisions, RevisionProcessOption applyOption, IConcurrencyControl* concurrencyControl)
    {
    SchemaUpgradeOptions schemaUpgradeOptions(revisions, applyOption, concurrencyControl);
    schemaUpgradeOptions.SetUpgradeFromDomains(SchemaUpgradeOptions::DomainUpgradeOptions::SkipCheck);

    DgnDb::OpenParams openParams(Db::OpenMode::ReadWrite, BeSQLite::DefaultTxn::Yes, schemaUpgradeOptions);
    DbResult result;
    DgnDbPtr dgndb = DgnDb::OpenDgnDb(&result, dbFileName, openParams);
    POSTCONDITION(result == BE_SQLITE_OK, RevisionStatus::ApplyError);

    dgndb->CloseDb();
    return RevisionStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::ApplyDataChangeSets(DgnDbR dgndb, bvector<DgnRevisionCP> const& revisions, RevisionProcessOption applyOption)
    {
    PRECONDITION(dgndb.IsDbOpen() && "Expected briefcase to be open when applying only data changes", RevisionStatus::ApplyError);
    return dgndb.Revisions().ProcessRevisions(revisions, applyOption);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::StartCreateChangeSet(JsonValueR changeSetInfo, DgnDbR dgndb)
    {
    RevisionManagerR revisions = dgndb.Revisions();

    if (revisions.IsCreatingRevision())
        revisions.AbandonCreateRevision();

    RevisionStatus status;
    DgnRevisionPtr revision = revisions.StartCreateRevision(&status);
    if (status != RevisionStatus::Success)
        return status;
    BeAssert(revision.IsValid());

    changeSetInfo = Json::objectValue;
    changeSetInfo[json_id()] = revision->GetId().c_str();
    changeSetInfo[json_parentId()] = revision->GetParentId().c_str();
    changeSetInfo[json_pathname()] = Utf8String(revision->GetRevisionChangesFile()).c_str();
    changeSetInfo[json_changeType()] = (int) (revision->ContainsSchemaChanges(dgndb) ? ChangeSetKind::Schema : ChangeSetKind::Regular);
    return RevisionStatus::Success;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 01/18
//---------------------------------------------------------------------------------------
RevisionStatus JsInterop::FinishCreateChangeSet(DgnDbR dgndb)
    {
    return dgndb.Revisions().FinishCreateRevision();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              03/18
//---------------------------------------------------------------------------------------
void JsInterop::AbandonCreateChangeSet(DgnDbR dgndb)
    {
    RevisionManagerR revisions = dgndb.Revisions();

    if (revisions.IsCreatingRevision())
        revisions.AbandonCreateRevision();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              03/18
//---------------------------------------------------------------------------------------
static void convertCodeToJson(JsonValueR json, DgnCodeCR code, int codeState)
    {
    json = Json::objectValue;
    json[json_codeSpecId()] = code.GetCodeSpecId().ToHexStr();
    json[json_codeScope()] = code.GetScopeString();
    json[json_value()] = code.GetValueUtf8();
    json[json_state()] = codeState;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              03/18
//---------------------------------------------------------------------------------------
static void convertCodeSetToJson(JsonValueR json, DgnCodeSet const& codes, int codeState)
    {
    int i = json.size();
    for (DgnCodeCR code : codes)
        convertCodeToJson(json[i++], code, codeState);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              03/18
//---------------------------------------------------------------------------------------
DbResult ExtractCodesFromChangeSet(JsonValueR codes, DgnRevisionPtr revision, DgnDbR dgndb)
    {
    DgnCodeSet usedCodes;
    DgnCodeSet discardedCodes;
    revision->ExtractCodes(usedCodes, discardedCodes, dgndb);

    codes = Json::arrayValue;
    convertCodeSetToJson(codes, usedCodes, 2);
    convertCodeSetToJson(codes, discardedCodes, 3);
    return BE_SQLITE_OK;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              03/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::ExtractCodes(JsonValueR codes, DgnDbR dgndb)
    {
    RevisionManagerR revisions = dgndb.Revisions();

    if (!revisions.IsCreatingRevision())
        return BE_SQLITE_ERROR;

    return ExtractCodesFromChangeSet(codes, revisions.GetCreatingRevision(), dgndb);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::ExtractCodesFromFile(JsonValueR codes, DgnDbR dgndb, JsonValueCR changeSetToken)
    {
    bvector<DgnRevisionPtr> revisionPtrs;
    bool containsSchemaChanges;
    RevisionStatus status = ReadChangeSets(revisionPtrs, containsSchemaChanges, dgndb.GetDbGuid().ToString(), changeSetToken);
    if (RevisionStatus::Success != status)
        return BE_SQLITE_ERROR;

    if (revisionPtrs.empty())
        return BE_SQLITE_ERROR;

    return ExtractCodesFromChangeSet(codes, revisionPtrs[0], dgndb);
    }

#define PENDING_CHANGESET_PROPERTY_NAME "PendingChangeSets"

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
DbResult SavePendingChangeSets(DgnDbR dgndb, JsonValueCR changeSets)
    {
    DbResult result = dgndb.SaveBriefcaseLocalValue(PENDING_CHANGESET_PROPERTY_NAME, changeSets.ToString());

    if (BE_SQLITE_DONE != result)
        return result;

    return dgndb.SaveChanges();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::GetPendingChangeSets(JsonValueR changeSets, DgnDbR dgndb)
    {
    Utf8String savedValue;

    dgndb.QueryBriefcaseLocalValue(savedValue, PENDING_CHANGESET_PROPERTY_NAME);
    if (Utf8String::IsNullOrEmpty(savedValue.c_str()))
        changeSets = Json::arrayValue;
    else
        Json::Reader::Parse(savedValue, changeSets);

    return BE_SQLITE_OK;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
Json::ArrayIndex FindChangeSet(JsonValueR changeSets, Utf8StringCR changeSetId)
    {
    for (Json::ArrayIndex i = 0; i < changeSets.size(); ++i)
        {
        if (changeSets[i].asString() == changeSetId)
            return i;
        }
    return -1;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::AddPendingChangeSet(DgnDbR dgndb, Utf8StringCR changeSetId)
    {
    Json::Value changeSets;
    DbResult result = GetPendingChangeSets(changeSets, dgndb);
    if (BE_SQLITE_OK != result)
        return result;

    if (-1 != FindChangeSet(changeSets, changeSetId))
        return BE_SQLITE_OK;

    changeSets.append(changeSetId);

    return SavePendingChangeSets(dgndb, changeSets);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Karolis.Dziedzelis              04/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::RemovePendingChangeSet(DgnDbR dgndb, Utf8StringCR changeSetId)
    {
    Json::Value changeSets;
    DbResult result = GetPendingChangeSets(changeSets, dgndb);
    if (BE_SQLITE_OK != result)
        return result;

    Json::ArrayIndex foundIndex = FindChangeSet(changeSets, changeSetId);

    if (-1 == foundIndex)
        return BE_SQLITE_OK;

    changeSets.removeIndex(foundIndex);

    return SavePendingChangeSets(dgndb, changeSets);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  06/17
//---------------------------------------------------------------------------------------
void JsInterop::GetRowAsJson(Json::Value& rowJson, ECSqlStatement& stmt)
    {
    JsonECSqlSelectAdapter adapter(stmt, JsonECSqlSelectAdapter::FormatOptions(JsonECSqlSelectAdapter::MemberNameCasing::LowerFirstChar, ECJsonInt64Format::AsHexadecimalString));
    adapter.GetRow(rowJson, true);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Sam.Wilson                  06/17
//---------------------------------------------------------------------------------------
void JsInterop::GetECValuesCollectionAsJson(Json::Value& json, ECN::ECValuesCollectionCR props)
    {
    for (ECN::ECPropertyValue const& prop : props)
        {
        if (prop.HasChildValues())
            GetECValuesCollectionAsJson(json[prop.GetValueAccessor().GetAccessString(prop.GetValueAccessor().GetDepth()-1)], *prop.GetChildValues());
        else
          {
          ECN::PrimitiveECPropertyCP propertyPtr = prop.GetValueAccessor().GetECProperty()->GetAsPrimitiveProperty();
          ECN::IECInstanceCR instance = prop.GetInstance();
          if(propertyPtr != nullptr)
            {
            Utf8CP propName = propertyPtr->GetName().c_str();
            JsonEcInstanceWriter::WritePrimitiveValue(json, *propertyPtr, instance, propName);
            }
          }
        }
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
DbResult JsInterop::OpenECDb(ECDbR ecdb, BeFileNameCR pathname, BeSQLite::Db::OpenParams const& params)
    {
    if (!pathname.DoesPathExist())
        return BE_SQLITE_NOTFOUND;

    DbResult res = ecdb.OpenBeSQLiteDb(pathname, params);
    if (res != BE_SQLITE_OK)
        return res;

    return BE_SQLITE_OK;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
DbResult JsInterop::CreateECDb(ECDbR ecdb, BeFileNameCR pathname)
    {
    BeFileName path = pathname.GetDirectoryName();
    if (!path.DoesPathExist())
        return BE_SQLITE_NOTFOUND;

    DbResult res = ecdb.CreateNewDb(pathname);
    if (res != BE_SQLITE_OK)
        return res;

    return BE_SQLITE_OK;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
DbResult JsInterop::ImportSchema(ECDbR ecdb, BeFileNameCR pathname)
    {
    if (!pathname.DoesPathExist())
        return BE_SQLITE_NOTFOUND;

    ECSchemaReadContextPtr schemaContext = ECSchemaReadContext::CreateContext(false /*=acceptLegacyImperfectLatestCompatibleMatch*/, true /*=includeFilesWithNoVerExt*/);
    schemaContext->SetFinalSchemaLocater(ecdb.GetSchemaLocater());

    ECSchemaPtr schema;
    SchemaReadStatus schemaStatus = ECSchema::ReadFromXmlFile(schema, pathname.GetName(), *schemaContext);
    if (SchemaReadStatus::Success != schemaStatus)
        return BE_SQLITE_ERROR;

    bvector<ECSchemaCP> schemas;
    schemas.push_back(schema.get());
    BentleyStatus status = ecdb.Schemas().ImportSchemas(schemas);
    if (status != SUCCESS)
        return BE_SQLITE_ERROR;

    return ecdb.SaveChanges();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Shaun.Sewall                06/19
//---------------------------------------------------------------------------------------
DbResult JsInterop::ImportSchemasDgnDb(DgnDbR dgndb, bvector<Utf8String> const& schemaFileNames)
    {
    if (0 == schemaFileNames.size())
        return BE_SQLITE_NOTFOUND;

    ECSchemaReadContextPtr schemaContext = ECSchemaReadContext::CreateContext(false /*=acceptLegacyImperfectLatestCompatibleMatch*/, true /*=includeFilesWithNoVerExt*/);
    schemaContext->SetFinalSchemaLocater(dgndb.GetSchemaLocater());
    bvector<ECSchemaCP> schemas;

    for (Utf8String schemaFileName : schemaFileNames)
        {
        BeFileName schemaFile(schemaFileName.c_str(), BentleyCharEncoding::Utf8);
        if (!schemaFile.DoesPathExist())
            return BE_SQLITE_NOTFOUND;

        ECSchemaPtr schema;
        SchemaReadStatus schemaStatus = ECSchema::ReadFromXmlFile(schema, schemaFile.GetName(), *schemaContext);
        if (SchemaReadStatus::DuplicateSchema == schemaStatus)
            continue;

        if (SchemaReadStatus::Success != schemaStatus)
            return BE_SQLITE_ERROR;

        schemas.push_back(schema.get());
        }

    if (0 == schemas.size())
        return BE_SQLITE_NOTFOUND;

    SchemaStatus status = dgndb.ImportSchemas(schemas); // NOTE: this calls DgnDb::ImportSchemas which has additional processing over SchemaManager::ImportSchemas
    if (status != SchemaStatus::Success)
        return DgnDb::SchemaStatusToDbResult(status, true);

    return dgndb.SaveChanges();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Chris.Lawson                    05/20
//---------------------------------------------------------------------------------------
DbResult JsInterop::ImportXmlSchemas(DgnDbR dgndb, bvector<Utf8String> const& serializedXmlSchemas)
    {
    if (0 == serializedXmlSchemas.size())
        return BE_SQLITE_NOTFOUND;

    ECSchemaReadContextPtr schemaContext = ECSchemaReadContext::CreateContext(false /*=acceptLegacyImperfectLatestCompatibleMatch*/, true /*=includeFilesWithNoVerExt*/);
    schemaContext->SetFinalSchemaLocater(dgndb.GetSchemaLocater());
    bvector<ECSchemaCP> schemas;

    for (Utf8String schemaXml : serializedXmlSchemas)
        {
        ECSchemaPtr schema;
        SchemaReadStatus schemaStatus = ECSchema::ReadFromXmlString(schema, schemaXml.c_str(), *schemaContext);
        if (SchemaReadStatus::DuplicateSchema == schemaStatus)
            continue;

        if (SchemaReadStatus::Success != schemaStatus)
            return BE_SQLITE_ERROR;

        schemas.push_back(schema.get());
        }

    if (0 == schemas.size())
        return BE_SQLITE_NOTFOUND;

    SchemaStatus status = dgndb.ImportSchemas(schemas); // NOTE: this calls DgnDb::ImportSchemas which has additional processing over SchemaManager::ImportSchemas
    if (status != SchemaStatus::Success)
        return DgnDb::SchemaStatusToDbResult(status, true);

    return dgndb.SaveChanges();
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                   Shaun.Sewall                09/18
//---------------------------------------------------------------------------------------
DbResult JsInterop::ImportFunctionalSchema(DgnDbR db)
    {
    return SchemaStatus::Success == FunctionalDomain::GetDomain().ImportSchema(db) ? BE_SQLITE_OK : BE_SQLITE_ERROR;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
ECClassCP JsInterop::GetClassFromInstance(ECDbCR ecdb, JsonValueCR jsonInstance)
    {
    return ECJsonUtilities::GetClassFromClassNameJson(jsonInstance[ECJsonUtilities::json_className()], ecdb.GetClassLocater());
    }

//---------------------------------------------------------------------------------------
// @bsimethod                               Ramanujam.Raman                 07/17
//---------------------------------------------------------------------------------------
ECInstanceId JsInterop::GetInstanceIdFromInstance(ECDbCR ecdb, JsonValueCR jsonInstance)
    {
    if (!jsonInstance.isMember(ECJsonUtilities::json_id()))
        return ECInstanceId();

    ECInstanceId instanceId;
    if (SUCCESS != ECInstanceId::FromString(instanceId, jsonInstance[ECJsonUtilities::json_id()].asCString()))
        return ECInstanceId();

    return instanceId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Sam.Wilson                      01/18
+---------------+---------------+---------------+---------------+---------------+------*/
void JsInterop::ThrowJsException(Utf8CP msg) { Napi::Error::New(Env(), msg).ThrowAsJavaScriptException(); }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   03/19
+---------------+---------------+---------------+---------------+---------------+------*/
void JsInterop::SetUseTileCache(bool use) { s_useTileCache = use; }
bool JsInterop::GetUseTileCache() { return s_useTileCache; }

//---------------------------------------------------------------------------------------
// @bsimethod                                  Krischan.Eberle                      02/18
//+---------------+---------------+---------------+---------------+---------------+------
HexStrSqlFunction& HexStrSqlFunction::GetSingleton()
    {
    static HexStrSqlFunction* s_singleton = nullptr;
    if (s_singleton == nullptr)
        s_singleton = new HexStrSqlFunction();

    return *s_singleton;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                  Krischan.Eberle                      02/18
//+---------------+---------------+---------------+---------------+---------------+------
void HexStrSqlFunction::_ComputeScalar(Context& ctx, int nArgs, DbValue* args)
    {
    DbValue const& numValue = args[0];
    if (numValue.IsNull())
        {
        ctx.SetResultNull();
        return;
        }

    if (numValue.GetValueType() != DbValueType::IntegerVal)
        {
        ctx.SetResultError("Argument of function HEXSTR is expected to be an integral number.");
        return;
        }

    static const size_t stringBufferLength = 19;
    Utf8Char stringBuffer[stringBufferLength];
    BeStringUtilities::FormatUInt64(stringBuffer, stringBufferLength, numValue.GetValueUInt64(), HexFormatOptions::IncludePrefix);
    ctx.SetResultText(stringBuffer, (int) strlen(stringBuffer), Context::CopyData::Yes);
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                  Krischan.Eberle                      02/18
//+---------------+---------------+---------------+---------------+---------------+------
StrSqlFunction& StrSqlFunction::GetSingleton()
    {
    static StrSqlFunction* s_singleton = nullptr;
    if (s_singleton == nullptr)
        s_singleton = new StrSqlFunction();

    return *s_singleton;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                  Krischan.Eberle                      02/18
//+---------------+---------------+---------------+---------------+---------------+------
void StrSqlFunction::_ComputeScalar(Context& ctx, int nArgs, DbValue* args)
    {
    DbValue const& numValue = args[0];
    if (numValue.IsNull())
        {
        ctx.SetResultNull();
        return;
        }

    if (numValue.GetValueType() != DbValueType::IntegerVal)
        {
        ctx.SetResultError("Argument of function STR is expected to be an integral number.");
        return;
        }

    static const size_t stringBufferLength = std::numeric_limits<uint64_t>::digits + 1; //+1 for the trailing 0 character

    Utf8Char stringBuffer[stringBufferLength]; //+1 for the trailing 0 character;
    BeStringUtilities::FormatUInt64(stringBuffer, numValue.GetValueUInt64());
    ctx.SetResultText(stringBuffer, (int) strlen(stringBuffer), Context::CopyData::Yes);
    }
