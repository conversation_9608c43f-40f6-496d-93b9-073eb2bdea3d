/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See COPYRIGHT.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModelTypes.h"
#include <memory>
#include <functional>

namespace TerrainModel {

//=======================================================================================
// Forward declarations
//=======================================================================================
class DTMTriangle;
class DTMVertex;

//=======================================================================================
// Spatial index interface for efficient spatial queries
//=======================================================================================
class SpatialIndex {
public:
    virtual ~SpatialIndex() = default;
    
    // Triangle operations
    virtual void InsertTriangle(DTMTriangleId triangleId, const Range3D& bounds) = 0;
    virtual void RemoveTriangle(DTMTriangleId triangleId) = 0;
    virtual void UpdateTriangle(DTMTriangleId triangleId, const Range3D& oldBounds, const Range3D& newBounds) = 0;
    
    // Vertex operations
    virtual void InsertVertex(DTMVertexId vertexId, const Point3D& position) = 0;
    virtual void RemoveVertex(DTMVertexId vertexId) = 0;
    virtual void UpdateVertex(DTMVertexId vertexId, const Point3D& oldPosition, const Point3D& newPosition) = 0;
    
    // Query operations
    virtual std::vector<DTMTriangleId> FindTrianglesInRange(const Range3D& range) const = 0;
    virtual std::vector<DTMTriangleId> FindTrianglesContaining(const Point3D& point) const = 0;
    virtual std::vector<DTMVertexId> FindVerticesInRange(const Range3D& range) const = 0;
    virtual std::vector<DTMVertexId> FindNearestVertices(const Point3D& point, double radius, int maxCount = -1) const = 0;
    
    // Utility operations
    virtual void Clear() = 0;
    virtual void Rebuild() = 0;
    virtual size_t GetMemoryUsage() const = 0;
    virtual void GetStatistics(std::map<std::string, double>& stats) const = 0;
    
    // Factory method
    static std::unique_ptr<SpatialIndex> Create(const std::string& type = "rtree");
};

//=======================================================================================
// R-tree based spatial index implementation
//=======================================================================================
class RTreeSpatialIndex : public SpatialIndex {
private:
    struct Node;
    struct Entry;
    
    static constexpr int MIN_ENTRIES = 4;
    static constexpr int MAX_ENTRIES = 8;
    
    std::unique_ptr<Node> m_root;
    size_t m_triangleCount;
    size_t m_vertexCount;
    int m_height;
    
    // Statistics
    mutable size_t m_queryCount;
    mutable size_t m_nodeVisits;

public:
    RTreeSpatialIndex();
    ~RTreeSpatialIndex() override;
    
    // Triangle operations
    void InsertTriangle(DTMTriangleId triangleId, const Range3D& bounds) override;
    void RemoveTriangle(DTMTriangleId triangleId) override;
    void UpdateTriangle(DTMTriangleId triangleId, const Range3D& oldBounds, const Range3D& newBounds) override;
    
    // Vertex operations
    void InsertVertex(DTMVertexId vertexId, const Point3D& position) override;
    void RemoveVertex(DTMVertexId vertexId) override;
    void UpdateVertex(DTMVertexId vertexId, const Point3D& oldPosition, const Point3D& newPosition) override;
    
    // Query operations
    std::vector<DTMTriangleId> FindTrianglesInRange(const Range3D& range) const override;
    std::vector<DTMTriangleId> FindTrianglesContaining(const Point3D& point) const override;
    std::vector<DTMVertexId> FindVerticesInRange(const Range3D& range) const override;
    std::vector<DTMVertexId> FindNearestVertices(const Point3D& point, double radius, int maxCount = -1) const override;
    
    // Utility operations
    void Clear() override;
    void Rebuild() override;
    size_t GetMemoryUsage() const override;
    void GetStatistics(std::map<std::string, double>& stats) const override;

private:
    // Internal R-tree operations
    void Insert(const Entry& entry);
    bool Remove(const Entry& entry);
    void Search(const Range3D& range, std::vector<Entry>& results) const;
    void SearchPoint(const Point3D& point, std::vector<Entry>& results) const;
    void SearchNearest(const Point3D& point, double radius, std::vector<Entry>& results, int maxCount) const;
    
    // Node operations
    std::unique_ptr<Node> ChooseLeaf(const Range3D& bounds);
    void AdjustTree(Node* leaf, std::unique_ptr<Node> newNode);
    std::pair<std::unique_ptr<Node>, std::unique_ptr<Node>> SplitNode(Node* node);
    void CondenseTree(Node* leaf);
    
    // Utility functions
    Range3D CalculateBounds(const Node* node) const;
    double CalculateAreaIncrease(const Range3D& existing, const Range3D& addition) const;
    double CalculateOverlap(const Range3D& bounds, const Node* node) const;
    void UpdateBounds(Node* node);
};

//=======================================================================================
// Grid-based spatial index for uniform data distribution
//=======================================================================================
class GridSpatialIndex : public SpatialIndex {
private:
    struct Cell {
        std::vector<DTMTriangleId> triangles;
        std::vector<DTMVertexId> vertices;
    };
    
    Range3D m_bounds;
    double m_cellSizeX;
    double m_cellSizeY;
    int m_gridWidth;
    int m_gridHeight;
    std::vector<Cell> m_cells;
    
    // Statistics
    mutable size_t m_queryCount;
    mutable size_t m_cellVisits;

public:
    GridSpatialIndex(const Range3D& bounds, double cellSize);
    GridSpatialIndex(const Range3D& bounds, int gridWidth, int gridHeight);
    ~GridSpatialIndex() override = default;
    
    // Triangle operations
    void InsertTriangle(DTMTriangleId triangleId, const Range3D& bounds) override;
    void RemoveTriangle(DTMTriangleId triangleId) override;
    void UpdateTriangle(DTMTriangleId triangleId, const Range3D& oldBounds, const Range3D& newBounds) override;
    
    // Vertex operations
    void InsertVertex(DTMVertexId vertexId, const Point3D& position) override;
    void RemoveVertex(DTMVertexId vertexId) override;
    void UpdateVertex(DTMVertexId vertexId, const Point3D& oldPosition, const Point3D& newPosition) override;
    
    // Query operations
    std::vector<DTMTriangleId> FindTrianglesInRange(const Range3D& range) const override;
    std::vector<DTMTriangleId> FindTrianglesContaining(const Point3D& point) const override;
    std::vector<DTMVertexId> FindVerticesInRange(const Range3D& range) const override;
    std::vector<DTMVertexId> FindNearestVertices(const Point3D& point, double radius, int maxCount = -1) const override;
    
    // Utility operations
    void Clear() override;
    void Rebuild() override;
    size_t GetMemoryUsage() const override;
    void GetStatistics(std::map<std::string, double>& stats) const override;

private:
    // Grid operations
    int GetCellIndex(int x, int y) const;
    std::pair<int, int> GetCellCoordinates(const Point3D& point) const;
    std::vector<int> GetCellsInRange(const Range3D& range) const;
    bool IsValidCell(int x, int y) const;
    
    // Utility functions
    void InsertTriangleInCells(DTMTriangleId triangleId, const Range3D& bounds);
    void RemoveTriangleFromCells(DTMTriangleId triangleId, const Range3D& bounds);
};

//=======================================================================================
// Adaptive spatial index that chooses the best strategy based on data characteristics
//=======================================================================================
class AdaptiveSpatialIndex : public SpatialIndex {
private:
    std::unique_ptr<SpatialIndex> m_activeIndex;
    std::string m_currentStrategy;
    
    // Data characteristics
    size_t m_triangleCount;
    size_t m_vertexCount;
    Range3D m_dataBounds;
    double m_dataUniformity;
    
    // Performance metrics
    mutable size_t m_queryCount;
    mutable double m_averageQueryTime;
    mutable size_t m_rebuildCount;

public:
    AdaptiveSpatialIndex();
    ~AdaptiveSpatialIndex() override = default;
    
    // Triangle operations
    void InsertTriangle(DTMTriangleId triangleId, const Range3D& bounds) override;
    void RemoveTriangle(DTMTriangleId triangleId) override;
    void UpdateTriangle(DTMTriangleId triangleId, const Range3D& oldBounds, const Range3D& newBounds) override;
    
    // Vertex operations
    void InsertVertex(DTMVertexId vertexId, const Point3D& position) override;
    void RemoveVertex(DTMVertexId vertexId) override;
    void UpdateVertex(DTMVertexId vertexId, const Point3D& oldPosition, const Point3D& newPosition) override;
    
    // Query operations
    std::vector<DTMTriangleId> FindTrianglesInRange(const Range3D& range) const override;
    std::vector<DTMTriangleId> FindTrianglesContaining(const Point3D& point) const override;
    std::vector<DTMVertexId> FindVerticesInRange(const Range3D& range) const override;
    std::vector<DTMVertexId> FindNearestVertices(const Point3D& point, double radius, int maxCount = -1) const override;
    
    // Utility operations
    void Clear() override;
    void Rebuild() override;
    size_t GetMemoryUsage() const override;
    void GetStatistics(std::map<std::string, double>& stats) const override;
    
    // Adaptive operations
    void AnalyzeDataCharacteristics();
    void OptimizeStrategy();
    std::string GetCurrentStrategy() const { return m_currentStrategy; }

private:
    // Strategy selection
    std::string SelectOptimalStrategy() const;
    void SwitchStrategy(const std::string& newStrategy);
    
    // Data analysis
    double CalculateDataUniformity() const;
    double EstimateQueryPerformance(const std::string& strategy) const;
    
    // Performance monitoring
    void UpdatePerformanceMetrics(double queryTime) const;
    bool ShouldRebuild() const;
};

//=======================================================================================
// Spatial index factory
//=======================================================================================
class SpatialIndexFactory {
public:
    static std::unique_ptr<SpatialIndex> CreateRTree();
    static std::unique_ptr<SpatialIndex> CreateGrid(const Range3D& bounds, double cellSize);
    static std::unique_ptr<SpatialIndex> CreateGrid(const Range3D& bounds, int gridWidth, int gridHeight);
    static std::unique_ptr<SpatialIndex> CreateAdaptive();
    static std::unique_ptr<SpatialIndex> CreateOptimal(const Range3D& bounds, size_t estimatedTriangles, size_t estimatedVertices);
};

} // namespace TerrainModel
