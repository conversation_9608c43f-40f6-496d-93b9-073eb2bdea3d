# TerrainModel Node.js Addon

A Node.js native addon that provides JavaScript/TypeScript bindings for the Bentley TerrainModelNET library, enabling high-performance terrain modeling and Digital Terrain Model (DTM) operations in Node.js applications.

## Features

- **DTM Creation and Management**: Create, load, save, and manipulate Digital Terrain Models
- **Triangulation**: Advanced Delaunay triangulation with configurable parameters
- **Feature Management**: Add, modify, and delete terrain features (points, breaklines, voids, etc.)
- **Draping Operations**: Project points onto terrain surfaces
- **Analysis Tools**: Calculate volumes, cut/fill, slope areas, and statistics
- **File I/O**: Support for various terrain file formats including GEOPAK
- **Memory Efficient**: Optimized memory management with automatic cleanup
- **TypeScript Support**: Full TypeScript definitions included

## Installation

### Prerequisites

- Node.js 16.0.0 or higher
- Bentley TerrainModel SDK
- Visual Studio 2019+ (Windows) or appropriate C++ compiler
- Python 3.x (for node-gyp)

### Environment Setup

1. Set the `BENTLEY_SDK_ROOT` environment variable to your Bentley SDK installation path:
   ```bash
   # Windows
   set BENTLEY_SDK_ROOT=C:\BentleySDK\

   # Linux/macOS
   export BENTLEY_SDK_ROOT=/opt/bentley-sdk/
   ```

2. Ensure the Bentley libraries are in your system PATH or LD_LIBRARY_PATH.

### Install from npm

```bash
npm install terrain-model-node-addon
```

### Build from Source

```bash
git clone https://github.com/bentley/terrain-model-node-addon.git
cd terrain-model-node-addon
npm install
npm run build
```

## Quick Start

```javascript
const TerrainModel = require('terrain-model-node-addon');

// Create a new DTM
const dtm = TerrainModel.createDTM();

// Add some points
const points = [
  { x: 0, y: 0, z: 100 },
  { x: 100, y: 0, z: 105 },
  { x: 50, y: 100, z: 110 }
];

points.forEach(point => {
  dtm.addPointFeature(point);
});

// Triangulate
const result = dtm.triangulate();
if (result.success) {
  console.log(`Triangulated ${result.verticesCount} vertices into ${result.trianglesCount} triangles`);
}

// Drape a point onto the surface
const drapedPoint = dtm.drapePoint({ x: 25, y: 25, z: 0 });
console.log(`Draped elevation: ${drapedPoint.drapedPoint.z}`);

// Save the DTM
dtm.save('my_terrain.tin');

// Clean up
dtm.dispose();
```

## API Reference

### Classes

#### DTM
The main Digital Terrain Model class.

```javascript
// Constructors
const dtm = new TerrainModel.DTM();
const dtm = new TerrainModel.DTM(initialPoints, incrementPoints);

// Static factory methods
const dtm = TerrainModel.DTM.createFromFile('terrain.tin');
const dtm = TerrainModel.DTM.createFromGeopakTinFile('terrain.tin');
```

**Key Methods:**
- `triangulate()`: Perform Delaunay triangulation
- `addPointFeature(point, userTag?)`: Add a point feature
- `addLinearFeature(points, featureType, userTag?)`: Add linear features
- `drapePoint(point)`: Project a point onto the terrain
- `calculateSlopeArea()`: Calculate slope and area statistics
- `save(fileName)`: Save DTM to file

#### DTMFeature
Represents a terrain feature (point, line, etc.).

```javascript
const feature = dtm.getFeatureById(featureId);
console.log(feature.featureType, feature.points);
```

#### DTMFeatureEnumerator
Iterate through DTM features.

```javascript
const enumerator = new TerrainModel.DTMFeatureEnumerator(dtm);
enumerator.includeAllFeatures();
while (enumerator.moveNext()) {
  const feature = enumerator.current();
  console.log(feature.featureId, feature.featureType);
}
```

### Enums

#### DTMFeatureType
- `Point`: Point features
- `BreakLine`: Breakline features
- `VoidLine`: Void boundary lines
- `HoleLine`: Hole boundary lines
- `IslandLine`: Island boundary lines
- `ContourLine`: Contour lines

#### DTMDrapedPointCode
- `External`: Point is outside the triangulation
- `Triangle`: Point is inside a triangle
- `Void`: Point is in a void area
- `PointOrSide`: Point coincides with vertex or edge

### Types

#### Point3D
```typescript
interface Point3D {
  x: number;
  y: number;
  z: number;
}
```

#### Range3D
```typescript
interface Range3D {
  low: Point3D;
  high: Point3D;
}
```

## Advanced Usage

### Async Operations

```javascript
// Use async versions for better performance
const result = await dtm.triangulateAsync();
await dtm.saveAsync('terrain.tin');
```

### Error Handling

```javascript
try {
  const dtm = TerrainModel.DTM.createFromFile('nonexistent.tin');
} catch (error) {
  if (error instanceof TerrainModel.TerrainModelError) {
    console.error('TerrainModel Error:', error.message, error.code);
  }
}
```

### Memory Management

```javascript
// Always dispose of DTM objects when done
dtm.dispose();

// Or use try/finally
const dtm = TerrainModel.createDTM();
try {
  // ... use dtm
} finally {
  dtm.dispose();
}
```

### Feature Management

```javascript
// Add different types of features
const pointId = dtm.addPointFeature({ x: 0, y: 0, z: 100 }, 1);
const breaklineId = dtm.addLinearFeature(
  [{ x: 0, y: 0, z: 100 }, { x: 100, y: 0, z: 105 }],
  TerrainModel.DTMFeatureType.BreakLine,
  2
);

// Delete features
dtm.deleteFeatureById(pointId);
dtm.deleteFeaturesByType(TerrainModel.DTMFeatureType.ContourLine);
```

## Performance Tips

1. **Batch Operations**: Add multiple points at once using `addPoints()`
2. **Memory Management**: Always call `dispose()` on DTM objects
3. **Triangulation Parameters**: Tune tolerances for your data precision
4. **Feature Types**: Use appropriate feature types for better triangulation

## Troubleshooting

### Common Issues

1. **Module Load Error**: Ensure Bentley SDK is properly installed and in PATH
2. **Triangulation Fails**: Check point tolerances and data quality
3. **Memory Issues**: Dispose of DTM objects and avoid memory leaks

### Debug Build

```bash
npm run build:debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- GitHub Issues: https://github.com/bentley/terrain-model-node-addon/issues
- Documentation: https://github.com/bentley/terrain-model-node-addon/wiki
