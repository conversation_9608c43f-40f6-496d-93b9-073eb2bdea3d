cmake_minimum_required(VERSION 3.16)
project(TerrainModelNodeAddon VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build options
option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(BUILD_TESTS "Build test suite" OFF)
option(BUILD_EXAMPLES "Build example applications" OFF)
option(ENABLE_OPENMP "Enable OpenMP support" ON)

# Find required packages
find_package(PkgConfig REQUIRED)

# Set Bentley SDK path
if(NOT DEFINED BENTLEY_SDK_ROOT)
    if(DEFINED ENV{BENTLEY_SDK_ROOT})
        set(BENTLEY_SDK_ROOT $ENV{BENTLEY_SDK_ROOT})
    else()
        message(FATAL_ERROR "BENTLEY_SDK_ROOT environment variable not set")
    endif()
endif()

# Node.js addon configuration
execute_process(
    COMMAND node -p "require('node-addon-api').include"
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE NODE_ADDON_API_DIR
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

# Remove quotes from the output
string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})

# Define source files
set(SOURCES
    TerrainModelNodeAddon.cpp
    NativeDTM.cpp
    NativeDTMFeature.cpp
    NativeDTMFeatureEnumerator.cpp
    NativeDTMMesh.cpp
    NativeDTMMeshEnumerator.cpp
    TerrainModelUtils.cpp
)

# Define header files
set(HEADERS
    TerrainModelNodeAddon.h
    NativeDTM.h
    NativeDTMFeature.h
    NativeDTMFeatureEnumerator.h
    NativeDTMMesh.h
    NativeDTMMeshEnumerator.h
    TerrainModelUtils.h
)

# Create the addon library
add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})

# Set library properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    PREFIX ""
    SUFFIX ".node"
    CXX_VISIBILITY_PRESET hidden
)

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE
    ${NODE_ADDON_API_DIR}
    ${CMAKE_SOURCE_DIR}/../TerrainModelNET
    ${CMAKE_SOURCE_DIR}/../iModelJsNodeAddon
    ${BENTLEY_SDK_ROOT}/include
    ${BENTLEY_SDK_ROOT}/include/TerrainModel
    ${BENTLEY_SDK_ROOT}/include/Bentley
    ${BENTLEY_SDK_ROOT}/include/BentleyGeom
)

# Compile definitions
target_compile_definitions(${PROJECT_NAME} PRIVATE
    NAPI_DISABLE_CPP_EXCEPTIONS
    BUILDING_NODE_EXTENSION
    USING_NAMESPACE_BENTLEY_TERRAINMODEL
)

# Platform-specific configurations
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0601
    )
    
    # Link libraries for Windows
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ${BENTLEY_SDK_ROOT}/lib/TerrainModel.lib
        ${BENTLEY_SDK_ROOT}/lib/BentleyGeom.lib
        ${BENTLEY_SDK_ROOT}/lib/BentleyAllocator.lib
    )
    
    # MSVC specific settings
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE
            /std:c++17
            /EHsc
        )
        set_target_properties(${PROJECT_NAME} PROPERTIES
            MSVC_RUNTIME_LIBRARY "MultiThreadedDLL"
        )
    endif()
    
elseif(APPLE)
    target_compile_options(${PROJECT_NAME} PRIVATE
        -std=c++17
        -stdlib=libc++
    )
    
    target_link_options(${PROJECT_NAME} PRIVATE
        -stdlib=libc++
    )
    
    # Link libraries for macOS
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ${BENTLEY_SDK_ROOT}/lib/libTerrainModel.dylib
        ${BENTLEY_SDK_ROOT}/lib/libBentleyGeom.dylib
        ${BENTLEY_SDK_ROOT}/lib/libBentleyAllocator.dylib
    )
    
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_DEPLOYMENT_TARGET "10.15"
    )
    
elseif(UNIX)
    target_compile_options(${PROJECT_NAME} PRIVATE
        -std=c++17
        -fPIC
    )
    
    # Link libraries for Linux
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ${BENTLEY_SDK_ROOT}/lib/libTerrainModel.so
        ${BENTLEY_SDK_ROOT}/lib/libBentleyGeom.so
        ${BENTLEY_SDK_ROOT}/lib/libBentleyAllocator.so
    )
endif()

# Disable exceptions for N-API compatibility
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-fno-exceptions>
    $<$<CXX_COMPILER_ID:Clang>:-fno-exceptions>
)

# Add custom target for cleaning
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}
    COMMENT "Cleaning all build files"
)

# Installation rules
install(TARGETS ${PROJECT_NAME}
    LIBRARY DESTINATION .
    RUNTIME DESTINATION .
)

# Copy required DLLs on Windows
if(WIN32)
    install(FILES
        ${BENTLEY_SDK_ROOT}/bin/TerrainModel.dll
        ${BENTLEY_SDK_ROOT}/bin/BentleyGeom.dll
        ${BENTLEY_SDK_ROOT}/bin/BentleyAllocator.dll
        DESTINATION .
        OPTIONAL
    )
endif()

# Development helpers
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG=1)
    
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /Od /Zi)
        target_link_options(${PROJECT_NAME} PRIVATE /DEBUG)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -g -O0)
    endif()
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG=1)
    
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /O2)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -O3)
    endif()
endif()

# Print configuration summary
message(STATUS "TerrainModel Node.js Addon Configuration:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Bentley SDK Root: ${BENTLEY_SDK_ROOT}")
message(STATUS "  Node Addon API Dir: ${NODE_ADDON_API_DIR}")
message(STATUS "  Target Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
