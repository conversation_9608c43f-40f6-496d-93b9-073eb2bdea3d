cmake_minimum_required(VERSION 3.16)
project(TerrainModelNodeAddon VERSION 1.0.0)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build options
option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(BUILD_TESTS "Build test suite" OFF)
option(BUILD_EXAMPLES "Build example applications" OFF)
option(ENABLE_OPENMP "Enable OpenMP support" ON)

# Find required packages
find_package(PkgConfig REQUIRED)

# Set Bentley SDK path
if(NOT DEFINED BENTLEY_SDK_ROOT)
    if(DEFINED ENV{BENTLEY_SDK_ROOT})
        set(BENTLEY_SDK_ROOT $ENV{BENTLEY_SDK_ROOT})
    else()
        message(FATAL_ERROR "BENTLEY_SDK_ROOT environment variable not set")
    endif()
endif()

# Node.js addon configuration
execute_process(
    COMMAND node -p "require('node-addon-api').include"
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE NODE_ADDON_API_DIR
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

# Remove quotes from the output
string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})

# Define TerrainModel Core library sources
set(TERRAIN_MODEL_CORE_SOURCES
    TerrainModelCore/DTM.cpp
    TerrainModelCore/DTMFeature.cpp
    TerrainModelCore/DTMTriangle.cpp
    TerrainModelCore/SpatialIndex.cpp
    TerrainModelCore/GeometryUtils.cpp
    TerrainModelCore/TriangulationEngine.cpp
)

# Add source files that exist
set(EXISTING_CORE_SOURCES)
foreach(SOURCE_FILE ${TERRAIN_MODEL_CORE_SOURCES})
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${SOURCE_FILE}")
        list(APPEND EXISTING_CORE_SOURCES ${SOURCE_FILE})
    else()
        message(STATUS "Source file not found: ${SOURCE_FILE}")
    endif()
endforeach()

set(TERRAIN_MODEL_CORE_SOURCES ${EXISTING_CORE_SOURCES})

set(TERRAIN_MODEL_CORE_HEADERS
    TerrainModelCore/TerrainModelTypes.h
    TerrainModelCore/DTM.h
    TerrainModelCore/DTMFeature.h
    TerrainModelCore/DTMTriangle.h
    TerrainModelCore/DTMTinEditor.h
    TerrainModelCore/WaterAnalysis.h
    TerrainModelCore/DTMPond.h
    TerrainModelCore/SpatialIndex.h
    TerrainModelCore/GeometryUtils.h
    TerrainModelCore/TriangulationEngine.h
    TerrainModelCore/FileIO.h
)

# Define Node.js addon source files
set(NODE_ADDON_SOURCES
    TerrainModelNodeAddon.cpp
    NativeDTM.cpp
    NativeDTMFeature.cpp
    NativeDTMFeatureEnumerator.cpp
    NativeDTMMesh.cpp
    NativeDTMMeshEnumerator.cpp
    NativeDTMTinEditor.cpp
    NativeDTMDrapedLinearElement.cpp
    NativeWaterAnalysis.cpp
    NativeDTMPond.cpp
    NativeDTMSideSlopeInput.cpp
    NativeDTMCaching.cpp
    TerrainModelUtils.cpp
)

# Define Node.js addon header files
set(NODE_ADDON_HEADERS
    TerrainModelNodeAddon.h
    NativeDTM.h
    NativeDTMFeature.h
    NativeDTMFeatureEnumerator.h
    NativeDTMMesh.h
    NativeDTMMeshEnumerator.h
    NativeDTMTinEditor.h
    NativeDTMDrapedLinearElement.h
    NativeWaterAnalysis.h
    NativeDTMPond.h
    NativeDTMSideSlopeInput.h
    NativeDTMCaching.h
    TerrainModelUtils.h
)

# Create TerrainModel Core library
add_library(TerrainModelCore STATIC ${TERRAIN_MODEL_CORE_SOURCES} ${TERRAIN_MODEL_CORE_HEADERS})

# Set library properties
set_target_properties(TerrainModelCore PROPERTIES
    CXX_VISIBILITY_PRESET hidden
    POSITION_INDEPENDENT_CODE ON
)

# Include directories for TerrainModel Core
target_include_directories(TerrainModelCore PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

# Create the Node.js addon library
add_library(${PROJECT_NAME} SHARED ${NODE_ADDON_SOURCES} ${NODE_ADDON_HEADERS})

# Set library properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    PREFIX ""
    SUFFIX ".node"
    CXX_VISIBILITY_PRESET hidden
)

# Include directories for Node.js addon
target_include_directories(${PROJECT_NAME} PRIVATE
    ${NODE_ADDON_API_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link TerrainModel Core library
target_link_libraries(${PROJECT_NAME} PRIVATE TerrainModelCore)

# Compile definitions for Node.js addon
target_compile_definitions(${PROJECT_NAME} PRIVATE
    NAPI_DISABLE_CPP_EXCEPTIONS
    BUILDING_NODE_EXTENSION
)

# Platform-specific configurations
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0601
    )
    
    # Windows-specific libraries (if needed)
    # target_link_libraries(${PROJECT_NAME} PRIVATE user32 kernel32)
    
    # MSVC specific settings
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE
            /std:c++17
            /EHsc
        )
        set_target_properties(${PROJECT_NAME} PROPERTIES
            MSVC_RUNTIME_LIBRARY "MultiThreadedDLL"
        )
    endif()
    
elseif(APPLE)
    target_compile_options(${PROJECT_NAME} PRIVATE
        -std=c++17
        -stdlib=libc++
    )
    
    target_link_options(${PROJECT_NAME} PRIVATE
        -stdlib=libc++
    )
    
    # macOS-specific libraries (if needed)
    # target_link_libraries(${PROJECT_NAME} PRIVATE "-framework CoreFoundation")
    
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_DEPLOYMENT_TARGET "10.15"
    )
    
elseif(UNIX)
    target_compile_options(${PROJECT_NAME} PRIVATE
        -std=c++17
        -fPIC
    )
    
    # Linux-specific libraries (if needed)
    # target_link_libraries(${PROJECT_NAME} PRIVATE pthread dl)
endif()

# Disable exceptions for N-API compatibility
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-fno-exceptions>
    $<$<CXX_COMPILER_ID:Clang>:-fno-exceptions>
)

# Add custom target for cleaning
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}
    COMMENT "Cleaning all build files"
)

# Installation rules
install(TARGETS ${PROJECT_NAME}
    LIBRARY DESTINATION .
    RUNTIME DESTINATION .
)

# Copy required DLLs on Windows
if(WIN32)
    install(FILES
        ${BENTLEY_SDK_ROOT}/bin/TerrainModel.dll
        ${BENTLEY_SDK_ROOT}/bin/BentleyGeom.dll
        ${BENTLEY_SDK_ROOT}/bin/BentleyAllocator.dll
        DESTINATION .
        OPTIONAL
    )
endif()

# Development helpers
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG=1)
    
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /Od /Zi)
        target_link_options(${PROJECT_NAME} PRIVATE /DEBUG)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -g -O0)
    endif()
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG=1)
    
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /O2)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -O3)
    endif()
endif()

# Print configuration summary
message(STATUS "TerrainModel Node.js Addon Configuration:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Bentley SDK Root: ${BENTLEY_SDK_ROOT}")
message(STATUS "  Node Addon API Dir: ${NODE_ADDON_API_DIR}")
message(STATUS "  Target Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
