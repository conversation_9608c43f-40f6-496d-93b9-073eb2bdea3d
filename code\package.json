{"name": "terrain-model-node-addon", "version": "1.0.0", "description": "Node.js addon for Bentley TerrainModel library", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "node scripts/build.js", "build:debug": "node scripts/build.js --debug", "build:release": "node scripts/build.js --release", "build:clean": "node scripts/build.js --clean", "build:full": "node scripts/build.js --clean --test --package", "rebuild": "node-gyp rebuild", "configure": "node-gyp configure", "clean": "node-gyp clean", "test": "node test/unit-tests.js", "test:unit": "node test/unit-tests.js", "test:benchmark": "node test/benchmark.js", "test:comprehensive": "node test/comprehensive-test.js", "test:all": "npm run test:unit && npm run test:comprehensive && npm run test:benchmark", "example": "node example.js", "install": "node scripts/build.js", "prepack": "npm run build:full", "docs": "typedoc --out docs index.d.ts", "lint": "eslint *.js test/*.js scripts/*.js", "format": "prettier --write *.js test/*.js scripts/*.js"}, "keywords": ["terrain", "dtm", "triangulation", "<PERSON><PERSON>", "native", "addon", "node-api", "napi"], "author": "Bentley Systems", "license": "MIT", "dependencies": {"node-addon-api": "^7.0.0"}, "devDependencies": {"node-gyp": "^10.0.0", "@types/node": "^20.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typedoc": "^0.25.0", "mocha": "^10.0.0", "chai": "^4.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/bentley/terrain-model-node-addon.git"}, "bugs": {"url": "https://github.com/bentley/terrain-model-node-addon/issues"}, "homepage": "https://github.com/bentley/terrain-model-node-addon#readme", "gypfile": true, "binary": {"module_name": "TerrainModelNodeAddon", "module_path": "./build/Release/", "host": "https://github.com/bentley/terrain-model-node-addon/releases/download/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz"}}