{"name": "terrain-model-node-addon", "version": "1.0.0", "description": "Node.js addon for Bentley TerrainModel library", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "node-gyp rebuild", "build:debug": "node-gyp rebuild --debug", "clean": "node-gyp clean", "configure": "node-gyp configure", "test": "node test/test.js", "install": "node-gyp rebuild", "prepack": "npm run build"}, "keywords": ["terrain", "dtm", "triangulation", "<PERSON><PERSON>", "native", "addon", "node-api", "napi"], "author": "Bentley Systems", "license": "MIT", "dependencies": {"node-addon-api": "^7.0.0"}, "devDependencies": {"node-gyp": "^10.0.0", "@types/node": "^20.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/bentley/terrain-model-node-addon.git"}, "bugs": {"url": "https://github.com/bentley/terrain-model-node-addon/issues"}, "homepage": "https://github.com/bentley/terrain-model-node-addon#readme", "gypfile": true, "binary": {"module_name": "TerrainModelNodeAddon", "module_path": "./build/Release/", "host": "https://github.com/bentley/terrain-model-node-addon/releases/download/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz"}}