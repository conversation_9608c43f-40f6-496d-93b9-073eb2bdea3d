﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ECPresentationUtils.h" />
    <ClInclude Include="ECSchemaXmlContextUtils.h" />
    <ClInclude Include="IModelJsNative.h" />
    <ClInclude Include="suppress_warnings.h" />
    <ClInclude Include="UlasClient.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ECPresentationUtils.cpp" />
    <ClCompile Include="ECSchemaXmlContextUtils.cpp" />
    <ClCompile Include="IModelJsNative.cpp" />
    <ClCompile Include="JsInterop.cpp" />
    <ClCompile Include="JsInteropBriefcaseManager.cpp" />
    <ClCompile Include="JsInteropDgnDb.cpp" />
    <ClCompile Include="JsInteropTiles.cpp" />
    <ClCompile Include="TestUtils.cpp" />
    <ClCompile Include="UlasClient.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="imodeljs-nodeaddonapi.package.version.h.template" />
    <None Include="IModelJsNative.mke" />
    <None Include="IModelJsNative_input_libs.mki" />
    <None Include="installnativeplatform.bat" />
    <None Include="installnativeplatformLinux.sh" />
    <None Include="installnativeplatformMacOS.sh" />
    <None Include="makePackages.mke" />
    <None Include="makePackages.py" />
    <None Include="makePackgeVersionHeaderFile.py" />
    <None Include="package.json.template" />
    <None Include="README-Private.md" />
    <None Include="README-Public.md" />
  </ItemGroup>
  <ItemGroup>
    <Xml Include="iModelJsNodeAddon.PartFile.xml" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="package_version.txt" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3DDE7AD3-D599-4F9A-B155-97FBC3BDEEBD}</ProjectGuid>
    <Keyword>MakeFileProj</Keyword>
    <WindowsTargetPlatformVersion>10.0.17134.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <NMakeOutput>iModelJsNodeAddon.exe</NMakeOutput>
    <NMakePreprocessorDefinitions>WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\;$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <NMakeOutput>iModelJsNodeAddon.exe</NMakeOutput>
    <NMakePreprocessorDefinitions>WIN32;NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\;$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <NMakeOutput>iModelJsNodeAddon.exe</NMakeOutput>
    <NMakePreprocessorDefinitions>WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\;$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <NMakeOutput>iModelJsNodeAddon.exe</NMakeOutput>
    <NMakePreprocessorDefinitions>WIN32;NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\;$(NMakeIncludeSearchPath)</NMakeIncludeSearchPath>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(ProjectDir);$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\VendorAPI\;$(OutRoot)Winx64\BuildContexts\iModelJsNodeAddon\PublicAPI\</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>